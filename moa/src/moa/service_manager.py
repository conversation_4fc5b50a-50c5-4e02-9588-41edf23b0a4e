import argparse

from typing import Optional
from pydantic import BaseModel
from fast_service import FastServiceManager

from .moa_config import MoAConfig

MoA_SERVICE_MANAGER = FastServiceManager()

_MoA_CONFIG: Optional[MoAConfig] = None

_API_KEY: Optional[str] = None

AGGREGATOR_SYSTEM_PROMPT = """You have been provided with a set of responses from various open-source models to the latest user query. Your task is to synthesize these responses into a single, high-quality response. It is crucial to critically evaluate the information provided in these responses, recognizing that some of it may be biased or incorrect. Your response should not simply replicate the given answers but should offer a refined, accurate, and comprehensive reply to the instruction. Ensure your response is well-structured, coherent, and adheres to the highest standards of accuracy and reliability.

Responses from models:"""


def init(cfg_file):
    global _MoA_CONFIG, _API_KEY

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "-k",
        "--api_key",
        default=True,
    )
    _API_KEY = parser.parse_args().api_key

    _MoA_CONFIG = MoAConfig.load_from_file(cfg_file)


def get_moa_config() -> MoAConfig:
    return _MoA_CONFIG


def get_api_key() -> str:
    return _API_KEY


def _get_final_system_prompt(references: list[str]) -> str:
    return (
        AGGREGATOR_SYSTEM_PROMPT
        + "\n"
        + "\n".join([f"{i + 1}. {element}" for i, element in enumerate(references)])
    )


class MoAInput(BaseModel):
    raw_question: str
    references: list[str] | None

    def to_messages(self):
        messages = (
            [
                {
                    "role": "system",
                    "content": _get_final_system_prompt(self.references),
                },
                {"role": "user", "content": self.raw_question},
            ]
            if self.references
            else [{"role": "user", "content": self.raw_question}]
        )
        return messages


class MoAOutput(BaseModel):
    answer: str
