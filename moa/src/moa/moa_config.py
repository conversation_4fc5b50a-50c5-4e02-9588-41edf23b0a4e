import yaml


def _read_yaml(path: str) -> dict:
    result = None
    with open(path, "r", encoding="utf-8") as f:
        result = yaml.load(f.read(), Loader=yaml.FullLoader)
    if result is None:
        raise IOError(f"Fail to read yaml file {path}")
    if not isinstance(result, dict):
        raise SyntaxError(f"Only support dict style yaml file.")
    return result


class ModelConfig:
    name: str
    ip: str
    port: int
    model_name: str

    def __init__(self, name: str, ip: str, port: int, model_name: str):
        self.name = name
        self.ip = ip
        self.port = port
        self.model_name = model_name


class LayerConfig:
    index: int
    proposer_list: list[ModelConfig]

    def __init__(self, index: int, proposer_list: list[dict]):
        self.index = index
        self.proposer_list = [ModelConfig(**_) for _ in proposer_list]


class MoAConfig:
    layer_list: list[LayerConfig]
    aggregator: ModelConfig

    def __init__(self, layer_list: list[dict], aggregator: dict):
        self.layer_list = [LayerConfig(**_) for _ in layer_list]
        self.aggregator = ModelConfig(**aggregator)

    @classmethod
    def load_from_file(cls, path: str):
        config_dict = _read_yaml(path)

        return MoAConfig(**config_dict)
