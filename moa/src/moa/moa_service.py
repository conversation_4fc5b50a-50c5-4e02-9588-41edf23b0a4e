import asyncio
import nest_asyncio

from openai import OpenAI
from fast_service import RequestContext

from .service_manager import (
    MoA_SERVICE_MANAGER,
    MoAInput,
    MoAOutput,
    get_moa_config,
    get_api_key,
)
from .moa_config import MoAConfig, LayerConfig, ModelConfig


_MoA_CONFIG: MoAConfig


def init():
    global _MoA_CONFIG
    _MoA_CONFIG = get_moa_config()
    nest_asyncio.apply()


@MoA_SERVICE_MANAGER.fast_service
def llm_chat(
    layer_input: MoAInput, proposer: ModelConfig, context: RequestContext
) -> str:
    client = OpenAI(
        api_key=get_api_key(), base_url=f"http://{proposer.ip}:{proposer.port}/v1"
    )
    resp = client.chat.completions.create(
        model=proposer.model_name,
        messages=layer_input.to_messages(),
        temperature=0.7,
        max_tokens=512,
    )
    return resp.choices[0].message.content


async def async_llm_chat(
    layer_input: MoAInput, proposer: ModelConfig, context: RequestContext
) -> str:
    return llm_chat(layer_input, proposer, context=context)


async def gather_layer_resp(
    layer_input: MoAInput, layer: LayerConfig, context: RequestContext
):
    return await asyncio.gather(
        *[
            async_llm_chat(
                layer_input, proposer, context=RequestContext(context.request_id)
            )
            for proposer in layer.proposer_list
        ]
    )


def async_run(task):
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    return loop.run_until_complete(task)


@MoA_SERVICE_MANAGER.fast_service
def moa_chat(req: MoAInput, context: RequestContext = None) -> MoAOutput:
    layer_input = req

    for layer in _MoA_CONFIG.layer_list:
        layer_resp = async_run(
            gather_layer_resp(
                layer_input, layer, context=RequestContext(context.request_id)
            )
        )
        layer_input = MoAInput(
            raw_question=layer_input.raw_question,
            references=[str(proposer_resp) for proposer_resp in layer_resp],
        )

    resp = async_run(
        async_llm_chat(
            layer_input,
            _MoA_CONFIG.aggregator,
            context=RequestContext(context.request_id),
        )
    )

    return MoAOutput(answer=resp)
