FROM python:3.12
LABEL authors="<PERSON>"

ENTRYPOINT ["top", "-b"]

# download and install fast-service
COPY .cache/fast-service /workspace/fast-service
RUN cd /workspace/fast-service && \
    pip install -r requirements.txt && \
    pip install -e .

# install dependencies for moa
WORKDIR /workspace/moa
COPY requirements.txt requirements.txt
RUN pip install -r requirements.txt

# copy necessary code to docker image
COPY src src
COPY test test
COPY scripts scripts
COPY config config
COPY benchmark benchmark

# set workdir and default command
WORKDIR /workspace/moa/benchmark
RUN python prepare_data.py
ENTRYPOINT ["python", "load_gen.py"]

CMD ["--help"]