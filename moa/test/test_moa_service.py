import os

import sys

abs_path = os.path.abspath("../src/moa")

module_dir = os.path.dirname(abs_path)
if module_dir not in sys.path:
    sys.path.append(module_dir)

from moa import MoA_SERVICE_MANAGER, moa_chat, MoAInput, MoAOutput

from fast_service import FastServiceConfig

if __name__ == "__main__":
    config = FastServiceConfig.load_from_file(
        os.path.abspath("../config/moa_client.yml")
    )
    MoA_SERVICE_MANAGER.setup_client_mode(config)

    while True:
        resp = moa_chat(
            MoAInput(raw_question="What are 3 fun things to do in SF?", references=None)
        )
        print(resp)
