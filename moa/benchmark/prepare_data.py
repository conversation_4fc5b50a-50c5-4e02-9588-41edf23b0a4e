import os
import datasets
import argparse

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("-d", "--dir", default="./.cache")
    args = parser.parse_args()

    dir_path = args.dir

    raw_dataset = datasets.load_dataset("tatsu-lab/alpaca")["train"]["instruction"]

    if not os.path.exists(dir_path):
        os.mkdir(dir_path)

    file_path = os.path.join(dir_path, "moa_data.txt")

    if not os.path.exists(file_path):
        with open(file_path, "w", encoding="utf-8") as file:
            for instruction in raw_dataset:
                if "\n" in instruction:
                    continue
                file.write(instruction)
                file.write("\n")
    else:
        print(f"{file_path} already exists.")
