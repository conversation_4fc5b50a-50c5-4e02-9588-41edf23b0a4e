import os
import sys
import argparse

from fast_service import (
    FastServiceConfig,
    FastServiceManager,
    FastServiceBenchmark,
    FastServiceBenchmarkConfig,
)

abs_path = os.path.abspath("../src/moa")

module_dir = os.path.dirname(abs_path)
if module_dir not in sys.path:
    sys.path.append(module_dir)

from moa import MoA_SERVICE_MANAGER, moa_chat, MoAInput, MoAOutput


def line_to_req(line: str):
    return MoAInput(raw_question=line.strip(), references=None)


def invoke_service(request):
    moa_chat(request)


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("-f", "--file_path", default="./.cache/moa_data.txt")
    parser.add_argument("-cf", "--config_file", default="../config/moa_client.yml")
    parser.add_argument("-m", "--mode", default="one-by-one")
    parser.add_argument("-n", "--request_num", default=100, type=int)
    parser.add_argument("-l", "--lambda_rate", default=1.0, type=float)
    args = parser.parse_args()

    config_file = args.config_file

    config = FastServiceConfig.load_from_file(os.path.abspath(config_file))
    MoA_SERVICE_MANAGER.setup_client_mode(config)

    bm_config = FastServiceBenchmarkConfig(
        file_path=args.file_path,
        mode=args.mode,
        request_num=args.request_num,
    )
    benchmark = FastServiceBenchmark(
        config=bm_config, line_to_request=line_to_req, invoke_service=invoke_service
    )

    print("start benchmark")
    benchmark.execute()
    print("end benchmark")
