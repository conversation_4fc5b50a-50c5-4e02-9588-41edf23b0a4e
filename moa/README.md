# Mixture of Agents (MoA)

This is an implementation of [MoA](https://arxiv.org/abs/2406.04692).

## Services

### moa_chat

The agent will invoke LLMs according to the configured MoA structure to generate the final response.

## How to run

[Deploy LLMs first](../tools/llm) and update [moa_config.yml](config/moa_config.yml)

In ```ai-agent-benchmark/moa/scripts``` dir, execute the following command.

```shell
python lauch_service.py -k [your hf-access-key]
```

## How to deploy (as a container)

In ```ai-agent-benchmark/moa``` dir, do the following steps.

### 1. Build an image

Make sure you copy the project [fast-service](https://github.com/hkust-adsl/fast-service) into dir ```./.cache```. The image will install fast-service first.

Run the following command.

```shell
docker build -t moa:v1 .
```

Instead, you can also run the script [build_docker_image.sh](scripts/build_docker_image.sh)

### 2. Start a container

Run the following command.

```shell
docker run \
  -d --rm \
  --gpus all \
  --name moa-service \
  -p 20101:20100 \
  -v ./config/moa_config.yml:/workspace/moa/config/moa_config.yml \
  -v ./config/moa_service.yml:/workspace/moa/config/moa_service.yml \
  moa:v1 \
  --api_key [hf_access_token]
```

Instead, you can also run the script [docker_run_moa_service.sh](scripts/docker_run_moa_service.sh).

## How to test

Run the service first.

In ```ai-agent-benchmark/moa/test``` dir, execute the following command.

```shell
python test_moa_service.py
```

## How to benchmark

### Local Run

In dir ```ai-agent-benchmark/moa/benchmark```

1. prepare data via [prepare_data](benchmark/prepare_data.py):

```shell
python prepare_data.py
```

2. run [load_gen.py](benchmark/load_gen.py)

```shell
python load_gen.py -m one-by-one -f ./.cache./moa_data.txt -n 100 -cf ../config/moa_client.yml
```

### Deploy as Docker Container

In ```ai-agent-benchmark/moa``` dir.

1. build docker image

```shell
docker build -f Dockerfile-client -t moa-client:v1
```

2. start a container to run

```shell
docker run \
  -d \
  --name moa-benchmark \
  -v ./config/moa_client.yml:/workspace/moa/config/moa_client.yml \
  -v ./.cache/benchmark:/workspace/moa/benchmark/.cache \
  -v ./benchmark/.cache/moa_data.txt:/workspace/moa/benchmark/.cache/moa_data.txt \
  moa-client:v1 \
  -m one-by-one
```

Instead, you can run [moa_benchmark.sh](scripts/moa_benchmark.sh).