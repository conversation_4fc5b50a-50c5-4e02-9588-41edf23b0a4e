import os
import sys

abs_path = os.path.abspath("../src/moa")

module_dir = os.path.dirname(abs_path)
if module_dir not in sys.path:
    sys.path.append(module_dir)

from moa import MoA_SERVICE_MANAGER, init_moa
from fast_service import FastServiceConfig

if __name__ == "__main__":
    init_moa(os.path.abspath("../config/moa_config.yml"))
    config = FastServiceConfig.load_from_file(
        os.path.abspath("../config/moa_service.yml")
    )
    MoA_SERVICE_MANAGER.execute(config)
