import os
import sys
import argparse

from fast_service import (
    FastServiceConfig,
    FastServiceBenchmark,
    FastServiceBenchmarkConfig,
)

abs_path = os.path.abspath("../src/naive_rag")

module_dir = os.path.dirname(abs_path)
if module_dir not in sys.path:
    sys.path.append(module_dir)

from naive_rag import wiki_rag_e2e, RAGE2EOutput, WIKIRAGE2EInput
from naive_rag import naive_rag_fsm


def line_to_req(line: str):
    qid, question, wiki_query = line.split("\t")
    return WIKIRAGE2EInput(
        collection_name=qid,
        question=question,
        wiki_query=wiki_query,
        context_limit=4,
    )


def invoke_service(request):
    # wiki_rag_e2e(request)
    ret = wiki_rag_e2e(request)
    print(f"return from wiki_rag_e2e: {ret}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("-f", "--file_path", default="./.cache/wiki_qa.txt")
    parser.add_argument(
        "-cf", "--config_file", default="../config/naive_rag-client.yml"
    )
    parser.add_argument("-m", "--mode", default="one-by-one")
    parser.add_argument("-n", "--request_num", default=100, type=int)
    parser.add_argument("-l", "--lambda_rate", default=1.0, type=float)
    args = parser.parse_args()

    config_file = args.config_file

    config = FastServiceConfig.load_from_file(os.path.abspath(config_file))
    naive_rag_fsm.setup_client_mode(config)

    bm_config = FastServiceBenchmarkConfig(
        file_path=args.file_path,
        mode=args.mode,
        request_num=args.request_num,
    )
    benchmark = FastServiceBenchmark(
        config=bm_config, line_to_request=line_to_req, invoke_service=invoke_service
    )

    print("start benchmark")
    benchmark.execute()
    print("end benchmark")
