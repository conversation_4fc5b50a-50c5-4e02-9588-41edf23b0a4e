import os, sys
import pandas as pd
from datasets import load_dataset


ds = load_dataset("microsoft/wiki_qa")

print(ds["train"].column_names)

selected_cols = ["question_id", "question", "document_title"]

train_df = ds["train"].select_columns(selected_cols).to_pandas().drop_duplicates()
valid_df = ds["validation"].select_columns(selected_cols).to_pandas().drop_duplicates()
test_df = ds["test"].select_columns(selected_cols).to_pandas().drop_duplicates()

print(len(train_df), len(valid_df), len(test_df))
df = pd.concat([train_df, valid_df, test_df]).drop_duplicates()
df.rename(
    columns={
        "question_id": "qid",
        "question": "query",
        "document_title": "collection_name",
    },
    inplace=True,
)

os.makedirs("./.cache", exist_ok=True)
df.to_csv("./.cache/wiki_qa.txt", index=False, header=False, sep="\t")
