FROM pytorch/pytorch:latest

# download and install fast-service
COPY .cache/fast-service /workspace/fast-service
RUN cd /workspace/fast-service && \
    pip install -r requirements.txt && \
    pip install -e .

# install dependencies for naive_rag
WORKDIR /workspace/naive_rag
COPY requirements.txt requirements.txt
RUN pip install -r requirements.txt

# copy necessary code to docker image
COPY src src
COPY test test
COPY scripts scripts
COPY config config

# add naive_rag to PYTHONPATH
ENV PYTHONPATH=/workspace/naive_rag/src

# set workdir and default command
WORKDIR /workspace/naive_rag/scripts
CMD ["python", "launch_services.py"]