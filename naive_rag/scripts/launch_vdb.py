import os
import argparse


def remove_sudo_in_file(file_path):
    try:
        # Read the content of the file
        with open(file_path, "r") as file:
            content = file.read()

        # Replace "sudo " with ""
        new_content = content.replace("sudo ", "")

        # Write the modified content back to the file
        with open(file_path, "w") as file:
            file.write(new_content)

        print(f"Successfully replaced 'sudo ' with '' in {file_path}")

    except Exception as e:
        print(f"An error occurred: {e}")


def get_args():
    parser = argparse.ArgumentParser()
    user_home = os.path.expanduser("~")
    parser.add_argument("--home", default=f"{user_home}/.cache/milvus_standalone")
    parser.add_argument("--sudo", action="store_true")

    args = parser.parse_args()
    return args


if __name__ == "__main__":
    args = get_args()
    milvus_home = args.home
    os.makedirs(milvus_home, exist_ok=True)
    filename = "milvus_standalone_embed.sh"
    file_path = os.path.join(milvus_home, filename)

    download_cmd = f"curl -sfL https://raw.githubusercontent.com/milvus-io/milvus/master/scripts/standalone_embed.sh -o {file_path}"
    os.system(download_cmd)

    if not args.sudo:
        remove_sudo_in_file(file_path)

    start_cmd = f"cd {milvus_home} && bash {filename} start"

    os.system(start_cmd)
