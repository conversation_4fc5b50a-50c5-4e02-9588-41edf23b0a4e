import os
import argparse


def get_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("-p", "--port", default=8000)
    parser.add_argument("-m", "--model", default="google/gemma-2-2b-it")
    parser.add_argument("-hf", "--hf_home", default="~/.cache/huggingface")
    parser.add_argument("-s", "--hf_secret", required=True)
    parser.add_argument("-n", "--name", default="vllm_google_gemma-2-2b-it")

    args = parser.parse_args()
    return args


if __name__ == "__main__":
    args = get_args()
    cmd = f"""docker ps|grep vllm/vllm-openai:latest|grep {args.name}|wc -l"""
    res = int(os.popen(cmd).read())
    if res == 1:
        print(f"{args.name} is running")
    else:
        cmd = f"""
            docker run -d \
            --name {args.name} \
            --runtime nvidia --gpus all \
            -v {args.hf_home}:/root/.cache/huggingface \
            --env "HUGGING_FACE_HUB_TOKEN={args.hf_secret}" \
            -p {args.port}:8000 \
            --ipc=host \
            vllm/vllm-openai:latest \
            --model {args.model}
            """
        os.system(cmd)
