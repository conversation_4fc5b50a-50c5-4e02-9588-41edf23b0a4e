# go to the parent directory of test_container.sh
cd ..

# build the docker image for naive rag
docker build -t naive-rag:v1 .

# clean up old instances
docker stop rag-1 rag-2 rag-3 rag-load-balancer
docker rm rag-1 rag-2 rag-3 rag-load-balancer

# launch 3 service instances
docker run -d --rm --gpus all --name rag-1 -p 20101:20100 -v ./.cache/config/settings.yml:/workspace/naive_rag/config/settings.yml naive-rag:v1
docker run -d --rm --gpus all --name rag-2 -p 20102:20100 -v ./.cache/config/settings.yml:/workspace/naive_rag/config/settings.yml naive-rag:v1
docker run -d --rm --gpus all --name rag-3 -p 20103:20100 -v ./.cache/config/settings.yml:/workspace/naive_rag/config/settings.yml naive-rag:v1

# launch nginx for load-balancing
docker run -d --rm --name rag-load-balancer -p 20100:20100 -v ./config/nginx.conf:/etc/nginx/nginx.conf:ro nginx

# download documents for testing
dir_path=".cache/download/papers"
mkdir -p $dir_path
curl -o $dir_path/paper1.pdf https://www.usenix.org/system/files/osdi24-zhong-yinmin.pdf
curl -o $dir_path/paper2.pdf https://www.usenix.org/system/files/osdi24-sun-biao.pdf
curl -o $dir_path/paper3.pdf https://www.usenix.org/system/files/osdi24-agrawal.pdf
curl -o $dir_path/paper4.pdf https://www.usenix.org/system/files/osdi24-fu.pdf
curl -o $dir_path/paper5.pdf https://www.usenix.org/system/files/osdi24-lee.pdf

# prepare requests.txt for testing
file_paths=()
for file in "$dir_path"/*.pdf; do
    file_path=$(realpath "$file")
    file_paths+=("$file_path")
done
for i in "${!file_paths[@]}"; do
    echo "${i}, rag_e2e, ${file_paths[$i]}" >>.cache/requests.txt
done

# run load_gen for testing
python benchmark/load_gen.py -f .cache/requests.txt -c config/naive_rag-client.yml

# clean up
rm -rf .cache/requests.txt
docker stop rag-1 rag-2 rag-3 rag-load-balancer
