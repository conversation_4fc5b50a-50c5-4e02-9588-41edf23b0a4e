# Naive RAG

This is an implementation of a naive RAG (Retrieval-Augmented Generation) application. We will first index a set of documents and then ask questions based on the context retrieved from these documents.


## Dependencies

1. vllm for llm inference
2. milvus for vector storage

## Services

### indexing
Split the documents into multiple chunks, transform the chunks into embeddings, and save these embeddings into a collection (i.e., a table) in the vector store.


- Args
  - collection_name: the name of the collection for storing the documents and their embeddings
  - files: a list of documents to be indexed
- Return
  - count: the number of chunks stored during this process
  - cost: the time taken for saving embedding, as reported by Milvus
  
### rag_chat
Retrieve a set of related chunks from the vector store and answer the question based on these chunks as context.


- Args
  - history: the history of the chat
  - question: the question to ask
  - collection_name: the source of the context for RAG
  - context_limit: the number of chunks to retrieve from the vector store for answering the current question
- Return
  - answer: The answer of the question
  - input_tokens: the number of tokens in context and question
  - output_tokens: the number of tokens in answer
  - query_context: the context used for the current answer

### undo_indexing
Undo the indexing of a set of documents by deleting their embeddings from the vector store.

- Args
  - collection_name: the collection that the documents belong to
  - filenames: the name of the files in the collection
- Return
  - collection_stats: the statistics of the current collection after undoing the indexing

### drop_collection
Delete a collection of vector store.

- Args
  - collection_name: the name of the collection to be deleted
- Return
  - collection_exists: whether the collection exists after dropping

### rag_e2e
An end-to-end procedure for using naive RAG. It will sequentially invoke "indexing, rag_chat, undo_indexing, drop_collection" and return the answer to the user's question.

- Args
  - collection_name: the name of the collection for storing the documents and their embedding
  - files: the documents used as the source of context
  - question: the question from user
  - context_limit: the number of chunks being used as context for answering the question
- Return
  - answer: The answer of the question
  - input_tokens: the number of tokens in context and question
  - output_tokens: the number of tokens in answer

## How to run

First, you need to add `naive_rag/src` to PYTHONPATH
``` bash
export PYTHONPATH=<realpath_of_naive_rag_src>:$PYTHONPATH
```

Go to naive_rag/scripts
``` bash
python launch_vdb.py
python launch_vllm.py -s <HUGGING_FACE_HUB_TOKEN>
python launch_services.py
```

This will first launch vllm and Milvus, followed by the services as a process on the current server.

`launch_services.py` will automatically read two configuration files: `naive_rag/config/naive_rag-server.yml` (for deployment configuration, e.g., the IP and port of each service) and `naive_rag/config/settings.yml` (for the internal attributes of the services, e.g., the addresses of vllm and Milvus, the API key, etc.). You can update these for a customized configuration. Based on the default configuration file, the services will be launched at 0.0.0.0:20100. You can access `http://0.0.0.0:20100/docs` to check the available services.



## How to deploy (as container)

### Launch Dependencies
First, ensure that vllm and Milvus are running by executing:
``` bash
python launch_vdb.py
python launch_vllm.py -s <HUGGING_FACE_HUB_TOKEN>
```

### Build image
Next, build the Docker image for naive RAG. Navigate to naive_rag/ and run:
``` bash
docker build -t naive_rag:v1 .
```

### Launch service instances
Now, launch several containers as serving instances. For example, to launch three instances, execute:

``` bash
docker run -d --rm --gpus all --name rag-1 -p 20101:20100 -v ./.cache/config/settings.yml:/workspace/naive_rag/config/settings.yml naive-rag:v1
docker run -d --rm --gpus all --name rag-2 -p 20102:20100 -v ./.cache/config/settings.yml:/workspace/naive_rag/config/settings.yml naive-rag:v1
docker run -d --rm --gpus all --name rag-3 -p 20103:20100 -v ./.cache/config/settings.yml:/workspace/naive_rag/config/settings.yml naive-rag:v1
```
Note: Remember to bind a unique port for each instance. When deploying the service as a container, do not use the default settings.yml, which has `localhost:xxxx` for vllm and Milvus. Instead, specify the actual host IP to access these services. Prepare `./.cache/config/settings.yml` with:

``` yaml
... # the same as default settings.yml
vdb_uri: "http://*************:19530"
openai_base_url: "http://*************:8000/v1"
```

### Launch load balancer
Launch NGINX for load balancing with the following command:

``` bash
docker run -d --rm --name rag-load-balancer -p 20100:20100 -v ./config/nginx.conf:/etc/nginx/nginx.conf:ro nginx
```
Note: If you used different ports for the service instances or launched a different number of instances, update `./config/nginx.conf` accordingly.

## How to test

### Simple test
``` bash
cd naive_rag/test
python test_naive_rag.py
```

### Test with load generator

``` bash
cd naive_rag

# download documents for testing
dir_path=".cache/download/papers"
mkdir -p $dir_path
curl -o $dir_path/paper1.pdf https://www.usenix.org/system/files/osdi24-zhong-yinmin.pdf
curl -o $dir_path/paper2.pdf https://www.usenix.org/system/files/osdi24-sun-biao.pdf
curl -o $dir_path/paper3.pdf https://www.usenix.org/system/files/osdi24-agrawal.pdf
curl -o $dir_path/paper4.pdf https://www.usenix.org/system/files/osdi24-fu.pdf
curl -o $dir_path/paper5.pdf https://www.usenix.org/system/files/osdi24-lee.pdf

# prepare requests.txt for testing
file_paths=()
for file in "$dir_path"/*.pdf; do
    file_path=$(realpath "$file")
    file_paths+=("$file_path")
done
for i in "${!file_paths[@]}"; do
    echo "${i}, rag_e2e, ${file_paths[$i]}" >>.cache/requests.txt
done

# run load_gen for testing
python benchmark/load_gen.py -f .cache/requests.txt -c config/naive_rag-client.yml
```

## On-click Deployment + Testing

``` bash
cd naive_rag/scripts
bash test_deployment.sh
```

## Benchmarking with Wiki-QA dataset

First, download the Wiki-QA dataset and prepare the dataset for testing.
``` bash
python prepare_dataset.py
```
It will save the dataset in huggingface dataset home and generate `./.cache/wiki_qa.txt` for testing.

Then, run the benchmarking script.
``` bash
python wiki_load_gen.py -n 300
```
Note: it takes around 50s for each request (due to the loading latency of documents from wikipages).