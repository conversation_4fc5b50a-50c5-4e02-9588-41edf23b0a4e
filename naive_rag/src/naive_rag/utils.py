from functools import lru_cache
from pydantic_settings import BaseSettings
import logging
import os
import yaml


class Settings(BaseSettings):
    app_name: str = "Naive RAG"

    vdb_uri: str = "http://localhost:19530"
    vdb_collection: str = "test"

    embedding_device: str = "cuda"

    raw_files_dir: str = ".cache/files"

    chunk_size: int = 256
    chunk_overlap: int = 128

    llm_model: str = "google/gemma-2-2b-it"

    openai_api_key: str = "EMPTY"
    openai_base_url: str = "http://localhost:8000/v1"


@lru_cache
def get_settings():
    env_file = os.path.join(os.path.dirname(__file__), "../../config/settings.yml")
    with open(env_file, "r", encoding="utf-8") as f:
        data = yaml.load(f.read(), Loader=yaml.FullLoader)
    return Settings(**data)


@lru_cache
def get_logger():
    naive_rag_logger = logging.getLogger(name="naive_rag")
    naive_rag_logger.setLevel(logging.DEBUG)
    fh = logging.FileHandler(filename=".cache/debug.log")
    fh.setLevel(logging.DEBUG)
    naive_rag_logger.addHandler(fh)
    return naive_rag_logger
