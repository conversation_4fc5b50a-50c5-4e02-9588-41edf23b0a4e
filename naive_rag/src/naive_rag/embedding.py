from typing import List
from abc import abstractmethod
import numpy as np
from sentence_transformers import SentenceTransformer


class BaseEmbeddingFunction:
    @abstractmethod
    def __call__(self, texts: List[str]):
        """ """

    @abstractmethod
    def encode(self, data: List[str]):
        """ """


class SentenceTransformerEmbeddingFunction(BaseEmbeddingFunction):
    def __init__(
        self,
        model_name: str = "all-MiniLM-L6-v2",
        batch_size: int = 32,
        query_instruction: str = "",
        doc_instruction: str = "",
        device: str = "cpu",
        normalize_embeddings: bool = True,
        **kwargs,
    ):
        self.model_name = model_name
        self.query_instruction = query_instruction
        self.doc_instruction = doc_instruction
        self.batch_size = batch_size
        self.normalize_embeddings = normalize_embeddings

        _model_config = dict(
            {"model_name_or_path": model_name, "device": device}, **kwargs
        )
        self.model = SentenceTransformer(**_model_config)

    def __call__(self, texts: List[str]) -> List[np.array]:
        return self._encode(texts)

    def _encode(self, texts: List[str]) -> List[np.array]:
        embs = self.model.encode(
            texts,
            batch_size=self.batch_size,
            show_progress_bar=False,
            convert_to_numpy=True,
            normalize_embeddings=self.normalize_embeddings,
        )
        return list(embs)

    @property
    def dim(self):
        return self.model.get_sentence_embedding_dimension()

    def encode(self, data: List[str]):
        return self._encode(data)

    def encode_queries(self, queries: List[str]) -> List[np.array]:
        instructed_queries = [self.query_instruction + query for query in queries]
        return self._encode(instructed_queries)

    def encode_documents(self, documents: List[str]) -> List[np.array]:
        instructed_documents = [
            self.doc_instruction + document for document in documents
        ]
        return self._encode(instructed_documents)
