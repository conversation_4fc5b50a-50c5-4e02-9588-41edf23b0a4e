import requests
import os
import json
from typing import BinaryIO
import argparse
from fast_service import FastServiceConfig, FastServiceFile, RequestContext
from naive_rag import naive_rag_fsm
from naive_rag import indexing, DocIndexingOutput
from naive_rag import rag_chat, RAGChatInput, RAGChatOutput
from naive_rag import undo_indexing, UndoIndexingInput, UndoIndexingOutput
from naive_rag import drop_collection, DropCollectionInput, DropCollectionOutput
from naive_rag import rag_e2e, RAGE2EOutput


def test_client_interface(file_paths, collection_name, question, questions):
    files = [FastServiceFile.load(open(file_path, "rb")) for file_path in file_paths]
    y1 = indexing(collection_name=collection_name, files=files)
    print(f"y1 => {y1.model_dump()}")
    y2 = rag_chat(
        RAGChatInput(
            history=[],
            question=question,
            collection_name=collection_name,
            context_limit=3,
        )
    )
    print(f"y2 => {y2.model_dump()}")

    answers = []
    for i, q in enumerate(questions):
        item = RAGChatInput(
            history=[], question=q, collection_name=collection_name, context_limit=3
        )
        answer = rag_chat(item=item).model_dump()
        print(f"q-{i} => {answer}")
        answers.append(answer)
    print(f"answers: {answers}")

    y3 = undo_indexing(
        UndoIndexingInput(
            collection_name=collection_name,
            filenames=[file.get_filename() for file in files],
        )
    )
    print(f"y3 => {y3.model_dump()}")
    y4 = drop_collection(DropCollectionInput(collection_name=collection_name))
    print(f"y4 => {y4.model_dump()}")
    files = [FastServiceFile.load(open(file_path, "rb")) for file_path in file_paths]
    y5 = rag_e2e(
        collection_name=collection_name, files=files, question=question, context_limit=3
    )
    print(f"y5 => {y5.model_dump()}")


def get_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("-d", "--file_dir", default="../.cache/download/papers")
    parser.add_argument("-n", "--collection_name", default="test")
    parser.add_argument("-c", "--config", default="../config/naive_rag-client.yml")

    args = parser.parse_args()
    return args


if __name__ == "__main__":
    args = get_args()
    file_dir = args.file_dir
    os.makedirs(file_dir, exist_ok=True)
    file_paths = [
        os.path.join(file_dir, "paper1.pdf"),
        os.path.join(file_dir, "paper2.pdf"),
    ]
    collection_name = args.collection_name
    question = "Please summarize the papers for me."
    questions = [
        f"Please help summarize the {part} of these papers."
        for part in [
            "Introduction",
            "Background",
            "Solution",
            "Evaluation",
            "Related Work",
        ]
    ]

    config = FastServiceConfig.load_from_file(args.config)
    naive_rag_fsm.setup_client_mode(config)

    # test client mode interface
    test_client_interface(file_paths, collection_name, question, questions)
