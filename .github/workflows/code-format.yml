name: Code-Format-Check

on:
  push:
    branches:
      - master
    paths-ignore:
      - 'docs/**'
  pull_request:
    branches:
      - main
    paths-ignore:
      - 'docs/**'

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  code-format-check:

    runs-on: ubuntu-latest

    strategy:
      fail-fast: false
      matrix:
        python-version: ["3.12"]
        black-version: ["24.8.0"]


    steps:
      - uses: actions/checkout@v4
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}
      - name: Set up black ${{ matrix.black-version }}
        run:
          pip install black==${{ matrix.black-version }}
      - name: Check code format
        run: |
          black --check .
          exit $?
