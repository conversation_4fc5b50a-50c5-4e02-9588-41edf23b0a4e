version: "3.5"

services:
  etcd:
    container_name: milvus-etcd
    image: quay.io/coreos/etcd:v3.5.5
    environment:
      - ETCD_AUTO_COMPACTION_MODE=revision
      - ETCD_AUTO_COMPACTION_RETENTION=1000
      - ETCD_QUOTA_BACKEND_BYTES=**********
      - ETCD_SNAPSHOT_COUNT=50000
    volumes:
      - etcd-data:/etcd
    command: etcd -advertise-client-urls=http://127.0.0.1:2379 -listen-client-urls http://0.0.0.0:2379 --data-dir /etcd
    healthcheck:
      test: ["CMD", "etcdctl", "endpoint", "health"]
      interval: 30s
      start_period: 90s
      timeout: 20s
      retries: 3
    deploy:
      resources:
        limits:
          cpus: ${ETCD_CPUS:-4.0} # Adjust CPU if necessary
          memory: ${ETCD_MEMORY:-32G} # Adjust memory if necessary

  minio:
    container_name: milvus-minio
    image: minio/minio:RELEASE.2023-03-20T20-16-18Z
    environment:
      MINIO_ACCESS_KEY: minioadmin
      MINIO_SECRET_KEY: minioadmin
    ports:
      - "19001:19001"
      - "19000:19000"
    volumes:
      - minio-data:/minio_data
    command: minio server /minio_data --address ":19000" --console-address ":19001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:19000/minio/health/live"]
      interval: 30s
      start_period: 90s
      timeout: 20s
      retries: 3
    deploy:
      resources:
        limits:
          cpus: ${MINIO_CPUS:-4.0} # Adjust CPU if necessary
          memory: ${MINIO_MEMORY:-32G} # Adjust memory if necessary

  milvus-service:
    container_name: milvus-service
    image: milvusdb/milvus:v2.5.0-beta
    command: ["milvus", "run", "standalone"]
    security_opt:
      - seccomp:unconfined
    environment:
      ETCD_ENDPOINTS: etcd:2379
      MINIO_ADDRESS: minio:19000
    volumes:
      - milvus-data:/var/lib/milvus
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9091/healthz"]
      interval: 30s
      start_period: 90s
      timeout: 20s
      retries: 3
    ports:
      - "19530:19530"
      - "9091:9091"
    deploy:
      resources:
        limits:
          cpus: ${MILVUS_CPUS:-32.0} # Adjust CPU if necessary
          memory: ${MILVUS_MEMORY:-64G} # Adjust memory if necessary
    depends_on:
      - "etcd"
      - "minio"

volumes:
  etcd-data:
  minio-data:
  milvus-data:
