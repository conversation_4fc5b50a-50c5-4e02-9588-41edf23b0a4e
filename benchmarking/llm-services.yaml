version: "2.28.1"

services:
  vllm-service:
    image: vllm/vllm-openai:${LLM_VERSION:-v0.7.2} # Image with version as a variable
    container_name: vllm-service # Container name as a variable
    runtime: nvidia # Use NVIDIA runtime for GPUs
    environment:
      - HUGGING_FACE_HUB_TOKEN=${HUGGING_FACE_HUB_TOKEN} # Hugging Face Hub Token
      - GPU_MEMORY_UTILIZATION=${LLM_GPU_MEMORY_UTILIZATION:-0.9} # GPU memory utilization
    ports:
      - "${LLM_PORT:-8000}:8000" # Expose port 8000 to a custom port
    volumes:
      - ${HF_HOME}:/root/.cache/huggingface # Mount Hugging Face cache directory
    ipc: host # Use host IPC
    deploy:
      resources:
        limits:
          cpus: ${LLM_CPUS:-8.0} # Adjust CPU if necessary
          memory: ${LLM_MEMORY:-16G} # Adjust memory if necessary
        reservations:
          devices:
            - driver: nvidia
              device_ids: ["${LLM_DEVICE}"] # Assign the GPU device
              capabilities: [gpu]
    command: "--model ${LLM_MODEL:-Qwen/Qwen2.5-1.5B-Instruct} --device ${LLM_HARDWARE:-auto} --gpu-memory-utilization ${LLM_GPU_MEMORY_UTILIZATION:-0.9} --disable-log-requests" # Command to run with the model argument
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      start_period: 90s
      timeout: 20s
      retries: 3
