services:
  sweagent-SWEEnv:
    image: sweagent:v1
    container_name: sweagent-SWEEnv
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - "${SWEAGENT_ROOT}/trajectories:/workspace/sweagent/trajectories"
      - "${SWEAGENT_ROOT}/.cache:/workspace/sweagent/.cache"
      - "${SWEAGENT_ROOT}/config:/workspace/sweagent/config"
      - "${SWEAGENT_ROOT}/scripts/SWEEnv_service.py:/workspace/sweagent/scripts/SWEEnv_service.py"
    command: python scripts/SWEEnv_service.py
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:1701/docs"]
      interval: 30s
      start_period: 30s
      timeout: 5s
      retries: 3

  sweagent-model:
    image: sweagent:v1
    container_name: sweagent-model
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - "${SWEAGENT_ROOT}/trajectories:/workspace/sweagent/trajectories"
      - "${SWEAGENT_ROOT}/.cache:/workspace/sweagent/.cache"
      - "${SWEAGENT_ROOT}/config:/workspace/sweagent/config"
      - "${SWEAGENT_ROOT}/scripts/model_service.py:/workspace/sweagent/scripts/model_service.py"
    command: python scripts/model_service.py
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:1702/docs"]
      interval: 30s
      start_period: 30s
      timeout: 5s
      retries: 3

  sweagent-server:
    image: sweagent:v1
    container_name: sweagent-server
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - "${SWEAGENT_ROOT}/trajectories:/workspace/sweagent/trajectories"
      - "${SWEAGENT_ROOT}/.cache:/workspace/sweagent/.cache"
      - "${SWEAGENT_ROOT}/config:/workspace/sweagent/config"
      - "${SWEAGENT_ROOT}/scripts/server_service.py:/workspace/sweagent/scripts/server_service.py"
    command: python scripts/server_service.py
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:1703/docs"]
      interval: 30s
      start_period: 30s
      timeout: 5s
      retries: 3

  sweagent-loadgen:
    image: sweagent:v1
    container_name: sweagent-loadgen
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - "${SWEAGENT_ROOT}/trajectories:/workspace/sweagent/trajectories"
      - "${SWEAGENT_ROOT}/.cache:/workspace/sweagent/.cache"
      - "${SWEAGENT_ROOT}/config:/workspace/sweagent/config"
      - "${SWEAGENT_ROOT}/benchmark/load_gen.py:/workspace/sweagent/benchmark/load_gen.py"
    command: 
      - python 
      - benchmark/load_gen.py 
      - --model_name 
      - azure:gpt-4o-2024-05-13
      - --bm_mode
      - one-by-one
      - --config_file
      - config/default.yaml
      - --per_instance_cost_limit 
      - "2.00"
    # extends:
    #   file: loadgen.yaml
    #   service: loadgen
    working_dir: /workspace/sweagent
    depends_on:
      sweagent-SWEEnv:
        condition: service_healthy
      sweagent-model:
        condition: service_healthy
      sweagent-server:
        condition: service_healthy
