version: "3.8"

services:
  webarena-homepage:
    image: webarena:v1
    container_name: webarena-homepage
    ports:
      - "4399:4399"
    command: python environment_docker/webarena-homepage/app.py
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4399"]
      interval: 30s
      start_period: 30s
      timeout: 5s
      retries: 3

  webarena-env-manager:
    image: webarena:v1
    container_name: webarena-env-manager
    environment:
      TMPDIR: /workspace/webarena/tmp
      SHOPPING: "http://${WEBSITE_HOST}:7770"
      SHOPPING_ADMIN: "http://${WEBSITE_HOST}:7780/admin"
      REDDIT: "http://${WEBSITE_HOST}:9999"
      GITLAB: "http://${WEBSITE_HOST}:8023"
      MAP: "http://${WEBSITE_HOST}:3000"
      WIKIPEDIA: "http://${WEBSITE_HOST}:8888/wikipedia_en_all_maxi_2022-05/A/User:The_other_Kiwix_guy/Landing"
      HOMEPAGE: "http://${WEBSITE_HOST}:4399"
    volumes:
      - "${WEBARENA_ROOT}/tmp:/workspace/webarena/tmp"
      - "${WEBARENA_ROOT}/datasets:/workspace/webarena/datasets"
      - "${WEBARENA_ROOT}/.auth:/workspace/webarena/.auth"
      - "${WEBARENA_ROOT}/.cache:/workspace/webarena/.cache"
      - "${WEBARENA_ROOT}/results:/workspace/webarena/results"
      - "${WEBARENA_ROOT}/scripts/webarena_env_manager_service.py:/workspace/webarena/scripts/webarena_env_manager_service.py"
      - "${WEBARENA_ROOT}/config:/workspace/webarena/config"
    command: python scripts/webarena_env_manager_service.py
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:1701/docs"]
      interval: 30s
      start_period: 30s
      timeout: 5s
      retries: 3

  webarena-llm:
    image: webarena:v1
    container_name: webarena-llm
    environment:
      TMPDIR: /workspace/webarena/tmp
      SHOPPING: "http://${WEBSITE_HOST}:7770"
      SHOPPING_ADMIN: "http://${WEBSITE_HOST}:7780/admin"
      REDDIT: "http://${WEBSITE_HOST}:9999"
      GITLAB: "http://${WEBSITE_HOST}:8023"
      MAP: "http://${WEBSITE_HOST}:3000"
      WIKIPEDIA: "http://${WEBSITE_HOST}:8888/wikipedia_en_all_maxi_2022-05/A/User:The_other_Kiwix_guy/Landing"
      HOMEPAGE: "http://${WEBSITE_HOST}:4399"
      OPENAI_API_KEY: "${OPENAI_API_KEY}"
      OPENAI_API_TYPE: "${OPENAI_API_TYPE:-}"
      OPENAI_BASE_URL: "${OPENAI_BASE_URL:-}"
      AZURE_OPENAI_ENDPOINT: "${AZURE_OPENAI_ENDPOINT:-}"
    volumes:
      - "${WEBARENA_ROOT}/tmp:/workspace/webarena/tmp"
      - "${WEBARENA_ROOT}/datasets:/workspace/webarena/datasets"
      - "${WEBARENA_ROOT}/.auth:/workspace/webarena/.auth"
      - "${WEBARENA_ROOT}/.cache:/workspace/webarena/.cache"
      - "${WEBARENA_ROOT}/results:/workspace/webarena/results"
      - "${WEBARENA_ROOT}/scripts/webarena_llm_service.py:/workspace/webarena/scripts/webarena_llm_service.py"
      - "${WEBARENA_ROOT}/config:/workspace/webarena/config"
    command: python scripts/webarena_llm_service.py
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:1702/docs"]
      interval: 30s
      start_period: 30s
      timeout: 5s
      retries: 3

  webarena-execution-coordinator:
    image: webarena:v1
    container_name: webarena-execution-coordinator
    environment:
      TMPDIR: /workspace/webarena/tmp
      PYTHONPATH: /workspace/webarena
      SHOPPING: "http://${WEBSITE_HOST}:7770"
      SHOPPING_ADMIN: "http://${WEBSITE_HOST}:7780/admin"
      REDDIT: "http://${WEBSITE_HOST}:9999"
      GITLAB: "http://${WEBSITE_HOST}:8023"
      MAP: "http://${WEBSITE_HOST}:3000"
      WIKIPEDIA: "http://${WEBSITE_HOST}:8888/wikipedia_en_all_maxi_2022-05/A/User:The_other_Kiwix_guy/Landing"
      HOMEPAGE: "http://${WEBSITE_HOST}:4399"
    volumes:
      - "${WEBARENA_ROOT}/tmp:/workspace/webarena/tmp"
      - "${WEBARENA_ROOT}/datasets:/workspace/webarena/datasets"
      - "${WEBARENA_ROOT}/.auth:/workspace/webarena/.auth"
      - "${WEBARENA_ROOT}/.cache:/workspace/webarena/.cache"
      - "${WEBARENA_ROOT}/results:/workspace/webarena/results"
      - "${WEBARENA_ROOT}/scripts/webarena_execution_coordinator_service.py:/workspace/webarena/scripts/webarena_execution_coordinator_service.py"
      - "${WEBARENA_ROOT}/config:/workspace/webarena/config"
    command: python scripts/webarena_execution_coordinator_service.py
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:1703/docs"]
      interval: 30s
      start_period: 30s
      timeout: 5s
      retries: 3


  webarena-loadgen:
    image: webarena:v1
    container_name: webarena-loadgen
    environment:
      TMPDIR: /workspace/webarena/tmp
      PYTHONPATH: /workspace/webarena
      SHOPPING: "http://${WEBSITE_HOST}:7770"
      SHOPPING_ADMIN: "http://${WEBSITE_HOST}:7780/admin"
      REDDIT: "http://${WEBSITE_HOST}:9999"
      GITLAB: "http://${WEBSITE_HOST}:8023"
      MAP: "http://${WEBSITE_HOST}:3000"
      WIKIPEDIA: "http://${WEBSITE_HOST}:8888/wikipedia_en_all_maxi_2022-05/A/User:The_other_Kiwix_guy/Landing"
      HOMEPAGE: "http://${WEBSITE_HOST}:4399"
    volumes:
      - "${WEBARENA_ROOT}/tmp:/workspace/webarena/tmp"
      - "${WEBARENA_ROOT}/datasets:/workspace/webarena/datasets"
      - "${WEBARENA_ROOT}/.auth:/workspace/webarena/.auth"
      - "${WEBARENA_ROOT}/.cache:/workspace/webarena/.cache"
      - "${WEBARENA_ROOT}/results:/workspace/webarena/results"
      - "${WEBARENA_ROOT}/config:/workspace/webarena/config"
      - "${WEBARENA_ROOT}/log_files:/workspace/webarena/log_files"
      - "${WEBARENA_ROOT}/benchmark:/workspace/webarena/benchmark"
    command: 
      - python 
      - benchmark/load_gen.py 
      - --instruction_path 
      - config/prompts/jsons/p_cot_id_actree_2s.json 
      - --model 
      - ${LLM_MODEL} 
      - --mode 
      - completion 
      - --no_eval
      - --bm_mode
      - poisson
    # extends:
    #   file: loadgen.yaml
    #   service: loadgen
    working_dir: /workspace/webarena
    depends_on:
      webarena-env-manager:
        condition: service_healthy
      webarena-llm:
        condition: service_healthy
      webarena-execution-coordinator:
        condition: service_healthy
      webarena-homepage:
        condition: service_healthy