version: "2.28.1"
name: rag-benchmarking

include:
  - milvus-standalone.yaml

services:
  llm-service:
    extends:
      file: llm-services.yaml
      service: vllm-service

  situating-service:
    extends:
      file: situating-services.yaml
      service: situating-vllm-service

  embedding-service:
    extends:
      file: embedding-services.yaml
      service: ${EMBEDDING_ENGINE:-vllm}-embedding-service

  reranking-service:
    extends:
      file: reranking-services.yaml
      service: ${RERANKING_ENGINE:-vllm}-reranking-service

  rag-service:
    image: rag:v1
    container_name: rag-coordinator
    extends:
      file: coordinator.yaml
      service: coordinator-service
    volumes:
      - ${AGENT_DATA}:${AGENT_DATA}
      - ${AGENT_CACHE}:/cache
      - ${AGENT_CONFIG}:/config
      - ${AGENT_RESULTS}/server:/output
      - /mnt/data/nltk_data:/usr/lib/nltk_data
    ports:
      - "${AGENT_PORT:-20000}:20000"
    depends_on:
      llm-service:
        condition: service_healthy
      embedding-service:
        condition: service_healthy
      milvus-service:
        condition: service_healthy
      reranking-service:
        condition: service_healthy
      situating-service:
        condition: service_healthy

  rag-loadgen:
    image: rag:v1
    container_name: rag-loadgen
    volumes:
      - ${AGENT_DATA}:${AGENT_DATA}
      - ${AGENT_CACHE}:/cache
      - ${AGENT_CONFIG}:/config
      - ${AGENT_RESULTS}/client:/output
      - ${AGENT_TMPDATA:-/rag-tmpdata}:${AGENT_TMPDATA:-/tmpdata}
    working_dir: /workspace/rag/benchmark
    command: "python load_gen.py --config /config/client.yml --settings /config/settings.yml --data_dir ${AGENT_DATA} --output_file /output/output.txt --request_version ${REQUEST_VERSION:-latest} ${BENCHMARK_ARGS} --warm_up ${WARM_UP:-0}"
    extends:
      file: loadgen.yaml
      service: loadgen
    depends_on:
      rag-service:
        condition: service_healthy
