version: "2.28.1"

services:
  vllm-reranking-service:
    image: vllm/vllm-openai:v0.7.2
    container_name: reranking-service
    runtime: nvidia
    deploy:
      resources:
        limits:
          cpus: ${RERANKING_CPUS:-8.0} # Adjust CPU if necessary
          memory: ${RERANKING_MEMORY:-16G} # Adjust memory if necessary
        reservations:
          devices:
            - driver: nvidia
              device_ids: ["${RERANKING_DEVICE:-0}"]
              capabilities: [gpu]
    environment:
      - HUGGING_FACE_HUB_TOKEN=${HUGGING_FACE_HUB_TOKEN}
      - GPU_MEMORY_UTILIZATION=${RERANKING_GPU_MEMORY_UTILIZATION:-0.9}
    ports:
      - "${RERANKING_PORT:-18000}:8000"
    volumes:
      - ${HF_HOME}:/root/.cache/huggingface
    ipc: host
    command: "--trust-remote-code --task score --model ${RERANKING_MODEL:-BAAI/bge-reranker-large} --gpu-memory-utilization ${RER<PERSON><PERSON><PERSON>_GPU_MEMORY_UTILIZATION:-0.9} --disable-log-requests --disable-log-stats"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      start_period: 3600s
      timeout: 20s
      retries: 3
