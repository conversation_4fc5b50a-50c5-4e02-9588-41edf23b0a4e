# Tutorial
This document introduce how to implement an agent based on fast-service. We use naive_rag as an example.

## Basic Concepts
Before development, let's learn some basic concepts first.

|  ![agent.jpg](./agent_structure.jpg)  |
| :-----------------------------------: |
| *Figure 1. The structure of an agent* |

- **AI Agent**: Applications built on top of LLMs. An application may utilize multiple LLMs and tools. In this project, we will deploy the agent in the cloud and access it via the network. An agent typically follows the structure shown in *Figure 1*. As depicted, an agent service usually comprises multiple modules.

- **Workload Generator**: A separate process that sends requests to the agent following a specific pattern. The workload generator also collects the agent's responses.

- **Monitor**: An independent service that continuously monitors the execution process of the agent, along with various system metrics like resource usage.

- **Agent Service**: The entry point of the agent during serving time. It receives requests from the network and invokes sub-modules to process these requests. The agent service can call sub-modules in two ways: (1) function call or (2) HTTP, depending on the agent's deployment.

- **Sub-Module Service**: Each sub-module service is responsible for a specific part of the agent service's logic. Different sub-modules can communicate with each other through function calls or HTTP.

- **Load Balancer**: As shown in *Figure 2*, there may be multiple instances of an agent to handle requests. The load balancer automatically routes requests to these instances in a balanced manner.

| ![agent-instances.jpg](./agent_scaling.jpg) |
| :-----------------------------------------: |
|   *Figure 2. Scaling and load balancing*    |


## Usage of Fast-service

Please refer to [fast-service-example](https://github.com/hkust-adsl/fast-service/tree/master/example) with simple example to learn how to use fast-service. 


## Development of Naive RAG

RAG is an agent that answers questions based on a set of reference documents. Since there may be many documents, with only a portion relevant to a given question, RAG retrieves only the relevant chunks of these documents. Therefore, this agent requires the following key functionalities:

1. **Indexing a Document**: Given a document, split it into multiple chunks, transform the chunks into embeddings, and save these embeddings to a vector store for similarity search.

2. **Answering a Question**: Given a question, transform it into embeddings, and retrieve relevant chunks from the vector store through similarity search. The retrieved chunks will then be provided as context when asking the LLM for an answer to the question.

This example agent is a naive version of RAG, where only PDF files are accepted as documents, and there is no advanced module like query rewritting, etc.

### Dependencies
From the above introduction, we know that our agent needs LLM for QA and vecotor database for storing chunks and embeddings. In this example, we launch `vllm` with `google/gemma-2b-2it` for LLM serving, and milvus standalon for vector database. The launching scripts are in `naive_rag/scripts/launch_vllm.py` and `naive_rag/scripts/launch_vdb.py`

These two dependencies can be seen as two sub-module services of the agent, one for LLM Inference, and one for Vecotor DB Tool.

Note that the embedding function can also be launched as a *Sub-module Service*. However, for simplicity, we take it as a normal function in *Agent Service* in this example. 

### Naive RAG Implementation
The main logic of Naive RAG is implemented in `naive_rag/src/naive_rag/naive_rag.py`.

First, we will create an instance of `FastServiceManager`, which is responsible for registering functions our services.

``` python
naive_rag_fsm = FastServiceManager()
```

Each service will have multiple APIs with different functionalities. For example, in the Naive RAG agent, we have the following five APIs:

1. indexing: Split the documents into multiple chunks, transform the chunks into embeddings, and save these embeddings into a collection (i.e., a table) in the vector store.
2. rag_chat: Retrieve a set of related chunks from the vector store and answer the question based on these chunks as context.
3. undo_indexing: Undo the indexing of a set of documents by deleting their embeddings from the vector store.
4. drop_collection: Delete a collection of vector store.
5. rag_e2e: An end-to-end procedure for using naive RAG. It will sequentially invoke "indexing, rag_chat, undo_indexing, drop_collection" and return the answer to the user's question.


The logic of each API is implemented as a function decorated with `@naive_rag_fsm.fast_service`. For example:

``` python
@naive_rag_fsm.fast_service
def indexing(
    collection_name: str,
    files: list[FastServiceFile],
    context: RequestContext = None,
) -> DocIndexingOutput:
```

For the function arguments, Fast Service supports:

1. Built-in types like int, str
2. Classes that extend Pydantic.BaseModel
3. FastServiceFile for file handling
4. The list of above three types

The return value of each function should be an instance of a class that extends Pydantic.BaseModel. For example, DocIndexingOutput is defined as follows:


``` python
from pydantic import BaseModel

class DocIndexingOutput(BaseModel):
    count: int
    cost: float
```

Note that each function decorated with `@naive_rag_fsm.fast_service` must include the argument `context: RequestContext = None`. This is compulsory to support monitoring. Developers do not need to create the context manually; the Fast Service framework will handle it automatically. However, each API must pass context to all APIs it invokes. For example, in `rag_e2e`:

``` python
@naive_rag_fsm.fast_service
def rag_e2e(
    collection_name: str,
    files: list[FastServiceFile],
    question: str,
    context_limit: int = 3,
    context: RequestContext = None,
) -> RAGE2EOutput:
    y1: DocIndexingOutput = indexing(
        collection_name=collection_name, files=files, context=context
    )
    ...
    y2: RAGChatOutput = rag_chat(
        RAGChatInput(
            history=[],
            question=question,
            collection_name=collection_name,
            context_limit=context_limit,
        ),
        context=context,
    )
    ...
```

### Configuration and Deployment
There are two kinds of configuration files in an agent:

1. **Deployment Configuration**: This specifies how each `fast_service` function is deployed.
2. **(Optional) Service Configuration**: This contains the settings for some internal variables of the service, such as the IP and port of the VLLM and Milvus.

For details about the deployment configuration, please refer to [Fast Service Documentation](https://github.com/hkust-adsl/fast-service/blob/master/example/fastapi/README.md#4-prepare-the-yaml-file-to-configure-the-deployment).

For example, the default deployment configuration of Naive RAG is located at `naive_rag/config/naive_rag-server.yml`. In this file, we can see that all five APIs are deployed as a local service at `0.0.0.0:20100`. The last few lines are for monitoring; please ensure they are included to track the execution process and time cost.

Note that when writing the deployment configuration file, be careful about the `module_name`. Since we have an `__init__.py` file in the directory `naive_rag/src/naive_rag`, this directory will be interpreted as a Python module. Therefore, the `module_name` of the functions in `naive_rag/src/naive_rag/naive_rag.py` should be `naive_rag.naive_rag`.

The default service configuration of Naive RAG is located at `naive_rag/config/settings.yml`. Naive RAG will automatically load the configuration (refer to `naive_rag/src/naive_rag/utils.py:get_settings()`). 

Given the deployment configuration, we can launch our Naive RAG service by running the following commands:
``` bash
cd naive_rag/scripts
python launch_services.py
```
Inside the launch_service.py, we will load the default deployment configuration and launch the service accordingly.

The service will be served as a process on the localhost.

### Prepare Dockerfile
In the above section, we deployed the service as a process. Now, we want to deploy each service as a Docker container for better management.

To achieve this, we need to prepare a Dockerfile to build an image for our agent. For example, the Dockerfile for Naive RAG is as follows:


``` Dockerfile
FROM pytorch/pytorch:latest

# download and install fast-service
COPY .cache/fast-service /workspace/fast-service
RUN cd /workspace/fast-service && \
    pip install -r requirements.txt && \
    pip install -e .

# install dependencies for naive_rag
WORKDIR /workspace/naive_rag
COPY requirements.txt requirements.txt
RUN pip install -r requirements.txt

# copy necessary code to docker image
COPY src src
COPY test test
COPY scripts scripts
COPY config config

# add naive_rag to PYTHONPATH
ENV PYTHONPATH=/workspace/naive_rag/src

# set workdir and default command
WORKDIR /workspace/naive_rag/scripts
CMD ["python", "launch_services.py"]
```

Note that when building the image with the Dockerfile, make sure you are under the `naive_rag/` directory and that you have downloaded Fast Service into `naive_rag/.cache`.

``` bash
cd naive_rag/
docker build -t naive-rag:v1 .
```

### Launch Containers
After successfully building the image, we can launch several containers based on the image. Each container is an independent instance of the Naive RAG agent.

Note that when launching a service in a Docker container, we need to update the `vdb_uri` for Milvus and `openai_base_url` for the LLM in the service configuration file. This is necessary because the default configuration file was used for deploying as a process, where VLLM, Milvus, and Naive RAG are deployed in the same VM, sharing the same IP. When deploying Naive RAG inside a container, we need to specify the concrete IP for the LLM (`vdb_uri`) and Milvus (`openai_base_url`). 

For example, if you are using the 4090D server in our lab:

```yaml
... # the same as default settings.yml
vdb_uri: "http://*************:19530"
openai_base_url: "http://*************:8000/v1"
```

Now, let's launch three instances with the following commands:

``` bash
docker run -d --rm --gpus all --name rag-1 -p 20101:20100 -v ./.cache/config/settings.yml:/workspace/naive_rag/config/settings.yml naive-rag:v1
docker run -d --rm --gpus all --name rag-2 -p 20102:20100 -v ./.cache/config/settings.yml:/workspace/naive_rag/config/settings.yml naive-rag:v1
docker run -d --rm --gpus all --name rag-3 -p 20103:20100 -v ./.cache/config/settings.yml:/workspace/naive_rag/config/settings.yml naive-rag:v1
```
where `./.cache/config/settings.yml` is the path to new service configuration file.

### Launch NGINX for load balancer

First, we need to prepare a configuration file for NGINX. An example config is located at `naive_rag/config/nginx.conf` for the three instances launched in the above section:

``` conf
http {
    upstream backend {
        server *************:20101;
        server *************:20102;
        server *************:20103;
    }

    server {
        listen 20100;

        location / {
            proxy_pass http://backend;
        }
    }

    sendfile on;
    client_max_body_size 20M;
}

events {}
```

After that we can launch the load balancer using the following command:
``` bash
docker run -d --rm --name rag-load-balancer -p 20100:20100 -v ./config/nginx.conf:/etc/nginx/nginx.conf:ro nginx
``` 

## Workload Generator of Naive RAG
Besides the development of the agent, we also need to implement a workload generator for testing and benchmarking at `naive_rag/benchmark/load_gen.py`.

Before launching the workload generator, we need to prepare a dataset of workload. For simplicity, we can download some PDF files from the internet and create a `requests.txt` accordingly.

``` bash 
# make sure you are under naive_rag/

# download documents for testing
dir_path=".cache/download/papers"
mkdir -p $dir_path
curl -o $dir_path/paper1.pdf https://www.usenix.org/system/files/osdi24-zhong-yinmin.pdf
curl -o $dir_path/paper2.pdf https://www.usenix.org/system/files/osdi24-sun-biao.pdf
curl -o $dir_path/paper3.pdf https://www.usenix.org/system/files/osdi24-agrawal.pdf
curl -o $dir_path/paper4.pdf https://www.usenix.org/system/files/osdi24-fu.pdf
curl -o $dir_path/paper5.pdf https://www.usenix.org/system/files/osdi24-lee.pdf

# prepare requests.txt for testing
file_paths=()
for file in "$dir_path"/*.pdf; do
    file_path=$(realpath "$file")
    file_paths+=("$file_path")
done
for i in "${!file_paths[@]}"; do
    echo "${i}, rag_e2e, ${file_paths[$i]}" >>.cache/requests.txt
done
```

Then we can run the following command under the `naive_rag/` directory to start the workload generator:
``` bash
python benchmark/load_gen.py -f .cache/requests.txt -c config/naive_rag-client.yml
```

In this command, the `.cache/requests.txt` contains the workload, and `config/naive_rag-client.yml` includes the information of the deployed services, including their IP and port.

## Monitoring
The execution time for each service and sub-module will be automatically recorded and saved to disk by the fast-service, based on the deployment configuration file.
For resource usage monitoring, you need to set up Prometheus, Grafana, cAdvisor, and DCGM Exporter on the servers where the service is deployed.
- If you are deploying services on the 4090D server of our lab, setup is not required as it has already been configured. You can access Prometheus via port 9090, Grafana via port 13000, and cAdvisor via port 8080.
- If you are deploying services on other servers, you can use the script tool/setup_resource_monitor.sh to assist with setting up the monitoring services.