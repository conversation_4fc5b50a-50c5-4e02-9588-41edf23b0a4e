# How to contribute
This document introduce how to contribute a new agent to ai-agent-benchmark.

## Project Structure
Take naive rag for example, the project structure should be like the following format:

- naive_rag/
  - README.md
  - requirements.txt
  - Dockerfile
  - src/
    - naive_rag/
      - `__init__.py`
      - naive_rag.py
      - ...
  - config/
  - test/
  - benchmark/
    - load_gen.py
  - scripts/
    - launch_services.py
    - ...

### Description of the structure
1. README.md: A comprehensive description of the project, including instructions on how to deploy, run, and test the application, as well as how to prepare datasets for testing.
2. requirements.txt: A list of all dependencies required by the project.
3. Dockerfile: The Dockerfile used to build the image for the services.
4. src/naive_rag/: Contains the implementation of the services. This directory should include a `__init__.py` file to designate it as a Python module, along with one or more files (e.g., `naive_rag.py`) that implement the services.
5. config/: Stores default configuration files required for the service and deployment.
6. test/: Contains unit tests for the services to ensure functionality and reliability.
7. benchmark/: Includes a load generator to test the service under realistic workloads, incorporating various request and arrival patterns.
8. scripts/: A collection of scripts (Python, Bash, etc.) for launching dependencies (e.g., vllm, vector store), starting services, and executing tests. 


## Service Implementation
Refer to the [tutorial](./tutorial.md). 

## Dockerfile and Deployment

Besides the implementation, you also need to prepare a Dockerfile for the agent, along with instructions on how to deploy the service as a Docker container.

## Code Format

This project relies on ```black``` to format code.

Install ```black``` on your dev machine.

```shell
pip install black==24.8.0
```

Run ```black``` to format your code.

```shell
black .
```


## Contribution Procedure
1. Create a New Branch
   - Based on the master branch, create a new branch for your changes.
2. Implement/Update Your Agent
   - Make your changes or improvements in the newly created branch.
3. Create a Pull Request (PR)
   - Submit a pull request to merge your updates into the master branch.
4. PR Review and Feedback
   - Collaborate with team members to review the PR, incorporating any feedback for improvements.
5. Approval and Merge
   - Once approved, your changes will be merged into the master branch.

