docker run \
	-d --rm \
	--name hugginggpt \
	-p 20101:20101 \
	-v /home/<USER>/ai-agent-benchmark/hugginggpt/config/settings.yml:/workspace/hugginggpt/config/settings.yml \
	-v /home/<USER>/ai-agent-benchmark/hugginggpt/config/hugginggpt_server.yml:/workspace/hugginggpt/config/hugginggpt_server.yml \
	-v /home/<USER>/ai-agent-benchmark/hugginggpt/config/hugginggpt_config.yml:/workspace/hugginggpt/config/hugginggpt_config.yml \
	-v /home/<USER>/ai-agent-benchmark/hugginggpt/scripts/.cache/:/workspace/hugginggpt/scripts/.cache/ \
	-v /home/<USER>/ai-agent-benchmark/hugginggpt/public/:/workspace/hugginggpt/public/ \
	-v /home/<USER>/ai-agent-benchmark/hugginggpt/benchmark/.cache/images:/workspace/hugginggpt/benchmark/.cache/images \
	hugginggpt:v1