docker run \
	-d \
	--rm	\
	--name hugginggpt_models_server \
	-p 7070:7070 \
    --gpus all  \
	-v /home/<USER>/ai-agent-benchmark/hugginggpt/config/models_server_config.yaml:/workspace/hugginggpt/config/models_server_config.yaml \
    -v /mnt/data/huggingface:/mnt/data/huggingface \
	-v /home/<USER>/ai-agent-benchmark/hugginggpt/public/:/workspace/hugginggpt/public/ \
	-v /home/<USER>/ai-agent-benchmark/hugginggpt/benchmark/.cache/images:/workspace/hugginggpt/benchmark/.cache/images \
    hugginggpt_models_server:v1