import os
import sys
import argparse

from fast_service import (
    FastServiceConfig,
    FastServiceBenchmark,
    FastServiceBenchmarkConfig,
)

abs_path = os.path.abspath(os.path.join(os.path.dirname(__file__), "../src/hugginggpt"))

module_dir = os.path.dirname(abs_path)
if module_dir not in sys.path:
    sys.path.append(module_dir)

from hugginggpt import (
    hugginggpt_fsm,
    hugginggpt_e2e,
    HuggingGPTE2EInput,
    HuggingGPTE2EOutput,
)


def line_to_req(line: str):
    return HuggingGPTE2EInput(collection_name="benchmark", question=line)


def line_to_req_plan(line: str):
    line_parts = line.split(",", 1)
    tasks_to_plan_str = line_parts[0]
    tasks_to_plan = list(map(int, tasks_to_plan_str.split("|")))
    return HuggingGPTE2EInput(
        collection_name="benchmark", question=line, tasks_to_plan=tasks_to_plan
    )


def invoke_service(request):
    y1: HuggingGPTE2EOutput = hugginggpt_e2e(request)
    print(f"y1 => {y1.model_dump()}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "-f", "--file_path", default="./.cache/hugginggpt_hybrid_image_text_data.txt"
    )
    parser.add_argument("--config_file", default="../config/hugginggpt_client.yml")
    parser.add_argument("-m", "--mode", default="poisson")
    parser.add_argument("-n", "--request_num", default=1000, type=int)
    parser.add_argument("-l", "--lambda_rate", default=1.0, type=float)
    parser.add_argument("-p", "--task_plan", action="store_true")
    args = parser.parse_args()

    config_file = args.config_file

    config = FastServiceConfig.load_from_file(os.path.abspath(config_file))
    hugginggpt_fsm.setup_client_mode(config)

    bm_config = FastServiceBenchmarkConfig(
        file_path=args.file_path,
        mode=args.mode,
        request_num=args.request_num,
        lambda_rate=args.lambda_rate,
    )
    if args.task_plan:
        benchmark = FastServiceBenchmark(
            config=bm_config,
            line_to_request=line_to_req_plan,
            invoke_service=invoke_service,
        )
    else:
        benchmark = FastServiceBenchmark(
            config=bm_config, line_to_request=line_to_req, invoke_service=invoke_service
        )

    print("start benchmark")
    benchmark.execute()
    print("end benchmark")
