import os
import threading
import http.server
import socketserver
import argparse


def start_image_server(image_dir: str):
    def _start_image_server_with_dir():
        os.chdir(image_dir)
        handler = http.server.SimpleHTTPRequestHandler
        with socketserver.TCPServer(("127.0.0.1", 8000), handler) as httpd:
            print("Serving at port 8000")
            httpd.serve_forever()

    server_thread = threading.Thread(target=_start_image_server_with_dir)
    server_thread.daemon = True
    server_thread.start()
    try:
        while True:
            pass
    except KeyboardInterrupt:
        print("Server stopped.")


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("-d", "--dir", default="./.cache/images")
    args = parser.parse_args()
    start_image_server(args.dir)
