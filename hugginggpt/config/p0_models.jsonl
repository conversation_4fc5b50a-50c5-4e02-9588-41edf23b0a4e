{"downloads": 16334, "id": "dandelin/vilt-b32-finetuned-vqa", "likes": 86, "pipeline_tag": "visual-question-answering", "task": "visual-question-answering", "meta": {"tags": ["visual-question-answering"], "license": "apache-2.0", "widget": [{"text": "What's the animal doing?", "src": "https://huggingface.co/datasets/mishig/sample_images/resolve/main/tiger.jpg"}, {"text": "What is on top of the building?", "src": "https://huggingface.co/datasets/mishig/sample_images/resolve/main/palace.jpg"}]}, "description": "\n\n# Vision-and-Language Transformer (ViLT), fine-tuned on VQAv2\n\nVision-and-Language Transformer (ViLT) model fine-tuned on [VQAv2](https://visualqa.org/). It was introduced in the paper [ViLT: Vision-and-Language Transformer\nWithout Convolution or Region Supervision](https://arxiv.org/abs/2102.03334) by <PERSON> et al. and first released in [this repository](https://github.com/dandelin/ViLT). \n\nDisclaimer: The team releasing ViLT did not write a model card for this model so this model card has been written by the Hugging Face team.\n\n## Intended uses & limitations\n\nYou can use the raw model for visual question answering. \n\n### How to use\n\nHere is how to use this model in PyTorch:\n\n```python\nfrom transformers import ViltProcessor, ViltForQuestionAnswering\nimport requests\nfrom PIL import Image\n\n# prepare image + question\nurl = \"http://images.cocodataset.org/val2017/000000039769.jpg\"\nimage = Image.open(requests.get(url, stream=True).raw)\ntext = \"How many cats are there?\"\n\nprocessor = ViltProcessor.from_pretrained(\"dandelin/vilt-b32-finetuned-vqa\")\nmodel = ViltForQuestionAnswering.from_pretrained(\"dandelin/vilt-b32-finetuned-vqa\")\n\n# prepare inputs\nencoding = processor(image, text, return_tensors=\"pt\")\n\n# forward pass\noutputs = model(**encoding)\nlogits = outputs.logits\nidx = logits.argmax(-1).item()\nprint(\"Predicted answer:\", model.config.id2label[idx])\n```\n\n## Training data\n\n(to do)\n\n## Training procedure\n\n### Preprocessing\n\n(to do)\n\n### Pretraining\n\n(to do)\n\n## Evaluation results\n\n(to do)\n\n### BibTeX entry and citation info\n\n```bibtex\n@misc{kim2021vilt,\n      title={ViLT: Vision-and-Language Transformer Without Convolution or Region Supervision}, \n      author={Wonjae Kim and Bokyung Son and Ildoo Kim},\n      year={2021},\n      eprint={2102.03334},\n      archivePrefix={arXiv},\n      primaryClass={stat.ML}\n}\n```"}
{"downloads": 9494, "id": "microsoft/speecht5_tts", "likes": 54, "pipeline_tag": "text-to-speech", "task": "text-to-speech", "meta": {"license": "mit", "tags": ["audio", "text-to-speech"], "datasets": ["libritts"]}, "description": "\n\n# SpeechT5 (TTS task)\n\nSpeechT5 model fine-tuned for speech synthesis (text-to-speech) on LibriTTS.\n\nThis model was introduced in [SpeechT5: Unified-Modal Encoder-Decoder Pre-Training for Spoken Language Processing](https://arxiv.org/abs/2110.07205) by Junyi Ao, Rui Wang, Long Zhou, Chengyi Wang, Shuo Ren, Yu Wu, Shujie Liu, Tom Ko, Qing Li, Yu Zhang, Zhihua Wei, Yao Qian, Jinyu Li, Furu Wei.\n\nSpeechT5 was first released in [this repository](https://github.com/microsoft/SpeechT5/), [original weights](https://huggingface.co/mechanicalsea/speecht5-tts). The license used is [MIT](https://github.com/microsoft/SpeechT5/blob/main/LICENSE).\n\n\n\n## Model Description\n\nMotivated by the success of T5 (Text-To-Text Transfer Transformer) in pre-trained natural language processing models, we propose a unified-modal SpeechT5 framework that explores the encoder-decoder pre-training for self-supervised speech/text representation learning. The SpeechT5 framework consists of a shared encoder-decoder network and six modal-specific (speech/text) pre/post-nets. After preprocessing the input speech/text through the pre-nets, the shared encoder-decoder network models the sequence-to-sequence transformation, and then the post-nets generate the output in the speech/text modality based on the output of the decoder.\n\nLeveraging large-scale unlabeled speech and text data, we pre-train SpeechT5 to learn a unified-modal representation, hoping to improve the modeling capability for both speech and text. To align the textual and speech information into this unified semantic space, we propose a cross-modal vector quantization approach that randomly mixes up speech/text states with latent units as the interface between encoder and decoder.\n\nExtensive evaluations show the superiority of the proposed SpeechT5 framework on a wide variety of spoken language processing tasks, including automatic speech recognition, speech synthesis, speech translation, voice conversion, speech enhancement, and speaker identification.\n\n- **Developed by:** Junyi Ao, Rui Wang, Long Zhou, Chengyi Wang, Shuo Ren, Yu Wu, Shujie Liu, Tom Ko, Qing Li, Yu Zhang, Zhihua Wei, Yao Qian, Jinyu Li, Furu Wei.\n- **Shared by [optional]:** [Matthijs Hollemans](https://huggingface.co/Matthijs)\n- **Model type:** text-to-speech\n- **Language(s) (NLP):** [More Information Needed]\n- **License:** [MIT](https://github.com/microsoft/SpeechT5/blob/main/LICENSE)\n- **Finetuned from model [optional]:** [More Information Needed]\n\n\n## Model Sources [optional]\n\n<!-- Provide the basic links for the model. -->\n\n- **Repository:** [https://github.com/microsoft/SpeechT5/]\n- **Paper:** [https://arxiv.org/pdf/2110.07205.pdf]\n- **Blog Post:** [https://huggingface.co/blog/speecht5]\n- **Demo:** [https://huggingface.co/spaces/Matthijs/speecht5-tts-demo]\n\n\n# Uses\n\n<!-- Address questions around how the model is intended to be used, including the foreseeable users of the model and those affected by the model. -->\n\n## Direct Use\n\n<!-- This section is for the model use without fine-tuning or plugging into a larger ecosystem/app. -->\n\nYou can use this model for speech synthesis. See the [model hub](https://huggingface.co/models?search=speecht5) to look for fine-tuned versions on a task that interests you.\n\n## Downstream Use [optional]\n\n<!-- This section is for the model use when fine-tuned for a task, or when plugged into a larger ecosystem/app -->\n\n[More Information Needed]\n\n## Out-of-Scope Use\n\n<!-- This section addresses misuse, malicious use, and uses that the model will not work well for. -->\n\n[More Information Needed]\n\n# Bias, Risks, and Limitations\n\n<!-- This section is meant to convey both technical and sociotechnical limitations. -->\n\n[More Information Needed]\n\n## Recommendations\n\n<!-- This section is meant to convey recommendations with respect to the bias, risk, and technical limitations. -->\n\nUsers (both direct and downstream) should be made aware of the risks, biases and limitations of the model. More information needed for further recommendations.\n\n\n## How to Get Started With the Model\n\nUse the code below to convert text into a mono 16 kHz speech waveform.\n\n```python\n# Following pip packages need to be installed:\n# !pip install git+https://github.com/huggingface/transformers sentencepiece datasets\n\nfrom transformers import SpeechT5Processor, SpeechT5ForTextToSpeech, SpeechT5HifiGan\nfrom datasets import load_dataset\nimport torch\nimport soundfile as sf\nfrom datasets import load_dataset\n\nprocessor = SpeechT5Processor.from_pretrained(\"microsoft/speecht5_tts\")\nmodel = SpeechT5ForTextToSpeech.from_pretrained(\"microsoft/speecht5_tts\")\nvocoder = SpeechT5HifiGan.from_pretrained(\"microsoft/speecht5_hifigan\")\n\ninputs = processor(text=\"Hello, my dog is cute\", return_tensors=\"pt\")\n\n# load xvector containing speaker's voice characteristics from a dataset\nembeddings_dataset = load_dataset(\"Matthijs/cmu-arctic-xvectors\", split=\"validation\")\nspeaker_embeddings = torch.tensor(embeddings_dataset[7306][\"xvector\"]).unsqueeze(0)\n\nspeech = model.generate_speech(inputs[\"input_ids\"], speaker_embeddings, vocoder=vocoder)\n\nsf.write(\"speech.wav\", speech.numpy(), samplerate=16000)\n```\n\n# Training Details\n\n## Training Data\n\n<!-- This should link to a Data Card, perhaps with a short stub of information on what the training data is all about as well as documentation related to data pre-processing or additional filtering. -->\n\nLibriTTS\n\n## Training Procedure \n\n<!-- This relates heavily to the Technical Specifications. Content here should link to that section when it is relevant to the training procedure. -->\n\n### Preprocessing [optional]\n\nLeveraging large-scale unlabeled speech and text data, we pre-train SpeechT5 to learn a unified-modal representation, hoping to improve the modeling capability for both speech and text.\n\n\n### Training hyperparameters\n- **Precision:** [More Information Needed] <!--fp16, bf16, fp8, fp32 -->\n- **Regime:** [More Information Needed] <!--mixed precision or not -->\n\n### Speeds, Sizes, Times [optional]\n\n<!-- This section provides information about throughput, start/end time, checkpoint size if relevant, etc. -->\n\n[More Information Needed]\n\n# Evaluation\n\n<!-- This section describes the evaluation protocols and provides the results. -->\n\n## Testing Data, Factors & Metrics\n\n### Testing Data\n\n<!-- This should link to a Data Card if possible. -->\n\n[More Information Needed]\n\n### Factors\n\n<!-- These are the things the evaluation is disaggregating by, e.g., subpopulations or domains. -->\n\n[More Information Needed]\n\n### Metrics\n\n<!-- These are the evaluation metrics being used, ideally with a description of why. -->\n\n[More Information Needed]\n\n## Results\n\n[More Information Needed]\n\n### Summary\n\n\n\n# Model Examination [optional]\n\n<!-- Relevant interpretability work for the model goes here -->\n\nExtensive evaluations show the superiority of the proposed SpeechT5 framework on a wide variety of spoken language processing tasks, including automatic speech recognition, speech synthesis, speech translation, voice conversion, speech enhancement, and speaker identification.\n\n# Environmental Impact\n\n<!-- Total emissions (in grams of CO2eq) and additional considerations, such as electricity usage, go here. Edit the suggested text below accordingly -->\n\nCarbon emissions can be estimated using the [Machine Learning Impact calculator](https://mlco2.github.io/impact#compute) presented in [Lacoste et al. (2019)](https://arxiv.org/abs/1910.09700).\n\n- **Hardware Type:** [More Information Needed]\n- **Hours used:** [More Information Needed]\n- **Cloud Provider:** [More Information Needed]\n- **Compute Region:** [More Information Needed]\n- **Carbon Emitted:** [More Information Needed]\n\n# Technical Specifications [optional]\n\n## Model Architecture and Objective\n\nThe SpeechT5 framework consists of a shared encoder-decoder network and six modal-specific (speech/text) pre/post-nets.\n\nAfter preprocessing the input speech/text through the pre-nets, the shared encoder-decoder network models the sequence-to-sequence transformation, and then the post-nets generate the output in the speech/text modality based on the output of the decoder.\n\n## Compute Infrastructure\n\n[More Information Needed]\n\n### Hardware\n\n[More Information Needed]\n\n### Software\n\n[More Information Needed]\n\n# Citation [optional]\n\n<!-- If there is a paper or blog post introducing the model, the APA and Bibtex information for that should go in this section. -->\n\n**BibTeX:**\n\n```bibtex\n@inproceedings{ao-etal-2022-speecht5,\n    title = {{S}peech{T}5: Unified-Modal Encoder-Decoder Pre-Training for Spoken Language Processing},\n    author = {Ao, Junyi and Wang, Rui and Zhou, Long and Wang, Chengyi and Ren, Shuo and Wu, Yu and Liu, Shujie and Ko, Tom and Li, Qing and Zhang, Yu and Wei, Zhihua and Qian, Yao and Li, Jinyu and Wei, Furu},\n    booktitle = {Proceedings of the 60th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers)},\n    month = {May},\n    year = {2022},\n    pages={5723--5738},\n}\n```\n\n# Glossary [optional]\n\n<!-- If relevant, include terms and calculations in this section that can help readers understand the model or model card. -->\n\n- **text-to-speech** to synthesize audio\n\n# More Information [optional]\n\n[More Information Needed]\n\n# Model Card Authors [optional]\n\nDisclaimer: The team releasing SpeechT5 did not write a model card for this model so this model card has been written by the Hugging Face team.\n\n# Model Card Contact\n\n[More Information Needed]\n\n\n\n"}
{"downloads": 56582, "id": "facebook/detr-resnet-101", "likes": 30, "pipeline_tag": "object-detection", "task": "object-detection", "meta": {"license": "apache-2.0", "tags": ["object-detection", "vision"], "datasets": ["coco"], "widget": [{"src": "https://huggingface.co/datasets/mishig/sample_images/resolve/main/savanna.jpg", "example_title": "Savanna"}, {"src": "https://huggingface.co/datasets/mishig/sample_images/resolve/main/football-match.jpg", "example_title": "Football Match"}, {"src": "https://huggingface.co/datasets/mishig/sample_images/resolve/main/airport.jpg", "example_title": "Airport"}]}, "description": "\n\n# DETR (End-to-End Object Detection) model with ResNet-101 backbone\n\nDEtection TRansformer (DETR) model trained end-to-end on COCO 2017 object detection (118k annotated images). It was introduced in the paper [End-to-End Object Detection with Transformers](https://arxiv.org/abs/2005.12872) by Carion et al. and first released in [this repository](https://github.com/facebookresearch/detr). \n\nDisclaimer: The team releasing DETR did not write a model card for this model so this model card has been written by the Hugging Face team.\n\n## Model description\n\nThe DETR model is an encoder-decoder transformer with a convolutional backbone. Two heads are added on top of the decoder outputs in order to perform object detection: a linear layer for the class labels and a MLP (multi-layer perceptron) for the bounding boxes. The model uses so-called object queries to detect objects in an image. Each object query looks for a particular object in the image. For COCO, the number of object queries is set to 100. \n\nThe model is trained using a \"bipartite matching loss\": one compares the predicted classes + bounding boxes of each of the N = 100 object queries to the ground truth annotations, padded up to the same length N (so if an image only contains 4 objects, 96 annotations will just have a \"no object\" as class and \"no bounding box\" as bounding box). The Hungarian matching algorithm is used to create an optimal one-to-one mapping between each of the N queries and each of the N annotations. Next, standard cross-entropy (for the classes) and a linear combination of the L1 and generalized IoU loss (for the bounding boxes) are used to optimize the parameters of the model.\n\n![model image](https://huggingface.co/datasets/huggingface/documentation-images/resolve/main/transformers/model_doc/detr_architecture.png)\n\n## Intended uses & limitations\n\nYou can use the raw model for object detection. See the [model hub](https://huggingface.co/models?search=facebook/detr) to look for all available DETR models.\n\n### How to use\n\nHere is how to use this model:\n\n```python\nfrom transformers import DetrImageProcessor, DetrForObjectDetection\nimport torch\nfrom PIL import Image\nimport requests\n\nurl = \"http://images.cocodataset.org/val2017/000000039769.jpg\"\nimage = Image.open(requests.get(url, stream=True).raw)\n\nprocessor = DetrImageProcessor.from_pretrained(\"facebook/detr-resnet-101\")\nmodel = DetrForObjectDetection.from_pretrained(\"facebook/detr-resnet-101\")\n\ninputs = processor(images=image, return_tensors=\"pt\")\noutputs = model(**inputs)\n\n# convert outputs (bounding boxes and class logits) to COCO API\n# let's only keep detections with score > 0.9\ntarget_sizes = torch.tensor([image.size[::-1]])\nresults = processor.post_process_object_detection(outputs, target_sizes=target_sizes, threshold=0.9)[0]\n\nfor score, label, box in zip(results[\"scores\"], results[\"labels\"], results[\"boxes\"]):\n    box = [round(i, 2) for i in box.tolist()]\n    print(\n            f\"Detected {model.config.id2label[label.item()]} with confidence \"\n            f\"{round(score.item(), 3)} at location {box}\"\n    )\n```\nThis should output (something along the lines of):\n```\nDetected cat with confidence 0.998 at location [344.06, 24.85, 640.34, 373.74]\nDetected remote with confidence 0.997 at location [328.13, 75.93, 372.81, 187.66]\nDetected remote with confidence 0.997 at location [39.34, 70.13, 175.56, 118.78]\nDetected cat with confidence 0.998 at location [15.36, 51.75, 316.89, 471.16]\nDetected couch with confidence 0.995 at location [-0.19, 0.71, 639.73, 474.17]\n```\n\nCurrently, both the feature extractor and model support PyTorch. \n\n## Training data\n\nThe DETR model was trained on [COCO 2017 object detection](https://cocodataset.org/#download), a dataset consisting of 118k/5k annotated images for training/validation respectively. \n\n## Training procedure\n\n### Preprocessing\n\nThe exact details of preprocessing of images during training/validation can be found [here](https://github.com/google-research/vision_transformer/blob/master/vit_jax/input_pipeline.py). \n\nImages are resized/rescaled such that the shortest side is at least 800 pixels and the largest side at most 1333 pixels, and normalized across the RGB channels with the ImageNet mean (0.485, 0.456, 0.406) and standard deviation (0.229, 0.224, 0.225).\n\n### Training\n\nThe model was trained for 300 epochs on 16 V100 GPUs. This takes 3 days, with 4 images per GPU (hence a total batch size of 64).\n\n## Evaluation results\n\nThis model achieves an AP (average precision) of **43.5** on COCO 2017 validation. For more details regarding evaluation results, we refer to table 1 of the original paper.\n### BibTeX entry and citation info\n\n```bibtex\n@article{DBLP:journals/corr/abs-2005-12872,\n  author    = {Nicolas Carion and\n               Francisco Massa and\n               Gabriel Synnaeve and\n               Nicolas Usunier and\n               Alexander Kirillov and\n               Sergey Zagoruyko},\n  title     = {End-to-End Object Detection with Transformers},\n  journal   = {CoRR},\n  volume    = {abs/2005.12872},\n  year      = {2020},\n  url       = {https://arxiv.org/abs/2005.12872},\n  archivePrefix = {arXiv},\n  eprint    = {2005.12872},\n  timestamp = {Thu, 28 May 2020 17:38:09 +0200},\n  biburl    = {https://dblp.org/rec/journals/corr/abs-2005-12872.bib},\n  bibsource = {dblp computer science bibliography, https://dblp.org}\n}\n```"}
{"downloads": 34203, "id": "Salesforce/blip-vqa-base", "likes": 18, "pipeline_tag": "visual-question-answering", "task": "visual-question-answering", "meta": {"pipeline_tag": "visual-question-answering", "tags": ["visual-question-answering"], "inference": false, "languages": ["en"], "license": "bsd-3-clause"}, "description": "\n\n# BLIP: Bootstrapping Language-Image Pre-training for Unified Vision-Language Understanding and Generation\n\nModel card for BLIP trained on visual question answering- base architecture (with ViT base backbone).\n\n| ![BLIP.gif](https://s3.amazonaws.com/moonup/production/uploads/1670928184033-62441d1d9fdefb55a0b7d12c.gif) |\n|:--:|\n| <b> Pull figure from BLIP official repo | Image source: https://github.com/salesforce/BLIP </b>|\n\n## TL;DR\n\nAuthors from the [paper](https://arxiv.org/abs/2201.12086) write in the abstract:\n\n*Vision-Language Pre-training (VLP) has advanced the performance for many vision-language tasks. However, most existing pre-trained models only excel in either understanding-based tasks or generation-based tasks. Furthermore, performance improvement has been largely achieved by scaling up the dataset with noisy image-text pairs collected from the web, which is a suboptimal source of supervision. In this paper, we propose BLIP, a new VLP framework which transfers flexibly to both vision-language understanding and generation tasks. BLIP effectively utilizes the noisy web data by bootstrapping the captions, where a captioner generates synthetic captions and a filter removes the noisy ones. We achieve state-of-the-art results on a wide range of vision-language tasks, such as image-text retrieval (+2.7% in average recall@1), image captioning (+2.8% in CIDEr), and VQA (+1.6% in VQA score). BLIP also demonstrates strong generalization ability when directly transferred to videolanguage tasks in a zero-shot manner. Code, models, and datasets are released.*\n\n## Usage\n\nYou can use this model for conditional and un-conditional image captioning\n\n### Using the Pytorch model\n\n#### Running the model on CPU\n\n<details>\n<summary> Click to expand </summary>\n\n```python\nimport requests\nfrom PIL import Image\nfrom transformers import BlipProcessor, BlipForQuestionAnswering\n\nprocessor = BlipProcessor.from_pretrained(\"Salesforce/blip-vqa-base\")\nmodel = BlipForQuestionAnswering.from_pretrained(\"Salesforce/blip-vqa-base\")\n\nimg_url = 'https://storage.googleapis.com/sfr-vision-language-research/BLIP/demo.jpg' \nraw_image = Image.open(requests.get(img_url, stream=True).raw).convert('RGB')\n\nquestion = \"how many dogs are in the picture?\"\ninputs = processor(raw_image, question, return_tensors=\"pt\")\n\nout = model.generate(**inputs)\nprint(processor.decode(out[0], skip_special_tokens=True))\n>>> 1\n```\n</details>\n\n#### Running the model on GPU\n\n##### In full precision \n\n<details>\n<summary> Click to expand </summary>\n\n```python\nimport requests\nfrom PIL import Image\nfrom transformers import BlipProcessor, BlipForQuestionAnswering\n\nprocessor = BlipProcessor.from_pretrained(\"Salesforce/blip-vqa-base\")\nmodel = BlipForQuestionAnswering.from_pretrained(\"Salesforce/blip-vqa-base\").to(\"cuda\")\n\nimg_url = 'https://storage.googleapis.com/sfr-vision-language-research/BLIP/demo.jpg' \nraw_image = Image.open(requests.get(img_url, stream=True).raw).convert('RGB')\n\nquestion = \"how many dogs are in the picture?\"\ninputs = processor(raw_image, question, return_tensors=\"pt\").to(\"cuda\")\n\nout = model.generate(**inputs)\nprint(processor.decode(out[0], skip_special_tokens=True))\n>>> 1\n```\n</details>\n\n##### In half precision (`float16`)\n\n<details>\n<summary> Click to expand </summary>\n\n```python\nimport torch\nimport requests\nfrom PIL import Image\nfrom transformers import BlipProcessor, BlipForQuestionAnswering\n\nprocessor = BlipProcessor.from_pretrained(\"ybelkada/blip-vqa-base\")\nmodel = BlipForQuestionAnswering.from_pretrained(\"ybelkada/blip-vqa-base\", torch_dtype=torch.float16).to(\"cuda\")\n\nimg_url = 'https://storage.googleapis.com/sfr-vision-language-research/BLIP/demo.jpg' \nraw_image = Image.open(requests.get(img_url, stream=True).raw).convert('RGB')\n\nquestion = \"how many dogs are in the picture?\"\ninputs = processor(raw_image, question, return_tensors=\"pt\").to(\"cuda\", torch.float16)\n\nout = model.generate(**inputs)\nprint(processor.decode(out[0], skip_special_tokens=True))\n>>> 1\n```\n</details>\n\n## BibTex and citation info\n\n```\n@misc{https://doi.org/10.48550/arxiv.2201.12086,\n  doi = {10.48550/ARXIV.2201.12086},\n  \n  url = {https://arxiv.org/abs/2201.12086},\n  \n  author = {Li, Junnan and Li, Dongxu and Xiong, Caiming and Hoi, Steven},\n  \n  keywords = {Computer Vision and Pattern Recognition (cs.CV), FOS: Computer and information sciences, FOS: Computer and information sciences},\n  \n  title = {BLIP: Bootstrapping Language-Image Pre-training for Unified Vision-Language Understanding and Generation},\n  \n  publisher = {arXiv},\n  \n  year = {2022},\n  \n  copyright = {Creative Commons Attribution 4.0 International}\n}\n```"}
{"downloads": 5187, "id": "facebook/detr-resnet-50-panoptic", "likes": 61, "pipeline_tag": "image-segmentation", "task": "image-segmentation", "meta": {"license": "apache-2.0", "tags": ["image-segmentation", "vision"], "datasets": ["coco"], "widget": [{"src": "https://huggingface.co/datasets/mishig/sample_images/resolve/main/football-match.jpg", "example_title": "Football Match"}, {"src": "https://huggingface.co/datasets/mishig/sample_images/resolve/main/dog-cat.jpg", "example_title": "Dog & Cat"}, {"src": "https://huggingface.co/datasets/mishig/sample_images/resolve/main/construction-site.jpg", "example_title": "Construction Site"}, {"src": "https://huggingface.co/datasets/mishig/sample_images/resolve/main/apple-orange.jpg", "example_title": "Apple & Orange"}]}, "description": "\n\n# DETR (End-to-End Object Detection) model with ResNet-50 backbone\n\nDEtection TRansformer (DETR) model trained end-to-end on COCO 2017 panoptic (118k annotated images). It was introduced in the paper [End-to-End Object Detection with Transformers](https://arxiv.org/abs/2005.12872) by Carion et al. and first released in [this repository](https://github.com/facebookresearch/detr). \n\nDisclaimer: The team releasing DETR did not write a model card for this model so this model card has been written by the Hugging Face team.\n\n## Model description\n\nThe DETR model is an encoder-decoder transformer with a convolutional backbone. Two heads are added on top of the decoder outputs in order to perform object detection: a linear layer for the class labels and a MLP (multi-layer perceptron) for the bounding boxes. The model uses so-called object queries to detect objects in an image. Each object query looks for a particular object in the image. For COCO, the number of object queries is set to 100. \n\nThe model is trained using a \"bipartite matching loss\": one compares the predicted classes + bounding boxes of each of the N = 100 object queries to the ground truth annotations, padded up to the same length N (so if an image only contains 4 objects, 96 annotations will just have a \"no object\" as class and \"no bounding box\" as bounding box). The Hungarian matching algorithm is used to create an optimal one-to-one mapping between each of the N queries and each of the N annotations. Next, standard cross-entropy (for the classes) and a linear combination of the L1 and generalized IoU loss (for the bounding boxes) are used to optimize the parameters of the model.\n\nDETR can be naturally extended to perform panoptic segmentation, by adding a mask head on top of the decoder outputs.\n\n![model image](https://huggingface.co/datasets/huggingface/documentation-images/resolve/main/transformers/model_doc/detr_architecture.png)\n\n## Intended uses & limitations\n\nYou can use the raw model for panoptic segmentation. See the [model hub](https://huggingface.co/models?search=facebook/detr) to look for all available DETR models.\n\n### How to use\n\nHere is how to use this model:\n\n```python\nimport io\nimport requests\nfrom PIL import Image\nimport torch\nimport numpy\n\nfrom transformers import DetrFeatureExtractor, DetrForSegmentation\nfrom transformers.models.detr.feature_extraction_detr import rgb_to_id\n\nurl = \"http://images.cocodataset.org/val2017/000000039769.jpg\"\nimage = Image.open(requests.get(url, stream=True).raw)\n\nfeature_extractor = DetrFeatureExtractor.from_pretrained(\"facebook/detr-resnet-50-panoptic\")\nmodel = DetrForSegmentation.from_pretrained(\"facebook/detr-resnet-50-panoptic\")\n\n# prepare image for the model\ninputs = feature_extractor(images=image, return_tensors=\"pt\")\n\n# forward pass\noutputs = model(**inputs)\n\n# use the `post_process_panoptic` method of `DetrFeatureExtractor` to convert to COCO format\nprocessed_sizes = torch.as_tensor(inputs[\"pixel_values\"].shape[-2:]).unsqueeze(0)\nresult = feature_extractor.post_process_panoptic(outputs, processed_sizes)[0]\n\n# the segmentation is stored in a special-format png\npanoptic_seg = Image.open(io.BytesIO(result[\"png_string\"]))\npanoptic_seg = numpy.array(panoptic_seg, dtype=numpy.uint8)\n# retrieve the ids corresponding to each mask\npanoptic_seg_id = rgb_to_id(panoptic_seg)\n```\n\nCurrently, both the feature extractor and model support PyTorch. \n\n## Training data\n\nThe DETR model was trained on [COCO 2017 panoptic](https://cocodataset.org/#download), a dataset consisting of 118k/5k annotated images for training/validation respectively. \n\n## Training procedure\n\n### Preprocessing\n\nThe exact details of preprocessing of images during training/validation can be found [here](https://github.com/facebookresearch/detr/blob/master/datasets/coco_panoptic.py). \n\nImages are resized/rescaled such that the shortest side is at least 800 pixels and the largest side at most 1333 pixels, and normalized across the RGB channels with the ImageNet mean (0.485, 0.456, 0.406) and standard deviation (0.229, 0.224, 0.225).\n\n### Training\n\nThe model was trained for 300 epochs on 16 V100 GPUs. This takes 3 days, with 4 images per GPU (hence a total batch size of 64).\n\n## Evaluation results\n\nThis model achieves the following results on COCO 2017 validation: a box AP (average precision) of **38.8**, a segmentation AP (average precision) of **31.1** and a PQ (panoptic quality) of **43.4**.\n\nFor more details regarding evaluation results, we refer to table 5 of the original paper.\n\n### BibTeX entry and citation info\n\n```bibtex\n@article{DBLP:journals/corr/abs-2005-12872,\n  author    = {Nicolas Carion and\n               Francisco Massa and\n               Gabriel Synnaeve and\n               Nicolas Usunier and\n               Alexander Kirillov and\n               Sergey Zagoruyko},\n  title     = {End-to-End Object Detection with Transformers},\n  journal   = {CoRR},\n  volume    = {abs/2005.12872},\n  year      = {2020},\n  url       = {https://arxiv.org/abs/2005.12872},\n  archivePrefix = {arXiv},\n  eprint    = {2005.12872},\n  timestamp = {Thu, 28 May 2020 17:38:09 +0200},\n  biburl    = {https://dblp.org/rec/journals/corr/abs-2005-12872.bib},\n  bibsource = {dblp computer science bibliography, https://dblp.org}\n}\n```"}
{"downloads": 1006245, "id": "impira/layoutlm-document-qa", "likes": 174, "pipeline_tag": "document-question-answering", "task": "document-question-answering", "meta": {"language": "en", "license": "mit", "pipeline_tag": "document-question-answering", "tags": ["layoutlm", "document-question-answering", "pdf"], "widget": [{"text": "What is the invoice number?", "src": "https://huggingface.co/spaces/impira/docquery/resolve/2359223c1837a7587402bda0f2643382a6eefeab/invoice.png"}, {"text": "What is the purchase amount?", "src": "https://huggingface.co/spaces/impira/docquery/resolve/2359223c1837a7587402bda0f2643382a6eefeab/contract.jpeg"}]}, "description": "\n\n# LayoutLM for Visual Question Answering\n\nThis is a fine-tuned version of the multi-modal [LayoutLM](https://aka.ms/layoutlm) model for the task of question answering on documents. It has been fine-tuned using both the [SQuAD2.0](https://huggingface.co/datasets/squad_v2) and [DocVQA](https://www.docvqa.org/) datasets.\n\n## Getting started with the model\n\nTo run these examples, you must have [PIL](https://pillow.readthedocs.io/en/stable/installation.html), [pytesseract](https://pypi.org/project/pytesseract/), and [PyTorch](https://pytorch.org/get-started/locally/) installed in addition to [transformers](https://huggingface.co/docs/transformers/index).\n\n```python\nfrom transformers import pipeline\n\nnlp = pipeline(\n    \"document-question-answering\",\n    model=\"impira/layoutlm-document-qa\",\n)\n\nnlp(\n    \"https://templates.invoicehome.com/invoice-template-us-neat-750px.png\",\n    \"What is the invoice number?\"\n)\n# {'score': 0.9943977, 'answer': 'us-001', 'start': 15, 'end': 15}\n\nnlp(\n    \"https://miro.medium.com/max/787/1*iECQRIiOGTmEFLdWkVIH2g.jpeg\",\n    \"What is the purchase amount?\"\n)\n# {'score': 0.9912159, 'answer': '$1,000,000,000', 'start': 97, 'end': 97}\n\nnlp(\n    \"https://www.accountingcoach.com/wp-content/uploads/2013/10/<EMAIL>\",\n    \"What are the 2020 net sales?\"\n)\n# {'score': 0.********, 'answer': '$ 3,750', 'start': 19, 'end': 20}\n```\n\n**NOTE**: This model and pipeline was recently landed in transformers via [PR #18407](https://github.com/huggingface/transformers/pull/18407) and [PR #18414](https://github.com/huggingface/transformers/pull/18414), so you'll need to use a recent version of transformers, for example:\n\n```bash\npip install git+https://github.com/huggingface/transformers.git@2ef774211733f0acf8d3415f9284c49ef219e991\n```\n\n## About us\n\nThis model was created by the team at [Impira](https://www.impira.com/).\n"}
{"downloads": 161997, "id": "microsoft/beit-base-patch16-224-pt22k-ft22k", "likes": 37, "pipeline_tag": "image-classification", "task": "image-classification", "meta": {"license": "apache-2.0", "tags": ["image-classification", "vision"], "datasets": ["imagenet", "imagenet-21k"]}, "description": "\n\n# BEiT (base-sized model, fine-tuned on ImageNet-22k) \n\nBEiT model pre-trained in a self-supervised fashion on ImageNet-22k - also called ImageNet-21k (14 million images, 21,841 classes) at resolution 224x224, and fine-tuned on the same dataset at resolution 224x224. It was introduced in the paper [BEIT: BERT Pre-Training of Image Transformers](https://arxiv.org/abs/2106.08254) by Hangbo Bao, Li Dong and Furu Wei and first released in [this repository](https://github.com/microsoft/unilm/tree/master/beit). \n\nDisclaimer: The team releasing BEiT did not write a model card for this model so this model card has been written by the Hugging Face team.\n\n## Model description\n\nThe BEiT model is a Vision Transformer (ViT), which is a transformer encoder model (BERT-like). In contrast to the original ViT model, BEiT is pretrained on a large collection of images in a self-supervised fashion, namely ImageNet-21k, at a resolution of 224x224 pixels. The pre-training objective for the model is to predict visual tokens from the encoder of OpenAI's DALL-E's VQ-VAE, based on masked patches.\nNext, the model was fine-tuned in a supervised fashion on ImageNet (also referred to as ILSVRC2012), a dataset comprising 1 million images and 1,000 classes, also at resolution 224x224.\n\nImages are presented to the model as a sequence of fixed-size patches (resolution 16x16), which are linearly embedded. Contrary to the original ViT models, BEiT models do use relative position embeddings (similar to T5) instead of absolute position embeddings, and perform classification of images by mean-pooling the final hidden states of the patches, instead of placing a linear layer on top of the final hidden state of the [CLS] token.\n\nBy pre-training the model, it learns an inner representation of images that can then be used to extract features useful for downstream tasks: if you have a dataset of labeled images for instance, you can train a standard classifier by placing a linear layer on top of the pre-trained encoder. One typically places a linear layer on top of the [CLS] token, as the last hidden state of this token can be seen as a representation of an entire image. Alternatively, one can mean-pool the final hidden states of the patch embeddings, and place a linear layer on top of that.\n\n## Intended uses & limitations\n\nYou can use the raw model for image classification. See the [model hub](https://huggingface.co/models?search=microsoft/beit) to look for\nfine-tuned versions on a task that interests you.\n\n### How to use\n\nHere is how to use this model to classify an image of the COCO 2017 dataset into one of the 1,000 ImageNet classes:\n\n```python\nfrom transformers import BeitImageProcessor, BeitForImageClassification\nfrom PIL import Image\nimport requests\n\nurl = 'http://images.cocodataset.org/val2017/000000039769.jpg'\nimage = Image.open(requests.get(url, stream=True).raw)\n\nprocessor = BeitImageProcessor.from_pretrained('microsoft/beit-base-patch16-224-pt22k-ft22k')\nmodel = BeitForImageClassification.from_pretrained('microsoft/beit-base-patch16-224-pt22k-ft22k')\n\ninputs = processor(images=image, return_tensors=\"pt\")\noutputs = model(**inputs)\nlogits = outputs.logits\n# model predicts one of the 21,841 ImageNet-22k classes\npredicted_class_idx = logits.argmax(-1).item()\nprint(\"Predicted class:\", model.config.id2label[predicted_class_idx])\n```\n\nCurrently, both the feature extractor and model support PyTorch.\n\n## Training data\n\nThe BEiT model was pretrained on [ImageNet-21k](http://www.image-net.org/), a dataset consisting of 14 million images and 21k classes, and fine-tuned on the same dataset.\n\n## Training procedure\n\n### Preprocessing\n\nThe exact details of preprocessing of images during training/validation can be found [here](https://github.com/microsoft/unilm/blob/master/beit/datasets.py). \n\nImages are resized/rescaled to the same resolution (224x224) and normalized across the RGB channels with mean (0.5, 0.5, 0.5) and standard deviation (0.5, 0.5, 0.5).\n\n### Pretraining\n\nFor all pre-training related hyperparameters, we refer to page 15 of the [original paper](https://arxiv.org/abs/2106.08254).\n\n## Evaluation results\n\nFor evaluation results on several image classification benchmarks, we refer to tables 1 and 2 of the original paper. Note that for fine-tuning, the best results are obtained with a higher resolution. Of course, increasing the model size will result in better performance.\n\n### BibTeX entry and citation info\n\n```@article{DBLP:journals/corr/abs-2106-08254,\n  author    = {Hangbo Bao and\n               Li Dong and\n               Furu Wei},\n  title     = {BEiT: {BERT} Pre-Training of Image Transformers},\n  journal   = {CoRR},\n  volume    = {abs/2106.08254},\n  year      = {2021},\n  url       = {https://arxiv.org/abs/2106.08254},\n  archivePrefix = {arXiv},\n  eprint    = {2106.08254},\n  timestamp = {Tue, 29 Jun 2021 16:55:04 +0200},\n  biburl    = {https://dblp.org/rec/journals/corr/abs-2106-08254.bib},\n  bibsource = {dblp computer science bibliography, https://dblp.org}\n}\n```\n\n```bibtex\n@inproceedings{deng2009imagenet,\n  title={Imagenet: A large-scale hierarchical image database},\n  author={Deng, Jia and Dong, Wei and Socher, Richard and Li, Li-Jia and Li, Kai and Fei-Fei, Li},\n  booktitle={2009 IEEE conference on computer vision and pattern recognition},\n  pages={248--255},\n  year={2009},\n  organization={Ieee}\n}\n```"}
{"downloads": 19299, "id": "lambdalabs/sd-image-variations-diffusers", "likes": 186, "pipeline_tag": "image-to-image", "task": "image-to-image", "meta": {"thumbnail": "https://repository-images.githubusercontent.com/523487884/fdb03a69-8353-4387-b5fc-0d85f888a63f", "datasets": ["ChristophSchuhmann/improved_aesthetics_6plus"], "license": "creativeml-openrail-m", "tags": ["stable-diffusion", "stable-diffusion-diffusers", "image-to-image"]}, "description": "\n\n# Stable Diffusion Image Variations Model Card\n\n\ud83d\udce3 V2 model released, and blurriness issues fixed! \ud83d\udce3\n\n\ud83e\udde8\ud83c\udf89 Image Variations is now natively supported in \ud83e\udd17 Diffusers! \ud83c\udf89\ud83e\udde8\n\n![](https://raw.githubusercontent.com/justinpinkney/stable-diffusion/main/assets/im-vars-thin.jpg)\n\n## Version 2\n\nThis version of Stable Diffusion has been fine tuned from [CompVis/stable-diffusion-v1-4-original](https://huggingface.co/CompVis/stable-diffusion-v-1-4-original) to accept CLIP image embedding rather than text embeddings. This allows the creation of \"image variations\" similar to DALLE-2 using Stable Diffusion. This version of the weights has been ported to huggingface Diffusers, to use this with the Diffusers library requires the [Lambda Diffusers repo](https://github.com/LambdaLabsML/lambda-diffusers).\n\nThis model was trained in two stages and longer than the original variations model and gives better image quality and better CLIP rated similarity compared to the original version\n\nSee training details and v1 vs v2 comparison below.\n\n\n## Example\n\nMake sure you are using a version of Diffusers >=0.8.0 (for older version see the old instructions at the bottom of this model card)\n\n```python\nfrom diffusers import StableDiffusionImageVariationPipeline\nfrom PIL import Image\n\ndevice = \"cuda:0\"\nsd_pipe = StableDiffusionImageVariationPipeline.from_pretrained(\n  \"lambdalabs/sd-image-variations-diffusers\",\n  revision=\"v2.0\",\n  )\nsd_pipe = sd_pipe.to(device)\n\nim = Image.open(\"path/to/image.jpg\")\ntform = transforms.Compose([\n    transforms.ToTensor(),\n    transforms.Resize(\n        (224, 224),\n        interpolation=transforms.InterpolationMode.BICUBIC,\n        antialias=False,\n        ),\n    transforms.Normalize(\n      [0.48145466, 0.4578275, 0.40821073],\n      [0.26862954, 0.26130258, 0.27577711]),\n])\ninp = tform(im).to(device).unsqueeze(0)\n\nout = sd_pipe(inp, guidance_scale=3)\nout[\"images\"][0].save(\"result.jpg\")\n```\n\n### The importance of resizing correctly... (or not)\n\nNote that due a bit of an oversight during training, the model expects resized images without anti-aliasing. This turns out to make a big difference and is important to do the resizing the same way during inference. When passing a PIL image to the Diffusers pipeline antialiasing will be applied during resize, so it's better to input a tensor which you have prepared manually according to the transfrom in the example above!\n\nHere are examples of images generated without (top) and with (bottom) anti-aliasing during resize. (Input is [this image](https://github.com/SHI-Labs/Versatile-Diffusion/blob/master/assets/ghibli.jpg))\n\n![](alias-montage.jpg)\n\n![](default-montage.jpg)\n\n### V1 vs V2\n\nHere's an example of V1 vs V2, version two was trained more carefully and for longer, see the details below. V2-top vs V1-bottom\n\n![](v2-montage.jpg)\n\n![](v1-montage.jpg)\n\nInput images:\n\n![](inputs.jpg)\n\nOne important thing to note is that due to the longer training V2 appears to have memorised some common images from the training data, e.g. now the previous example of the Girl with a Pearl Earring almosts perfectly reproduce the original rather than creating variations. You can always use v1 by specifiying `revision=\"v1.0\"`.\n\nv2 output for girl with a pearl earing as input (guidance scale=3)\n\n![](earring.jpg)\n\n# Training\n\n\n**Training Procedure**\nThis model is fine tuned from Stable Diffusion v1-3 where the text encoder has been replaced with an image encoder. The training procedure is the same as for Stable Diffusion except for the fact that images are encoded through a ViT-L/14 image-encoder including the final projection layer to the CLIP shared embedding space. The model was trained on LAION improved aesthetics 6plus.\n\n- **Hardware:** 8 x A100-40GB GPUs (provided by [Lambda GPU Cloud](https://lambdalabs.com/service/gpu-cloud))\n- **Optimizer:** AdamW\n\n- **Stage 1** - Fine tune only CrossAttention layer weights from Stable Diffusion v1.4 model\n  - **Steps**: 46,000\n  - **Batch:** batch size=4, GPUs=8, Gradient Accumulations=4. Total batch size=128\n  - **Learning rate:** warmup to 1e-5 for 10,000 steps and then kept constant\n\n- **Stage 2** - Resume from Stage 1 training the whole unet\n  - **Steps**: 50,000\n  - **Batch:** batch size=4, GPUs=8, Gradient Accumulations=5. Total batch size=160\n  - **Learning rate:** warmup to 1e-5 for 5,000 steps and then kept constant\n\n\nTraining was done using a [modified version of the original Stable Diffusion training code](https://github.com/justinpinkney/stable-diffusion).\n\n\n# Uses\n_The following section is adapted from the [Stable Diffusion model card](https://huggingface.co/CompVis/stable-diffusion-v1-4)_\n\n## Direct Use\nThe model is intended for research purposes only. Possible research areas and\ntasks include\n\n- Safe deployment of models which have the potential to generate harmful content.\n- Probing and understanding the limitations and biases of generative models.\n- Generation of artworks and use in design and other artistic processes.\n- Applications in educational or creative tools.\n- Research on generative models.\n\nExcluded uses are described below.\n\n ### Misuse, Malicious Use, and Out-of-Scope Use\n\nThe model should not be used to intentionally create or disseminate images that create hostile or alienating environments for people. This includes generating images that people would foreseeably find disturbing, distressing, or offensive; or content that propagates historical or current stereotypes.\n\n#### Out-of-Scope Use\nThe model was not trained to be factual or true representations of people or events, and therefore using the model to generate such content is out-of-scope for the abilities of this model.\n\n#### Misuse and Malicious Use\nUsing the model to generate content that is cruel to individuals is a misuse of this model. This includes, but is not limited to:\n\n- Generating demeaning, dehumanizing, or otherwise harmful representations of people or their environments, cultures, religions, etc.\n- Intentionally promoting or propagating discriminatory content or harmful stereotypes.\n- Impersonating individuals without their consent.\n- Sexual content without consent of the people who might see it.\n- Mis- and disinformation\n- Representations of egregious violence and gore\n- Sharing of copyrighted or licensed material in violation of its terms of use.\n- Sharing content that is an alteration of copyrighted or licensed material in violation of its terms of use.\n\n## Limitations and Bias\n\n### Limitations\n\n- The model does not achieve perfect photorealism\n- The model cannot render legible text\n- The model does not perform well on more difficult tasks which involve compositionality, such as rendering an image corresponding to \u201cA red cube on top of a blue sphere\u201d\n- Faces and people in general may not be generated properly.\n- The model was trained mainly with English captions and will not work as well in other languages.\n- The autoencoding part of the model is lossy\n- The model was trained on a large-scale dataset\n  [LAION-5B](https://laion.ai/blog/laion-5b/) which contains adult material\n  and is not fit for product use without additional safety mechanisms and\n  considerations.\n- No additional measures were used to deduplicate the dataset. As a result, we observe some degree of memorization for images that are duplicated in the training data.\n  The training data can be searched at [https://rom1504.github.io/clip-retrieval/](https://rom1504.github.io/clip-retrieval/) to possibly assist in the detection of memorized images.\n\n### Bias\n\nWhile the capabilities of image generation models are impressive, they can also reinforce or exacerbate social biases.\nStable Diffusion v1 was trained on subsets of [LAION-2B(en)](https://laion.ai/blog/laion-5b/),\nwhich consists of images that are primarily limited to English descriptions.\nTexts and images from communities and cultures that use other languages are likely to be insufficiently accounted for.\nThis affects the overall output of the model, as white and western cultures are often set as the default. Further, the\nability of the model to generate content with non-English prompts is significantly worse than with English-language prompts.\n\n### Safety Module\n\nThe intended use of this model is with the [Safety Checker](https://github.com/huggingface/diffusers/blob/main/src/diffusers/pipelines/stable_diffusion/safety_checker.py) in Diffusers.\nThis checker works by checking model outputs against known hard-coded NSFW concepts.\nThe concepts are intentionally hidden to reduce the likelihood of reverse-engineering this filter.\nSpecifically, the checker compares the class probability of harmful concepts in the embedding space of the `CLIPModel` *after generation* of the images.\nThe concepts are passed into the model with the generated image and compared to a hand-engineered weight for each NSFW concept.\n\n\n## Old instructions\n\nIf you are using a diffusers version <0.8.0 there is no `StableDiffusionImageVariationPipeline`,\nin this case you need to use an older revision (`2ddbd90b14bc5892c19925b15185e561bc8e5d0a`) in conjunction with the lambda-diffusers repo:\n\n\nFirst clone [Lambda Diffusers](https://github.com/LambdaLabsML/lambda-diffusers) and install any requirements (in a virtual environment in the example below):\n\n```bash\ngit clone https://github.com/LambdaLabsML/lambda-diffusers.git\ncd lambda-diffusers\npython -m venv .venv\nsource .venv/bin/activate\npip install -r requirements.txt\n```\n\nThen run the following python code:\n\n```python\nfrom pathlib import Path\nfrom lambda_diffusers import StableDiffusionImageEmbedPipeline\nfrom PIL import Image\nimport torch\n\ndevice = \"cuda\" if torch.cuda.is_available() else \"cpu\"\npipe = StableDiffusionImageEmbedPipeline.from_pretrained(\n\"lambdalabs/sd-image-variations-diffusers\",\nrevision=\"2ddbd90b14bc5892c19925b15185e561bc8e5d0a\",\n)\npipe = pipe.to(device)\n\nim = Image.open(\"your/input/image/here.jpg\")\nnum_samples = 4\nimage = pipe(num_samples*[im], guidance_scale=3.0)\nimage = image[\"sample\"]\n\nbase_path = Path(\"outputs/im2im\")\nbase_path.mkdir(exist_ok=True, parents=True)\nfor idx, im in enumerate(image):\n    im.save(base_path/f\"{idx:06}.jpg\")\n```\n\n\n\n*This model card was written by: Justin Pinkney and is based on the [Stable Diffusion model card](https://huggingface.co/CompVis/stable-diffusion-v1-4).*"}
{"downloads": 1446, "id": "superb/wav2vec2-base-superb-ks", "likes": 8, "pipeline_tag": "audio-classification", "task": "audio-classification", "meta": {"language": "en", "datasets": ["superb"], "tags": ["speech", "audio", "wav2vec2", "audio-classification"], "widget": [{"example_title": "Speech Commands \"down\"", "src": "https://cdn-media.huggingface.co/speech_samples/keyword_spotting_down.wav"}, {"example_title": "Speech Commands \"go\"", "src": "https://cdn-media.huggingface.co/speech_samples/keyword_spotting_go.wav"}], "license": "apache-2.0"}, "description": "\n\n# Wav2Vec2-Base for Keyword Spotting\n\n## Model description\n\nThis is a ported version of \n[S3PRL's Wav2Vec2 for the SUPERB Keyword Spotting task](https://github.com/s3prl/s3prl/tree/master/s3prl/downstream/speech_commands).\n\nThe base model is [wav2vec2-base](https://huggingface.co/facebook/wav2vec2-base), which is pretrained on 16kHz \nsampled speech audio. When using the model make sure that your speech input is also sampled at 16Khz. \n\nFor more information refer to [SUPERB: Speech processing Universal PERformance Benchmark](https://arxiv.org/abs/2105.01051)\n\n## Task and dataset description\n\nKeyword Spotting (KS) detects preregistered keywords by classifying utterances into a predefined set of \nwords. The task is usually performed on-device for the fast response time. Thus, accuracy, model size, and\ninference time are all crucial. SUPERB uses the widely used \n[Speech Commands dataset v1.0](https://www.tensorflow.org/datasets/catalog/speech_commands) for the task.\nThe dataset consists of ten classes of keywords, a class for silence, and an unknown class to include the\nfalse positive. \n\nFor the original model's training and evaluation instructions refer to the \n[S3PRL downstream task README](https://github.com/s3prl/s3prl/tree/master/s3prl/downstream#ks-keyword-spotting).\n\n\n## Usage examples\n\nYou can use the model via the Audio Classification pipeline:\n```python\nfrom datasets import load_dataset\nfrom transformers import pipeline\n\ndataset = load_dataset(\"anton-l/superb_demo\", \"ks\", split=\"test\")\n\nclassifier = pipeline(\"audio-classification\", model=\"superb/wav2vec2-base-superb-ks\")\nlabels = classifier(dataset[0][\"file\"], top_k=5)\n```\n\nOr use the model directly:\n```python\nimport torch\nfrom datasets import load_dataset\nfrom transformers import Wav2Vec2ForSequenceClassification, Wav2Vec2FeatureExtractor\nfrom torchaudio.sox_effects import apply_effects_file\n\neffects = [[\"channels\", \"1\"], [\"rate\", \"16000\"], [\"gain\", \"-3.0\"]]\ndef map_to_array(example):\n    speech, _ = apply_effects_file(example[\"file\"], effects)\n    example[\"speech\"] = speech.squeeze(0).numpy()\n    return example\n\n# load a demo dataset and read audio files\ndataset = load_dataset(\"anton-l/superb_demo\", \"ks\", split=\"test\")\ndataset = dataset.map(map_to_array)\n\nmodel = Wav2Vec2ForSequenceClassification.from_pretrained(\"superb/wav2vec2-base-superb-ks\")\nfeature_extractor = Wav2Vec2FeatureExtractor.from_pretrained(\"superb/wav2vec2-base-superb-ks\")\n\n# compute attention masks and normalize the waveform if needed\ninputs = feature_extractor(dataset[:4][\"speech\"], sampling_rate=16000, padding=True, return_tensors=\"pt\")\n\nlogits = model(**inputs).logits\npredicted_ids = torch.argmax(logits, dim=-1)\nlabels = [model.config.id2label[_id] for _id in predicted_ids.tolist()]\n```\n\n## Eval results\n\nThe evaluation metric is accuracy.\n\n|        | **s3prl** | **transformers** |\n|"}
{"downloads": 25522, "id": "openai/whisper-base", "likes": 52, "pipeline_tag": "automatic-speech-recognition", "task": "automatic-speech-recognition", "meta": {"language": ["en", "zh", "de", "es", "ru", "ko", "fr", "ja", "pt", "tr", "pl", "ca", "nl", "ar", "sv", "it", "id", "hi", "fi", "vi", "he", "uk", "el", "ms", "cs", "ro", "da", "hu", "ta", false, "th", "ur", "hr", "bg", "lt", "la", "mi", "ml", "cy", "sk", "te", "fa", "lv", "bn", "sr", "az", "sl", "kn", "et", "mk", "br", "eu", "is", "hy", "ne", "mn", "bs", "kk", "sq", "sw", "gl", "mr", "pa", "si", "km", "sn", "yo", "so", "af", "oc", "ka", "be", "tg", "sd", "gu", "am", "yi", "lo", "uz", "fo", "ht", "ps", "tk", "nn", "mt", "sa", "lb", "my", "bo", "tl", "mg", "as", "tt", "haw", "ln", "ha", "ba", "jw", "su"], "tags": ["audio", "automatic-speech-recognition", "hf-asr-leaderboard"], "widget": [{"example_title": "Librispeech sample 1", "src": "https://cdn-media.huggingface.co/speech_samples/sample1.flac"}, {"example_title": "Librispeech sample 2", "src": "https://cdn-media.huggingface.co/speech_samples/sample2.flac"}], "model-index": [{"name": "whisper-base", "results": [{"task": {"name": "Automatic Speech Recognition", "type": "automatic-speech-recognition"}, "dataset": {"name": "LibriSpeech (clean)", "type": "librispeech_asr", "config": "clean", "split": "test", "args": {"language": "en"}}, "metrics": [{"name": "Test WER", "type": "wer", "value": 5.008769117619326}]}, {"task": {"name": "Automatic Speech Recognition", "type": "automatic-speech-recognition"}, "dataset": {"name": "LibriSpeech (other)", "type": "librispeech_asr", "config": "other", "split": "test", "args": {"language": "en"}}, "metrics": [{"name": "Test WER", "type": "wer", "value": 12.84936273212057}]}, {"task": {"name": "Automatic Speech Recognition", "type": "automatic-speech-recognition"}, "dataset": {"name": "Common Voice 11.0", "type": "mozilla-foundation/common_voice_11_0", "config": "hi", "split": "test", "args": {"language": "hi"}}, "metrics": [{"name": "Test WER", "type": "wer", "value": 131}]}]}], "pipeline_tag": "automatic-speech-recognition", "license": "apache-2.0"}, "description": "\n\n# Whisper\n\nWhisper is a pre-trained model for automatic speech recognition (ASR) and speech translation. Trained on 680k hours \nof labelled data, Whisper models demonstrate a strong ability to generalise to many datasets and domains **without** the need \nfor fine-tuning.\n\nWhisper was proposed in the paper [Robust Speech Recognition via Large-Scale Weak Supervision](https://arxiv.org/abs/2212.04356) \nby Alec Radford et al from OpenAI. The original code repository can be found [here](https://github.com/openai/whisper).\n\n**Disclaimer**: Content for this model card has partly been written by the Hugging Face team, and parts of it were \ncopied and pasted from the original model card.\n\n## Model details\n\nWhisper is a Transformer based encoder-decoder model, also referred to as a _sequence-to-sequence_ model. \nIt was trained on 680k hours of labelled speech data annotated using large-scale weak supervision. \n\nThe models were trained on either English-only data or multilingual data. The English-only models were trained \non the task of speech recognition. The multilingual models were trained on both speech recognition and speech \ntranslation. For speech recognition, the model predicts transcriptions in the *same* language as the audio. \nFor speech translation, the model predicts transcriptions to a *different* language to the audio.\n\nWhisper checkpoints come in five configurations of varying model sizes.\nThe smallest four are trained on either English-only or multilingual data.\nThe largest checkpoints are multilingual only. All ten of the pre-trained checkpoints \nare available on the [Hugging Face Hub](https://huggingface.co/models?search=openai/whisper). The \ncheckpoints are summarised in the following table with links to the models on the Hub:\n\n| Size     | Parameters | English-only                                         | Multilingual                                        |\n|"}
{"downloads": 931, "id": "microsoft/speecht5_vc", "likes": 10, "pipeline_tag": "audio-to-audio", "task": "audio-to-audio", "meta": {"license": "mit", "tags": ["audio", "audio-to-audio"], "datasets": ["cmu-arctic"]}, "description": "\n\n# SpeechT5 (voice conversion task)\n\nSpeechT5 model fine-tuned for voice conversion (speech-to-speech) on CMU ARCTIC.\n\nThis model was introduced in [SpeechT5: Unified-Modal Encoder-Decoder Pre-Training for Spoken Language Processing](https://arxiv.org/abs/2110.07205) by Junyi Ao, Rui Wang, Long Zhou, Chengyi Wang, Shuo Ren, Yu Wu, Shujie Liu, Tom Ko, Qing Li, Yu Zhang, Zhihua Wei, Yao Qian, Jinyu Li, Furu Wei.\n\nSpeechT5 was first released in [this repository](https://github.com/microsoft/SpeechT5/), [original weights](https://huggingface.co/mechanicalsea/speecht5-vc). The license used is [MIT](https://github.com/microsoft/SpeechT5/blob/main/LICENSE).\n\nDisclaimer: The team releasing SpeechT5 did not write a model card for this model so this model card has been written by the Hugging Face team.\n\n## Model Description\n\nMotivated by the success of T5 (Text-To-Text Transfer Transformer) in pre-trained natural language processing models, we propose a unified-modal SpeechT5 framework that explores the encoder-decoder pre-training for self-supervised speech/text representation learning. The SpeechT5 framework consists of a shared encoder-decoder network and six modal-specific (speech/text) pre/post-nets. After preprocessing the input speech/text through the pre-nets, the shared encoder-decoder network models the sequence-to-sequence transformation, and then the post-nets generate the output in the speech/text modality based on the output of the decoder.\n\nLeveraging large-scale unlabeled speech and text data, we pre-train SpeechT5 to learn a unified-modal representation, hoping to improve the modeling capability for both speech and text. To align the textual and speech information into this unified semantic space, we propose a cross-modal vector quantization approach that randomly mixes up speech/text states with latent units as the interface between encoder and decoder.\n\nExtensive evaluations show the superiority of the proposed SpeechT5 framework on a wide variety of spoken language processing tasks, including automatic speech recognition, speech synthesis, speech translation, voice conversion, speech enhancement, and speaker identification.\n\n## Intended Uses & Limitations\n\nYou can use this model for speech conversion. See the [model hub](https://huggingface.co/models?search=speecht5) to look for fine-tuned versions on a task that interests you.\n\nCurrently, both the feature extractor and model support PyTorch.\n\n## Citation\n\n**BibTeX:**\n\n```bibtex\n@inproceedings{ao-etal-2022-speecht5,\n    title = {{S}peech{T}5: Unified-Modal Encoder-Decoder Pre-Training for Spoken Language Processing},\n    author = {Ao, Junyi and Wang, Rui and Zhou, Long and Wang, Chengyi and Ren, Shuo and Wu, Yu and Liu, Shujie and Ko, Tom and Li, Qing and Zhang, Yu and Wei, Zhihua and Qian, Yao and Li, Jinyu and Wei, Furu},\n    booktitle = {Proceedings of the 60th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers)},\n    month = {May},\n    year = {2022},\n    pages={5723--5738},\n}\n```\n\n## How to Get Started With the Model\n\nUse the code below to convert a mono 16 kHz speech waveform into another.\n\n```python\nfrom transformers import SpeechT5Processor, SpeechT5ForSpeechToSpeech, SpeechT5HifiGan\nfrom datasets import load_dataset\n\ndataset = load_dataset(\"hf-internal-testing/librispeech_asr_demo\", \"clean\", split=\"validation\")\ndataset = dataset.sort(\"id\")\nsampling_rate = dataset.features[\"audio\"].sampling_rate\nexample_speech = dataset[0][\"audio\"][\"array\"]\n\nprocessor = SpeechT5Processor.from_pretrained(\"microsoft/speecht5_vc\")\nmodel = SpeechT5ForSpeechToSpeech.from_pretrained(\"microsoft/speecht5_vc\")\nvocoder = SpeechT5HifiGan.from_pretrained(\"microsoft/speecht5_hifigan\")\n\ninputs = processor(audio=example_speech, sampling_rate=sampling_rate, return_tensors=\"pt\")\n\n# load xvector containing speaker's voice characteristics from a file\nimport numpy as np\nimport torch\nspeaker_embeddings = np.load(\"xvector_speaker_embedding.npy\")\nspeaker_embeddings = torch.tensor(speaker_embeddings).unsqueeze(0)\n\nspeech = model.generate_speech(inputs[\"input_values\"], speaker_embeddings, vocoder=vocoder)\n\nimport soundfile as sf\nsf.write(\"speech.wav\", speech.numpy(), samplerate=16000)\n```\n"}
{"downloads": 10703, "id": "ali-vilab/text-to-video-ms-1.7b", "likes": 43, "pipeline_tag": "text-to-video", "task": "text-to-video", "meta": {"license": "cc-by-nc-4.0", "tags": ["text-to-video"], "duplicated_from": "diffusers/text-to-video-ms-1.7b"}, "description": "\n\n# Text-to-video-synthesis Model in Open Domain\n\nThis model is based on a multi-stage text-to-video generation diffusion model, which inputs a description text and returns a video that matches the text description. Only English input is supported.\n\n**We Are Hiring!** (Based in Beijing / Hangzhou, China.)\n\nIf you're looking for an exciting challenge and the opportunity to work with cutting-edge technologies in AIGC and large-scale pretraining, then we are the place for you. We are looking for talented, motivated and creative individuals to join our team. If you are interested, please send your CV to us.\n\nEMAIL: <EMAIL>\n\n## Model description\n\nThe text-to-video generation diffusion model consists of three sub-networks: text feature extraction model, text feature-to-video latent space diffusion model, and video latent space to video visual space model. The overall model parameters are about 1.7 billion. Currently, it only supports English input. The diffusion model adopts a UNet3D structure, and implements video generation through the iterative denoising process from the pure Gaussian noise video.\n\nThis model is meant for research purposes. Please look at the [model limitations and biases and misuse](#model-limitations-and-biases), [malicious use and excessive use](#misuse-malicious-use-and-excessive-use) sections.\n\n## Model Details\n\n- **Developed by:** [ModelScope](https://modelscope.cn/)\n- **Model type:** Diffusion-based text-to-video generation model\n- **Language(s):** English\n- **License:**[ CC-BY-NC-ND](https://creativecommons.org/licenses/by-nc-nd/4.0/)\n- **Resources for more information:** [ModelScope GitHub Repository](https://github.com/modelscope/modelscope), [Summary](https://modelscope.cn/models/damo/text-to-video-synthesis/summary).\n- **Cite as:**\n\n## Use cases\n\nThis model has a wide range of applications and can reason and generate videos based on arbitrary English text descriptions. \n\n## Usage \n\nLet's first install the libraries required:\n\n```bash\n$ pip install git+https://github.com/huggingface/diffusers transformers accelerate\n```\n\nNow, generate a video:\n\n```python\nimport torch\nfrom diffusers import DiffusionPipeline, DPMSolverMultistepScheduler\nfrom diffusers.utils import export_to_video\n\npipe = DiffusionPipeline.from_pretrained(\"damo-vilab/text-to-video-ms-1.7b\", torch_dtype=torch.float16, variant=\"fp16\")\npipe.scheduler = DPMSolverMultistepScheduler.from_config(pipe.scheduler.config)\npipe.enable_model_cpu_offload()\n\nprompt = \"Spiderman is surfing\"\nvideo_frames = pipe(prompt, num_inference_steps=25).frames\nvideo_path = export_to_video(video_frames)\n```\n\nHere are some results:\n\n<table>\n    <tr>\n        <td><center>\n        An astronaut riding a horse.\n        <br>\n        <img src=\"https://huggingface.co/datasets/huggingface/documentation-images/resolve/main/diffusers/astr.gif\"\n            alt=\"An astronaut riding a horse.\"\n            style=\"width: 300px;\" />\n        </center></td>\n        <td ><center>\n        Darth vader surfing in waves.\n        <br>\n        <img src=\"https://huggingface.co/datasets/huggingface/documentation-images/resolve/main/diffusers/vader.gif\"\n            alt=\"Darth vader surfing in waves.\"\n            style=\"width: 300px;\" />\n        </center></td>\n    </tr>\n</table>\n\n## Long Video Generation\n\nYou can optimize for memory usage by enabling attention and VAE slicing and using Torch 2.0.\nThis should allow you to generate videos up to 25 seconds on less than 16GB of GPU VRAM.\n\n```bash\n$ pip install git+https://github.com/huggingface/diffusers transformers accelerate\n```\n\n```py\nimport torch\nfrom diffusers import DiffusionPipeline, DPMSolverMultistepScheduler\nfrom diffusers.utils import export_to_video\n\n# load pipeline\npipe = DiffusionPipeline.from_pretrained(\"damo-vilab/text-to-video-ms-1.7b\", torch_dtype=torch.float16, variant=\"fp16\")\npipe.scheduler = DPMSolverMultistepScheduler.from_config(pipe.scheduler.config)\n\n# optimize for GPU memory\npipe.enable_model_cpu_offload()\npipe.enable_vae_slicing()\n\n# generate\nprompt = \"Spiderman is surfing. Darth Vader is also surfing and following Spiderman\"\nvideo_frames = pipe(prompt, num_inference_steps=25, num_frames=200).frames\n\n# convent to video\nvideo_path = export_to_video(video_frames)\n```\n\n\n## View results\n\nThe above code will display the save path of the output video, and the current encoding format can be played with [VLC player](https://www.videolan.org/vlc/).\n\nThe output mp4 file can be viewed by [VLC media player](https://www.videolan.org/vlc/). Some other media players may not view it normally.\n\n## Model limitations and biases\n\n* The model is trained based on public data sets such as Webvid, and the generated results may have deviations related to the distribution of training data.\n* This model cannot achieve perfect film and television quality generation.\n* The model cannot generate clear text.\n* The model is mainly trained with English corpus and does not support other languages \u200b\u200bat the moment**.\n* The performance of this model needs to be improved on complex compositional generation tasks.\n\n## Misuse, Malicious Use and Excessive Use\n\n* The model was not trained to realistically represent people or events, so using it to generate such content is beyond the model's capabilities.\n* It is prohibited to generate content that is demeaning or harmful to people or their environment, culture, religion, etc.\n* Prohibited for pornographic, violent and bloody content generation.\n* Prohibited for error and false information generation.\n\n## Training data\n\nThe training data includes [LAION5B](https://huggingface.co/datasets/laion/laion2B-en), [ImageNet](https://www.image-net.org/), [Webvid](https://m-bain.github.io/webvid-dataset/) and other public datasets. Image and video filtering is performed after pre-training such as aesthetic score, watermark score, and deduplication.\n\n_(Part of this model card has been taken from [here](https://huggingface.co/damo-vilab/modelscope-damo-text-to-video-synthesis))_\n\n## Citation\n\n```bibtex\n    @InProceedings{VideoFusion,\n        author    = {Luo, Zhengxiong and Chen, Dayou and Zhang, Yingya and Huang, Yan and Wang, Liang and Shen, Yujun and Zhao, Deli and Zhou, Jingren and Tan, Tieniu},\n        title     = {VideoFusion: Decomposed Diffusion Models for High-Quality Video Generation},\n        booktitle = {Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)},\n        month     = {June},\n        year      = {2023}\n    }\n```\n"}
{"downloads": 45223, "id": "MCG-NJU/videomae-base", "likes": 37, "pipeline_tag": "video-classification", "task": "video-classification", "meta": {"license": "cc-by-nc-4.0", "tags": ["video-classification"]}, "description": "VideoMAE is an extension of Masked Autoencoders (MAE) to video. The architecture of the model is very similar to that of a standard Vision Transformer (ViT), with a decoder on top for predicting pixel values for masked patches.Videos are presented to the model as a sequence of fixed-size patches (resolution 16x16), which are linearly embedded. One also adds a [CLS] token to the beginning of a sequence to use it for classification tasks. One also adds fixed sinus/cosinus position embeddings before feeding the sequence to the layers of the Transformer encoder.By pre-training the model, it learns an inner representation of videos that can then be used to extract features useful for downstream tasks: if you have a dataset of labeled videos for instance, you can train a standard classifier by placing a linear layer on top of the pre-trained encoder. One typically places a linear layer on top of the [CLS] token, as the last hidden state of this token can be seen as a representation of an entire video."}
{"_id":"621ffdc136468d709f17ade3","id":"facebook/detr-resnet-50","private":false,"pipeline_tag":"object-detection","library_name":"transformers","tags":["transformers","pytorch","safetensors","detr","object-detection","vision","dataset:coco","arxiv:2005.12872","license:apache-2.0","endpoints_compatible","region:us"],"downloads":662419,"likes":813,"modelId":"facebook/detr-resnet-50","author":"facebook","sha":"1d5f47bd3bdd2c4bbfa585418ffe6da5028b4c0b","lastModified":"2024-04-10T13:56:31.000Z","gated":false,"disabled":false,"widgetData":[{"src":"https://huggingface.co/datasets/mishig/sample_images/resolve/main/savanna.jpg","example_title":"Savanna"},{"src":"https://huggingface.co/datasets/mishig/sample_images/resolve/main/football-match.jpg","example_title":"Football Match"},{"src":"https://huggingface.co/datasets/mishig/sample_images/resolve/main/airport.jpg","example_title":"Airport"}],"model-index":null,"config":{"architectures":["DetrForObjectDetection"],"model_type":"detr"},"cardData":{"license":"apache-2.0","tags":["object-detection","vision"],"datasets":["coco"],"widget":[{"src":"https://huggingface.co/datasets/mishig/sample_images/resolve/main/savanna.jpg","example_title":"Savanna"},{"src":"https://huggingface.co/datasets/mishig/sample_images/resolve/main/football-match.jpg","example_title":"Football Match"},{"src":"https://huggingface.co/datasets/mishig/sample_images/resolve/main/airport.jpg","example_title":"Airport"}]},"transformersInfo":{"auto_model":"AutoModelForObjectDetection","pipeline_tag":"object-detection","processor":"AutoImageProcessor"},"siblings":[{"rfilename":".gitattributes"},{"rfilename":"README.md"},{"rfilename":"config.json"},{"rfilename":"model.safetensors"},{"rfilename":"preprocessor_config.json"},{"rfilename":"pytorch_model.bin"}],"spaces":["Gradio-Blocks/Object-Detection-With-DETR-and-YOLOS","ClassCat/DETR-Object-Detection","achterbrain/Intel-Generative-Image-Dashboard","kartik91/Image-To-Story","nateraw/detr-object-detection","CVPR/Object-Detection-With-DETR-and-YOLOS","LuckRafly/Chat-with-an-Image-GeminiAI","XAI/PEEB","Vishaltiwari2019/Object-Detection-with-NLP","Jangai/Describer","Taizun/Object-detection","UmairSyed/ObjectDetection","khizon/ActiveTransportDetection","Tanapol/object_detection","Soumen/transform_image","radames/hello-huggingface.js","eddie5389/Object-Detection-With-DETR-and-YOLOS","beingcognitive/segmentation_w_object_detection","moyanxinxu/video-object-detect-detr-bytetrack","dschandra/OutFits_On_Me","ThisHumanA/Art-of-Visual-Storytelling","ayush200399391001/Video_Manipulation_detection","danielHora/Object_Detection_for_Self-Checkout_Stores","ayaanzaveri/detr","Epitech/Object-Detection","autumn8/selectModel","KaygNas/cut-it","Charles95/gradio-tasks","chethu/Image_Whisper","captain-awesome/Image_object_detection","orYx-models/object-detection-facebook-ResNets","Frantz103/CaptionQuest","spritlesoftware/Image-Object-Detection","dioarafl/objectDetection","grostaco/IRRA","Mahaveer3435/facebook-detr-resnet-50","apratim24/Object_Detector","eldavid/quantificar","ZahraAlebouye/HaveFunWithGenAI","Msaqibsharif/InteriorRedesign","madhawavish/facebook-detr-resnet-50","fofo5019/Recette","BenK0y/RADARPICKv3","123LETSPLAY/iforgot","arssite/Object_Detector_with_Audio","Shamhoon2002/objectdetection","Audecity-sid/SnapAsk","ANASAKHTAR/Object_Detection","ANASAKHTAR/Object_detection_from_Video","ANASAKHTAR/Object_Detector_with_Audio","iamkb/zsod","nicolaleo/repo","EVad/DETR_object_detection_simple_tuto","Stuti/detect-for-drone","supermy/Object-Detection","mr2742/facebook","Nilantha/OD-AugLy","torehan/facebook-detr-resnet-50","RayanRen/Object_detection_202","mahev/facebook-detr-resnet-50","erhansholla/Exercise1","Erion23/ObjectDetectionEx1","mr2742/facebook-detr-resnet-50","mr2742/fb_test","calvin225800/facebook-detr-resnet-50","ismot/8testiaa5","aaravlovescodes/facebook-detr-resnet-50","djenandji/facebook-detr-resnet-50","charly32177/hustvl-yolos-tiny-by","niksen4499/object_detection","awacke1/Image-Classifier-facebook-detr-resnet-50","risekid/imageProcess","jagriti/object_detection","SivaResearch/OpenSourceObjectDetectionModelComparision","eagle-eye/facebook-detr-resnet-50","Riley-x/facebook-detr-resnet-50","dperales/Object-Detection-With-DETR-and-YOLOS","taroii/taroii-notfinetuned-detr-50","kellyxiaowei/Octopus_chat_anything_Public","freddyaboulton/facebook-detr-resnet-50","taroii/taroii-finetuned-detr-50","taroii/Guy2-AirportSec-100epoch","Guy2/Guy2-AirportSec-100epoch","Guy2/Guy2-AirportSec-150epoch","GoliPass/ImageObjectDetectionV01","fdsgr/facebook-detr-resnet-50","amelieav/test-space","Venadoingenieria/IAD1","Venadoingenieria/IA1","7jimmy/ask_to_Image","Tas667/facebook-detr-resnet-50","DexterSptizu/ObjectDetection","mskrishna/facebook-detr-resnet-50","Palakshi/object_detection","SaladSlayer00/object_detection","GoliPass/OpenFlamingo","matthartman/facebook-detr-resnet-50","g1hf/facebook-detr-resnet-50","techysanoj/live-object-detection","adildhkh/facebook-detr-resnet-50"],"createdAt":"2022-03-02T23:29:05.000Z","safetensors":{"parameters":{"F32":41631008},"total":41631008},"inference":"warm","usedStorage":835779962}
{"_id":"6529d7a960f06c6e52e84b53","id":"Falconsai/nsfw_image_detection","private":false,"pipeline_tag":"image-classification","library_name":"transformers","tags":["transformers","pytorch","safetensors","vit","image-classification","arxiv:2010.11929","license:apache-2.0","autotrain_compatible","endpoints_compatible","region:us"],"downloads":54749950,"likes":501,"modelId":"Falconsai/nsfw_image_detection","author":"Falconsai","sha":"63e0a066bb08d2ae47324b540fba3adfd4536569","lastModified":"2023-12-06T17:18:38.000Z","gated":false,"disabled":false,"model-index":null,"config":{"architectures":["ViTForImageClassification"],"model_type":"vit"},"cardData":{"license":"apache-2.0","pipeline_tag":"image-classification"},"transformersInfo":{"auto_model":"AutoModelForImageClassification","pipeline_tag":"image-classification","processor":"AutoImageProcessor"},"siblings":[{"rfilename":".gitattributes"},{"rfilename":"README.md"},{"rfilename":"config.json"},{"rfilename":"model.safetensors"},{"rfilename":"optimizer.pt"},{"rfilename":"preprocessor_config.json"},{"rfilename":"pytorch_model.bin"}],"spaces":["yanze/PuLID-FLUX","DamarJati/FLUX.1-DEV-Canny","fantaxy/flx-pulid","guardiancc/flux-advanced-explorer","Deddy/PuLid-FLX-GPU","JournalistsonHF/text-to-image-bias","sofianhw/PuLID-FLUX","Nuno-Tome/simple_image_classifier","qiuzhi2046/PuLID-FLUX","stazizov/XFluxSpace","yasserrmd/MagicDoodles","Hatman/AWS-Nova-Canvas","Deadmon/FLUX.1-DEV-Canny","SunderAli17/ToonMage","m-ric/PuLID-FLUX","5m4ck3r/nsfw_image_detection","khailoong24/Falconsai-nsfw_image_detection","evijit/text-to-image-bias","JamesCookJr90/Falconsai-nsfw_image_detection","lucianosb/sinteticoXL-bias","Statical/Image","LearningnRunning/adult_image_detector","kevinppaulo/PuLID","piaoyu2011/flux-advanced-explorer","error466/Falconsai-nsfw_image_detection","OzoneAsai/Falconsai-nsfw_image_detection","OzoneAsai/gallaries","PlayForLose/Final_Project","viknsagit/Falconsai-nsfw_image_detection","Raungni0001/Falconsai-nsfw_image_detection","elontusk404/Falconsai-nsfw_image_detection","khailoong24/Falconsai-nsfw_image_detection-Streamlit","PhelpsGG/Falconsai-nsfw_image_detection","Nymbo/simple_image_classifier","KK44/Verify_Content","ruidanwang/not_suit_for_work","ruidanwang/Falconsai-nsfw_image_detection","Gaejoon/Falconsai-nsfw_image_detection","Uzunn/Falconsai-nsfw_image_detection","Dannel/gender","Shabbir-Anjum/image-to-text","Vieshieouaz/nsfw_image_detection","jtgsystems/Falconsai-nsfw_image_detection","sapbot/Falconsai-nsfw_image_detection","shadownada/uff","kksrnfkal/FLUX.1-DEV-Canny","gaur3009/FLUX.1-DEV-Canny","John6666/Xlabs-Gradio-error","VidhitMakvana1/Falconsai-nsfw_image_detection","moniazamla/PuLID-FLUXw","xogaurav/PuLID-FLUX","xogaurav/PuLID-FLUX-New","Rakoo04/PuLID-FLUX","lnyan/flux-dev-flax","MohamedTalaat91/2B-EG-FLUX","Shad0ws/PuLID-FLUX","MohamedTalaat91/2B-EG-FLUX-stores","huanhoang/PuLID-FLUX","MohamedTalaat91/2B-EG-FLUX-stores-video","adminx/PuLID-FLUX","WodeDadao/PuLID-FLUX","AndrewTTiplady/Falconsai-nsfw_image_detection","1124yu/PuLID-FLUX_test","SethyYann98/Falconsai-nsfw_image_detection","zabis13/Falconsai-nsfw_image_detection","ItsMa8di/sentiment-analyze","Daposey15/nsfw_image_detection","Blood076/PuLID-FLUX_","sandeshk/Falconsai-nsfw_image_detection","Soljawritten/FLUX.1-DEV-Canny","MartsoBodziu1994/PuLID-FLUX","arjay-esca/Falconsai-nsfw_image_detection","Zalla666/Falconsai-nsfw_image_detection","cINAWGD/Falconsai-nsfw_image_detection","maccmaccmaccc/5428-p-llamaindexRAG","Miau001/Falconsai-nsfw_image_detection","cINAWGD/Enkacard","sid3000/dfit","Canstralian/Falconsai-nsfw_image_detection","tachibanaa710/safe-content-ai","michieda725shunsuke/PuLID-FLUX","diorbeauty/PuLID-FLUX","rphrp1985/PuLID-FLUX","Monyta/Falconsai-nsfw_image_detection","yasserrmd/GratiCraft","SantiagoSf/Falconsai-nsfw_image_detection","wangyiyi2056/Falconsai-nsfw_image_detection","bcci/detector","sambathmom/Falconsai-nsfw_image_detection","sallehuddin/test-nsfw","MartsoBodziu1994/flx-pulid","nathanlegros123/Falconsai-nsfw_image_detection","helblazer811/ConceptAttention","explorewithai/NSFW-DETECT","RexModZ/Falconsai-nsfw_image_detection"],"createdAt":"2023-10-13T23:50:01.000Z","safetensors":{"parameters":{"F32":85800194},"total":85800194},"inference":"warm","usedStorage":**********}
{"_id":"64c0cd59200a5155968dea57","id":"stabilityai/stable-diffusion-xl-refiner-1.0","private":false,"pipeline_tag":"image-to-image","library_name":"diffusers","tags":["diffusers","safetensors","stable-diffusion","image-to-image","arxiv:2307.01952","arxiv:2211.01324","arxiv:2108.01073","arxiv:2112.10752","license:openrail++","diffusers:StableDiffusionXLImg2ImgPipeline","region:us"],"downloads":1485269,"likes":1807,"modelId":"stabilityai/stable-diffusion-xl-refiner-1.0","author":"stabilityai","sha":"5d4cfe854c9a9a87939ff3653551c2b3c99a4356","lastModified":"2023-09-25T13:42:56.000Z","gated":false,"disabled":false,"model-index":null,"config":{"diffusers":{"_class_name":"StableDiffusionXLImg2ImgPipeline"}},"cardData":{"license":"openrail++","tags":["stable-diffusion","image-to-image"]},"siblings":[{"rfilename":".gitattributes"},{"rfilename":"01.png"},{"rfilename":"LICENSE.md"},{"rfilename":"README.md"},{"rfilename":"comparison.png"},{"rfilename":"model_index.json"},{"rfilename":"pipeline.png"},{"rfilename":"scheduler/scheduler_config.json"},{"rfilename":"sd_xl_refiner_1.0.safetensors"},{"rfilename":"sd_xl_refiner_1.0_0.9vae.safetensors"},{"rfilename":"text_encoder_2/config.json"},{"rfilename":"text_encoder_2/model.fp16.safetensors"},{"rfilename":"text_encoder_2/model.safetensors"},{"rfilename":"tokenizer_2/merges.txt"},{"rfilename":"tokenizer_2/special_tokens_map.json"},{"rfilename":"tokenizer_2/tokenizer_config.json"},{"rfilename":"tokenizer_2/vocab.json"},{"rfilename":"unet/config.json"},{"rfilename":"unet/diffusion_pytorch_model.fp16.safetensors"},{"rfilename":"unet/diffusion_pytorch_model.safetensors"},{"rfilename":"vae/config.json"},{"rfilename":"vae/diffusion_pytorch_model.fp16.safetensors"},{"rfilename":"vae/diffusion_pytorch_model.safetensors"},{"rfilename":"vae_1_0/config.json"},{"rfilename":"vae_1_0/diffusion_pytorch_model.fp16.safetensors"},{"rfilename":"vae_1_0/diffusion_pytorch_model.safetensors"}],"spaces":["mrfakename/OpenDalleV1.1-GPU-Demo","hysts/SDXL","gunship999/SexyImages","segmind/Segmind-Stable-Diffusion","Yntec/ToyWorld","KingNish/Image-Gen-Pro","llamameta/flux-pro-uncensored","exx8/differential-diffusion","XCLiu/InstaFlow","Nymbo/Compare-6","Yntec/PrintingPress","TencentARC/ColorFlow","jeasinema/UltraEdit-SD3","Uthar/SexyReality","llamameta/fluxproV2","openskyml/super-fast-sdxl-stable-diffusion-xl","openskyml/fast-sdxl-stable-diffusion-xl","Yntec/ToyWorldXL","Manjushri/SDXL-1.0-CPU","phenixrhyder/NSFW-ToyWorld","mrfakename/OpenDalle-GPU-Demo","Yntec/blitz_diffusion","cosmicman/CosmicMan-SDXL","prs-eth/rollingdepth","Nymbo/Image-Gen-Pro","ameerazam08/TempestV0.1-GPU-Demo","Vchitect/Vlogger-ShowMaker","dgoot/image-to-image","John6666/Diffusion80XX4sg","llamameta/fast-sd3.5-large","John6666/PrintingPress4","Yntec/Image-Models-Test-April-2024","DemiPoto/TestDifs","Yntec/Image-Models-Test-2024","Yntec/Image-Models-Test","awacke1/OpenDalleV1.1-GPU-Demo","FFusion/FFusionXL-SDXL-DEMO","sub314xxl/SDXL-1.0-Img2Img-CPU","adamelliotfields/diffusion-xl","Yntec/Image-Models-Test-September-2024","DemiPoto/testSortModels","multimodalart/ctrl-x","jbilcke-hf/image-server","CreitinGameplays/cutycat2000x-InterDiffusion-3.5","John6666/hfd_test_nostopbutton","Abinivesh/Multi-models-prompt-to-image-generation","Manjushri/SDXL-1.0-Doodle-to-Image","cryptocalypse/sophia_ai_robot_prophet","Yntec/MiniToyWorld","mantrakp/aai","segmind/Segmind-Vega","AlphaQuark/img2img-01","Omnibus/SDXL-1.0-Img2Img-CPU","John6666/ToyWorld4","John6666/Diffusion80XX4g","Nymbo/Diffusion80XX4sg","SAITAN666/StableDiffusion35Large-Image-Models-Test-November-2024","Yntec/Image-Models-Test-December-2024","torahCodes/Torah_Codes","John6666/Diffusion80XX4","K00B404/HuggingfaceDiffusion_custom","SunderAli17/Generate_images_with_OpenDalle","John6666/blitz_diffusion4","John6666/blitz_diffusion_builtin","smartfeed/image2image","kaleidoskop-hug/PrintingPress","pikto/Diffuser","Fabrice-TIERCELIN/Make-my-image-tile","Ffftdtd5dtft/gfgf","Blane187/multi-diffusion","batuhangoktepe/stabilityai-stable-diffusion-xl-refiner-1.0","NativeAngels/HuggingfaceDiffusion","Uthar/BodyPaint","sub314xxl/SDXL-1.0-CPU","pikto/Elite-Scifi-Models","crystalai/stabilityai-stable-diffusion-xl-refiner-1.0","2MaxM/ShoeGenv2","QualityMinds/Weihnachtskarten","0x7o/RussianVibe","xripunov/stabilityai-stable-diffusion-xl-refiner-1.0","tuan2308/OpenDalleV1.1-GPU","educrpg/text2image2image","mckeeboards/Image-Gen-Pro","Uthar/LewdExperiments","ccy-2000/stabilityai-stable-diffusion-xl-refiner-1.0","Uthar/HighFashion","Yntec/open-craiyon","Taizun/Coloring-book","Yntec/Image-Models-Test-January-2025","yergyerg/ImgGenClone","sub314xxl/SDXL-1.0","sub314xxl/SD-XL","sub314xxl/image-server-1","fittar/summagary","jbilcke-hf/panorama-server","unik-style/unik-ml","Ggxcc4566/stabilityai-stable-diffusion-xl-refiner-1.0","Jack1804/stabilityai-stable-diffusion-xl-refiner-1.0","liliyRehtina/Stable-Diffusion-XL-two","sejamenath2023/SLASHAI.V1.ART"],"createdAt":"2023-07-26T07:38:01.000Z","inference":"warm","usedStorage":31115495204}
{"_id":"6720d1ac035954d93b936a64","id":"briaai/RMBG-2.0","private":false,"pipeline_tag":"image-segmentation","library_name":"transformers","tags":["transformers","pytorch","onnx","safetensors","image-segmentation","remove background","background","background-removal","Pytorch","vision","legal liability","custom_code","license:other","region:us"],"downloads":681241,"likes":624,"modelId":"briaai/RMBG-2.0","author":"briaai","sha":"8d8654b2767cdbe14dc872eb8c99809085fc8a15","lastModified":"2025-02-16T11:52:12.000Z","gated":false,"disabled":false,"model-index":null,"config":{"architectures":["BiRefNet"],"auto_map":{"AutoConfig":"BiRefNet_config.BiRefNetConfig","AutoModelForImageSegmentation":"birefnet.BiRefNet"}},"cardData":{"license":"other","license_name":"bria-rmbg-2.0","license_link":"https://bria.ai/bria-huggingface-model-license-agreement/","pipeline_tag":"image-segmentation","tags":["remove background","background","background-removal","Pytorch","vision","legal liability","transformers"]},"transformersInfo":{"auto_model":"AutoModelForImageSegmentation","custom_class":"birefnet.BiRefNet","pipeline_tag":"image-segmentation"},"siblings":[{"rfilename":".gitattributes"},{"rfilename":"BiRefNet_config.py"},{"rfilename":"README.md"},{"rfilename":"birefnet.py"},{"rfilename":"collage5.png"},{"rfilename":"config.json"},{"rfilename":"diagram1.png"},{"rfilename":"model.safetensors"},{"rfilename":"onnx/model.onnx"},{"rfilename":"onnx/model_bnb4.onnx"},{"rfilename":"onnx/model_fp16.onnx"},{"rfilename":"onnx/model_int8.onnx"},{"rfilename":"onnx/model_q4.onnx"},{"rfilename":"onnx/model_q4f16.onnx"},{"rfilename":"onnx/model_quantized.onnx"},{"rfilename":"onnx/model_uint8.onnx"},{"rfilename":"preprocessor_config.json"},{"rfilename":"pytorch_model.bin"},{"rfilename":"t4.png"}],"spaces":["briaai/BRIA-RMBG-2.0","TencentARC/FreeSplatter","Jonny001/RMBG-Image-Background-Remover","briaai/Product-Shot-Generation","kheloo/RMBG-Image-Background-Remover","svjack/BRIA-RMBG-2.0-Video","innoai/BRIA-RMBG-2.0","svjack/BRIA-RMBG-2.0","tuan2308/BRIA-RMBG-2.0","iatiah/BRIA-RMBG-2.0","Joehoo/BRIA-RMBG-2.0","samanfa9828/storyyar-remove-bg","LiXiang12/RMBG2.0","Greff3/BRIA-RMBG-2.0","Rayhan6251/ray","SiyunHE/glass_try_on1","mcmonostereo/BRIA-RMBG-2.0","kooldark/removebg","Eternal-AI/briaai-RMBG-2.0","ViniDosSantos/Remove_Background","John47225/BRIA-RMBG-2.0","vagodin/BRIA-RMBG-2.0","Ashoka74/Demo_Refurnish","Mixtiles/holiday-cards","AmitGazal/holiday_cards","b-arnault/RMBG","kedaji666/rmbg2.0","kkcdev/BRIA-RMBG-2.0","ShahbazAlam/Product-Shot-Generationns","shemayons/Image-Background-Removal","remuser/RMBG-Image-Background-Remover","LiXiang12/RMBG2.0-gradio","ssboost/Background_Remover-1","01devManish/bg-remover","Himanshu806/bgRemove","Himanshu806/BRIA-RMBG-2.0"],"createdAt":"2024-10-29T12:14:36.000Z","safetensors":{"parameters":{"I64":497664,"F32":220202578},"total":220700242},"usedStorage":**********}
{"_id":"621ffdc136468d709f17e81a","id":"nlpconnect/vit-gpt2-image-captioning","private":false,"pipeline_tag":"image-to-text","library_name":"transformers","tags":["transformers","pytorch","vision-encoder-decoder","image-text-to-text","image-to-text","image-captioning","doi:10.57967/hf/0222","license:apache-2.0","endpoints_compatible","region:us"],"downloads":1817565,"likes":873,"modelId":"nlpconnect/vit-gpt2-image-captioning","author":"nlpconnect","sha":"dc68f91c06a1ba6f15268e5b9c13ae7a7c514084","lastModified":"2023-02-27T15:00:09.000Z","gated":false,"disabled":false,"widgetData":[{"src":"https://huggingface.co/datasets/mishig/sample_images/resolve/main/savanna.jpg","example_title":"Savanna"},{"src":"https://huggingface.co/datasets/mishig/sample_images/resolve/main/football-match.jpg","example_title":"Football Match"},{"src":"https://huggingface.co/datasets/mishig/sample_images/resolve/main/airport.jpg","example_title":"Airport"}],"model-index":null,"config":{"architectures":["VisionEncoderDecoderModel"],"model_type":"vision-encoder-decoder","tokenizer_config":{"unk_token":"<|endoftext|>","bos_token":"<|endoftext|>","eos_token":"<|endoftext|>"}},"cardData":{"tags":["image-to-text","image-captioning"],"license":"apache-2.0","widget":[{"src":"https://huggingface.co/datasets/mishig/sample_images/resolve/main/savanna.jpg","example_title":"Savanna"},{"src":"https://huggingface.co/datasets/mishig/sample_images/resolve/main/football-match.jpg","example_title":"Football Match"},{"src":"https://huggingface.co/datasets/mishig/sample_images/resolve/main/airport.jpg","example_title":"Airport"}]},"transformersInfo":{"auto_model":"AutoModelForImageTextToText","pipeline_tag":"image-text-to-text","processor":"AutoTokenizer"},"siblings":[{"rfilename":".gitattributes"},{"rfilename":"README.md"},{"rfilename":"config.json"},{"rfilename":"merges.txt"},{"rfilename":"preprocessor_config.json"},{"rfilename":"pytorch_model.bin"},{"rfilename":"special_tokens_map.json"},{"rfilename":"tokenizer.json"},{"rfilename":"tokenizer_config.json"},{"rfilename":"vocab.json"}],"spaces":["microsoft/HuggingGPT","fffiloni/text-guided-image-colorization","SRDdev/Image-Caption","openfree/ColorRevive","resul-ai/comparing-captioning-models","GeorgiosIoannouCoder/cuny-tech-prep-tutorial-1","Bils/Generate-Sound-Effects-from-Image","Xhaheen/meme_world","bilgeyucel/captionate","huggingfacejs/image-to-text","kartik91/Image-To-Story","taesiri/HuggingGPT-Lite","Xhaheen/chatgpt_meme_world_","Soumen/image_to_text","mouaddb/image2text-comp","Shriharshan/Image-Caption-Generator","gradio-client-demos/comparing-captioning-models","najoungkim/round-trip-dalle-mini","johngoad/Image-Caption","clem/comparing-captioning-models","Vageesh1/clip_gpt2","qbration21/compare_image_question_answer","IvaElen/find_my_pic","jayyd/nlpconnect-vit-gpt2-image-captioning","vama09/HashtagAndCaption","fynn3003/image_to_text","amarzana/Drop_image_to_short_story","yash-srivastava19/insta_captions","kasun/git-large","krishnapal2308/eye_for_blind","abiabidali/bulk-image-upscaler","eskayML/IMAGE_CAPTIONING","dhanushreddy29/comparing-captioning-models","kasun/comparing-captioning-models","mirzaburanali/project-caption-generation","kasun/blip-large","kusumakar/Image_Describer","ikechan8370/cp-extra","Toinean/huggingfashion","iohanngrig/image2textapp","ruslanmv/Image-To-Text","JorgeV20/PictoVerse","sflindrs/vlm_comparer","awacke1/NLPImageUnderstanding","ccarr0807/HuggingGPT","theholycityweb/HuggingGPT","yuukicammy/vit-gpt2-image-captioning","magnadox/nlpconnect-vit-gpt2-image-captioning","Suhailshah/image-captioning-with-vit-gpt2","himanshubhardwaz/nlpconnect-vit-gpt2-image-captioning","gauravahuja/nlpconnect-vit-gpt2-image-captioning","fariyan/image-to-text","Alfasign/HuggingGPT-Lite","awacke1/Image-to-Text-nlpconnect-vit-gpt2-image-captioning","kingz/nlpconnect-vit-gpt2-image-captioning","swaptr/image-captioning","bedrock123/nlp-vit-gpt2-image-captioning","EnigmaOfTheWorld/MemeWorld","NonnaRose/Image-Caption","mrrandom123/image_creative_caption_new","saurshaz/HuggingGPT","parasmech/Image_captioning_nlpconnect","Woogiepark/nlpconnect-vit-gpt2-image-captioning","DrBenjamin/AI_Demo","redo62/image2text-comp","SumanthKarnati/SumanthKarnati-Image2Ingredients","SumanthKarnati/SumanthKarnati-Image2Ingredients2","Rooni/nlpconnect-vit-gpt2-image-captioning","ishi1234/IMAGE-CAPTIONING","DVLH/nlpconnect-vit-gpt2-image-captioning","joaomorossini/image_captioning_model_comparison","keplersj/photo-merge","themanas021/Image_Caption_Generation","keeptalking/nlpconnect-vit-gpt2-image-captioning2","Charles95/gradio-tasks","themanas021/VisualVoice-Caption_to_Hindi_Speech","SeyedAli/Persian-Image-Captioning-1","Frantz103/CaptionQuest","hninl23/image_translator","sakina1122/Jimmey_image_capturing","apratim24/Image_to_Story_Generator","santu24/images_to_caption","khanaabidabdal/ImageCaptioning","Walid-Ahmed/Image-Captioning_w_audio","Raj086/image-captioning","spark-nlp/VisionEncoderDecoderForImageCaptioning","Osama066/Image-Caption-Generator","123LETSPLAY/image.to.txt","123LETSPLAY/imagetotxt1234567","kanishk128/eye_for_blind","Potre1qw/text-guided-image-colorization","garudkar/PicTunes","Skym616/yamelo","JarvisOnSolana/Jarvis","hellokitty/image-captioning","EVad/BeforeMe","wandergeek/testing","sixrings23/unbias-one","burman/Image-Caption","chrisW6825/HuggingGPT"],"createdAt":"2022-03-02T23:29:05.000Z","inference":"warm","usedStorage":**********}
{"_id":"64bfcd5ff462a99a04fd1ec8","id":"stabilityai/stable-diffusion-xl-base-1.0","private":false,"pipeline_tag":"text-to-image","library_name":"diffusers","tags":["diffusers","onnx","safetensors","text-to-image","stable-diffusion","arxiv:2307.01952","arxiv:2211.01324","arxiv:2108.01073","arxiv:2112.10752","license:openrail++","autotrain_compatible","endpoints_compatible","diffusers:StableDiffusionXLPipeline","region:us"],"downloads":3146737,"likes":6312,"modelId":"stabilityai/stable-diffusion-xl-base-1.0","author":"stabilityai","sha":"462165984030d82259a11f4367a4eed129e94a7b","lastModified":"2023-10-30T16:03:47.000Z","gated":false,"disabled":false,"model-index":null,"config":{"diffusers":{"_class_name":"StableDiffusionXLPipeline"}},"cardData":{"license":"openrail++","tags":["text-to-image","stable-diffusion"]},"siblings":[{"rfilename":".gitattributes"},{"rfilename":"01.png"},{"rfilename":"LICENSE.md"},{"rfilename":"README.md"},{"rfilename":"comparison.png"},{"rfilename":"model_index.json"},{"rfilename":"pipeline.png"},{"rfilename":"scheduler/scheduler_config.json"},{"rfilename":"sd_xl_base_1.0.safetensors"},{"rfilename":"sd_xl_base_1.0_0.9vae.safetensors"},{"rfilename":"sd_xl_offset_example-lora_1.0.safetensors"},{"rfilename":"text_encoder/config.json"},{"rfilename":"text_encoder/flax_model.msgpack"},{"rfilename":"text_encoder/model.fp16.safetensors"},{"rfilename":"text_encoder/model.onnx"},{"rfilename":"text_encoder/model.safetensors"},{"rfilename":"text_encoder/openvino_model.bin"},{"rfilename":"text_encoder/openvino_model.xml"},{"rfilename":"text_encoder_2/config.json"},{"rfilename":"text_encoder_2/flax_model.msgpack"},{"rfilename":"text_encoder_2/model.fp16.safetensors"},{"rfilename":"text_encoder_2/model.onnx"},{"rfilename":"text_encoder_2/model.onnx_data"},{"rfilename":"text_encoder_2/model.safetensors"},{"rfilename":"text_encoder_2/openvino_model.bin"},{"rfilename":"text_encoder_2/openvino_model.xml"},{"rfilename":"tokenizer/merges.txt"},{"rfilename":"tokenizer/special_tokens_map.json"},{"rfilename":"tokenizer/tokenizer_config.json"},{"rfilename":"tokenizer/vocab.json"},{"rfilename":"tokenizer_2/merges.txt"},{"rfilename":"tokenizer_2/special_tokens_map.json"},{"rfilename":"tokenizer_2/tokenizer_config.json"},{"rfilename":"tokenizer_2/vocab.json"},{"rfilename":"unet/config.json"},{"rfilename":"unet/diffusion_flax_model.msgpack"},{"rfilename":"unet/diffusion_pytorch_model.fp16.safetensors"},{"rfilename":"unet/diffusion_pytorch_model.safetensors"},{"rfilename":"unet/model.onnx"},{"rfilename":"unet/model.onnx_data"},{"rfilename":"unet/openvino_model.bin"},{"rfilename":"unet/openvino_model.xml"},{"rfilename":"vae/config.json"},{"rfilename":"vae/diffusion_flax_model.msgpack"},{"rfilename":"vae/diffusion_pytorch_model.fp16.safetensors"},{"rfilename":"vae/diffusion_pytorch_model.safetensors"},{"rfilename":"vae_1_0/config.json"},{"rfilename":"vae_1_0/diffusion_pytorch_model.fp16.safetensors"},{"rfilename":"vae_1_0/diffusion_pytorch_model.safetensors"},{"rfilename":"vae_decoder/config.json"},{"rfilename":"vae_decoder/model.onnx"},{"rfilename":"vae_decoder/openvino_model.bin"},{"rfilename":"vae_decoder/openvino_model.xml"},{"rfilename":"vae_encoder/config.json"},{"rfilename":"vae_encoder/model.onnx"},{"rfilename":"vae_encoder/openvino_model.bin"},{"rfilename":"vae_encoder/openvino_model.xml"}],"spaces":["InstantX/InstantID","google/sdxl","yisol/IDM-VTON","yanze/PuLID-FLUX","lllyasviel/Omost","radames/Real-Time-Latent-Consistency-Model","YupengZhou/StoryDiffusion","radames/Real-Time-Text-to-Image-SDXL-Lightning","yanze/PuLID","diffusers/stable-diffusion-xl-inpainting","AP123/SDXL-Lightning","Shopify/background-replacement","okaris/omni-zero","InstantX/InstantStyle","radames/Enhance-This-HiDiffusion-SDXL","hysts/SDXL","multimodalart/lora-ease","radames/Enhance-This-DemoFusion-SDXL","ByteDance/SDXL-Lightning","TencentARC/T2I-Adapter-SDXL","sudo-ai/zero123plus-demo-space","songweig/rich-text-to-image","Nymbo/Virtual-Try-On","fffiloni/InstantIR","TIGER-Lab/GenAI-Arena","fffiloni/Music-To-Image","latentexplorers/latentnavigation-flux","TencentARC/T2I-Adapter-SDXL-Sketch","ByteDance/Hyper-SDXL-1Step-T2I","AP123/Upside-Down-Diffusion","latent-consistency/lcm-lora-for-sdxl","Nymbo/Serverless-ImgGen-Hub","fffiloni/ZeST","radames/Real-Time-Latent-Consistency-Model-Text-To-Image","linoyts/scribble-sdxl-flash","PAIR/StreamingT2V","chansung/co-write-with-llama2","jasperai/flash-lora","artificialguybr/artificialguybr-demo-lora","r3gm/DiffuseCraft","ChenyangSi/FreeU","fffiloni/text-guided-image-colorization","ginipick/time-machine","turboedit/turbo_edit","exx8/differential-diffusion","John6666/DiffuseCraftMod","fantaxy/playground25","garibida/ReNoise-Inversion","xingpng/CSGO","multimodalart/lora-roulette","multimodalart/one-step-comparison","multimodalart/perturbed-attention-guidance-sdxl","fffiloni/sdxl-dpo","radames/MistoLine-ControlNet-demo","jallenjia/Change-Clothes-AI","radames/Real-Time-SD-Turbo","VAST-AI/MV-Adapter-I2MV-SDXL","radames/real-time-pix2pix-turbo","Nymbo/Compare-6","wangfuyun/Phased-Consistency-Model-PCM","ginipick/AccDiffusion","ymzhang319/FoleyCrafter","huanngzh/MV-Adapter-T2MV-Anime","John6666/votepurchase-multiple-model","Kwai-Kolors/Kolors-FaceID","fantaxy/flx-pulid","TencentARC/ColorFlow","fffiloni/sdxl-control-loras","baulab/ConceptSliders","Collov-Labs/d-edit","openfree/ColorRevive","JOY-Huang/InstantIR","linoyts/scribble-sdxl","TIGER-Lab/AnyV2V","jeasinema/UltraEdit-SD3","wcy1122/MGM","fantos/EveryText","multimodalart/civitai-to-hf","clinteroni/outpainting-with-differential-diffusion-demo","latent-consistency/Real-Time-LCM-ControlNet-Lora-SD1.5","aiqtech/kofaceid","radames/InstantStyle-SDXL-Lightning","h1t/TCD","openskyml/super-fast-sdxl-stable-diffusion-xl","ChenDY/NitroFusion_1step_T2I","naver-ai/VisualStylePrompting","fffiloni/AccDiffusion","fantos/Panorama","fffiloni/StyleAligned_Transfer","SakanaAI/EvoSDXL-JP","openskyml/fast-sdxl-stable-diffusion-xl","Kwai-Kolors/Kolors-Inpainting","Nymbo/image_gen_supaqueue","GlyphByT5/DesignEdit","Djrango/qwen2vl-flux-mini-demo","briaai/BRIA-Background-Generation","Yntec/ToyWorldXL","SakanaAI/Evo-Ukiyoe","multimodalart/HiDiffusion","ameerazam08/InstantStyle-GPU-Demo"],"createdAt":"2023-07-25T13:25:51.000Z","inference":"warm","usedStorage":77373571593}