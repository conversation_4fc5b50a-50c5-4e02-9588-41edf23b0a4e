app_name: "Hugging<PERSON>T"

# Agent Workflow LLM settings task_planning
llm_model_task_planning: "Qwen/Qwen2.5-7B-Instruct"
openai_api_key_task_planning: "EMPTY"
openai_base_url_task_planning: "http://143.89.191.21:8002/v1"

# Agent Workflow LLM settings model_selection
llm_model_model_selection: "Qwen/Qwen2.5-7B-Instruct"
openai_api_key_model_selection: "EMPTY"
openai_base_url_model_selection: "http://143.89.191.21:8002/v1"

# Model Execution LLM settings
## model_inference server
inference_mode: "local"
models_file: "./hugginggpt_local_models.yaml"
models_server_url: "http://143.89.191.21:7070"
## LLM model for model_inference
llm_model_model_inference: "Qwen/Qwen2.5-7B-Instruct"
openai_api_key_model_inference: "EMPTY"
openai_base_url_model_inference: "http://143.89.191.21:8002/v1"

# Agent Workflow LLM setting response_generation
llm_model_response_generation: "Qwen/Qwen2.5-7B-Instruct"
openai_api_key_response_generation: "EMPTY"
openai_base_url_response_generation: "http://143.89.191.21:8002/v1"