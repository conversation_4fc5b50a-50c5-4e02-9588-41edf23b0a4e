FROM pytorch/pytorch:2.5.1-cuda12.4-cudnn9-devel

# download and install fast-service
COPY .cache/fast-service /workspace/fast-service
RUN cd /workspace/fast-service && \
    pip install -r requirements.txt && \
    pip install -e .

# install dependencies for hugginggpt
WORKDIR /workspace/hugginggpt
COPY requirements.txt requirements.txt
RUN pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple

# copy necessary code to docker image
COPY src src
COPY test test
COPY scripts scripts
COPY config config

# add hugginggpt to PYTHONPATH
ENV PYTHONPATH=/workspace/hugginggpt/src

# set workdir and default command
WORKDIR /workspace/hugginggpt/scripts
CMD ["python", "launch_services.py"]