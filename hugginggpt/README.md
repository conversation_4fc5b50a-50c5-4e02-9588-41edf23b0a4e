# HuggingGPT

This is an implementation of [HuggingGPT]().

## Services

### task_planning

This service will call the LLM to generate tasks based on the prompt. The tasks format is as follows. 

```json
{
	"role": "user",
	"content": "please show me a joke and an image of cat"
},
{
	"role": "assistant",
	"content": "[{\"task\": \"conversational\", \"id\": 0, \"dep\": [-1], \"args\": {\"text\": \"please show me a joke of cat\" }}, {\"task\": \"text-to-image\", \"id\": 1, \"dep\": [-1], \"args\": {\"text\": \"a photo of cat\" }}]"
}
```

### model_selection

This service will call the LLM to select the best candidate model based on the `task` value of the running task.

### model_inference

After selecting the best candidate model, this service will call the target model to do inference.

### response_generation

This service will call the LLM to generate the response based on the past inference results.

## How to run

1. Launch Agent service.

[Deploy LLMs first](../tools/llm) and update [settings.yml](config/settings.yml). There are two LLMs in `settings.yml`, one for the Agent Workflow(task_planning, model_selection, response_generation), the other for the Model Execution(text-generation task).

In ```ai-agent-benchmark/hugginggpt/scripts``` dir, execute the following command.

```shell
export HUGGINGFACE_ACCESS_TOKEN=[your_token]
python launch_services.py
```

2. Launch Model Service.

Configure the models in [models.yaml](hugginggpt_models/models.yaml) in the following format:
```yaml
- model_name: nlpconnect/vit-gpt2-image-captioning
  task: image-to-text
  device: 1
```

Then launch the models_service in `ai-agent-benchmark/hugginggpt/src/models_server` by running:
```shell
python ModelServer.py
[-p port]
[-f models_config]
[-hf hf_home]
[-b batch_size]
[-t waiting_interval]
```

## How to deploy (as a container)

In ```ai-agent-benchmark/hugginggpt``` dir, do the following steps.

### 1. Build an image

Make sure you copy the project [fast-service](https://github.com/hkust-adsl/fast-service) into dir ```./.cache```. The image will install fast-service first.

Run the following command.

```shell
docker build -t hugginggpt:v1 .
```

### 2. Start a container

Run the following command. Be sure to at the path `hugginggpt/`

```shell
docker run \
	-d --rm \
	--gpus all \
	--name hugginggpt \
	-p 20101:20101 \
	-e HUGGINGFACE_ACCESS_TOKEN=[your_token] \
	-v ./config/settings.yml:/workspace/hugginggpt/config/settings.yml \
	-v ./config/hugginggpt_server.yml:/workspace/hugginggpt/config/hugginggpt_server.yml \
	-v ./config/hugginggpt_config.yml:/workspace/hugginggpt/config/hugginggpt_config.yml \
	hugginggpt:v1
```

Instead, you can also run the script [docker_run_service.sh](scripts/docker_run_service.sh). `./scripts/docker_run_service.sh`

## How to test

Run the service first.

In ```ai-agent-benchmark/hugginggpt/test``` dir, execute the following command.

```shell
python test_hugginggpt.py
```

## How to benchmark

First run the `Agent Service` and the `Models Service`.

Then in dir ```ai-agent-benchmark/hugginggpt/benchmark```

1. prepare data via [prepare_data](benchmark/prepare_data.py):

```shell
python prepare_data.py
[-d dir] Data directory.
[-n num] The number of data.
[-l] The image use the local_path. If not set, it will use the image_server at 127.0.0.1:8000 by default.
```


2. run [load_gen.py](benchmark/load_gen.py)

```shell
python load_gen
[-m (one-by-one, possion, fixed-interval)]
[-p] If the dataset has the planned task.
```