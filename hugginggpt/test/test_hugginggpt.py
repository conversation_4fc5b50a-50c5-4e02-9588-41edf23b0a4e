import argparse
from fast_service import FastServiceConfig
from hugginggpt import hugginggpt_fsm
from hugginggpt import hugginggpt_e2e, HuggingGPTE2EOutput, HuggingGPTE2EInput


def test_client_interface(collection_name, question):
    messages = [{"role": "user", "content": question}]

    y1: HuggingGPTE2EOutput = hugginggpt_e2e(
        HuggingGPTE2EInput(collection_name="test", question=question)
    )

    print(f"y1 => {y1.model_dump()}")


def get_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("-n", "--collection_name", default="test")
    parser.add_argument("-c", "--config", default="../config/hugginggpt_client.yml")

    args = parser.parse_args()
    return args


if __name__ == "__main__":
    args = get_args()
    collection_name = args.collection_name

    # question = "Translate 'hello world' into Chinese."
    # question = "Please tell me how many zebras are in the https://s2.loli.net/2024/11/07/UaO8FZev1QpcdyB.png."
    # question = "do image to image to https://s2.loli.net/2024/11/07/UaO8FZev1QpcdyB.png, a photo of zebras in the wild"
    # question = "do image segmentation to https://s2.loli.net/2024/11/07/UaO8FZev1QpcdyB.png"
    # question = "do image to text to https://s2.loli.net/2024/11/07/UaO8FZev1QpcdyB.png"
    # question = "do text to image to 'An astronaut riding a green horse'"
    # question = "do visual question answering to https://s2.loli.net/2024/11/07/UaO8FZev1QpcdyB.png"
    # question = "I need to identify and label objects in the provided image '/home/<USER>/ai-agent-benchmark/hugginggpt/benchmark/.cache/images/0.jpg'."
    question = (
        "do object detection to https://s2.loli.net/2024/11/07/UaO8FZev1QpcdyB.png"
    )
    config = FastServiceConfig.load_from_file(args.config)
    hugginggpt_fsm.setup_client_mode(config)
    test_client_interface(collection_name, question)
