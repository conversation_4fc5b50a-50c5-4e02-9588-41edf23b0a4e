import argparse
from fast_service import FastServiceConfig
from hugginggpt import hugginggpt_fsm
from hugginggpt import hugginggpt_e2e, HuggingGPTE2EOutput, HuggingGPTE2EInput


def test_client_interface(collection_name, question, tasks_to_plan=None):
    messages = [{"role": "user", "content": question}]

    y1: HuggingGPTE2EOutput = hugginggpt_e2e(
        HuggingGPTE2EInput(
            collection_name=collection_name,
            question=question,
            tasks_to_plan=tasks_to_plan,
        )
    )

    print(f"y1 => {y1.model_dump()}")


def get_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("-n", "--collection_name", default="test")
    parser.add_argument("-c", "--config", default="../config/hugginggpt_client.yml")

    args = parser.parse_args()
    return args


if __name__ == "__main__":
    args = get_args()
    collection_name = args.collection_name

    # question = "Translate 'hello world' into Chinese."
    # question = "Please tell me how many zebras are in the https://s2.loli.net/2024/11/07/UaO8FZev1QpcdyB.png."

    tasks_to_plan = [12]
    question = "Please talk about the figure https://s2.loli.net/2024/11/07/UaO8FZev1QpcdyB.png."

    config = FastServiceConfig.load_from_file(args.config)
    hugginggpt_fsm.setup_client_mode(config)
    test_client_interface(collection_name, question, tasks_to_plan)
