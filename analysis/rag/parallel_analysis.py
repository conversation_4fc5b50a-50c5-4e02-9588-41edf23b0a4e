# %%
import os, sys
import numpy as np
import json
import tqdm
import matplotlib.pyplot as plt
import pandas as pd
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import argparse

import utils as analysis_utils
from utils import COL_NAMES
from utils import load_csv, load_client_csv, load_server_csv
from utils import load_csv, load_client_csv, load_server_csv
from utils import load_monitor_data
from utils import generate_stats
from utils import load_all_bench, load_exps

from breakdown import (
    client_services,
    entry_services,
    indexing_services,
    chatting_services,
    all_services,
)
from breakdown import service_color_palette

# %%
# SOURCE_HOME = "/mnt/data/rag-benchmark/exps/2025-2-1/light-embed"
# sources = {
#     "noparallel": "benchmarking-noparallel",
#     "parallel": "benchmarking-parallel-rag",
# }


def e2e_parallel_execution(
    exp_dirs: list,
    service: str = None,
    warmup: int = 10,
    cooldown: int = 0,
    ax=None,
    save_dir: str = None,
    max_delay: float = 1e9,
):
    df, df_stats = load_exps(exp_dirs, warmup=warmup, cooldown=cooldown)
    # df['exp_tag'] = df['exp_dir'].apply(lambda x: "/".join(x.split("/")[-2:]))
    df["exp_tag"] = df["exp_dir"].apply(
        lambda x: "-".join(x.split("/")[-2].split("-")[1:])
    )
    df_stats["exp_tag"] = df_stats["exp_dir"].apply(
        lambda x: "-".join(x.split("/")[-2].split("-")[1:])
    )
    if service:
        data = df[df["service_name"] == service]
        data_stats = df_stats[df_stats["service_name"] == service]
    else:
        data = df[df["service_name"].isin(entry_services)]
        data_stats = df_stats[df_stats["service_name"].isin(entry_services)]
    service_names = data["service_name"].unique()
    assert len(service_names) == 1, f"Multiple service names found: {service_names}"
    service_name = service_names[0]
    print(f"Service Name: {service_name}")
    datasets = data["dataset"].unique()
    assert len(datasets) == 1, f"Multiple datasets found: {datasets}"
    dataset = datasets[0]
    print(f"Dataset: {dataset}")

    data = data[data["delay"] < max_delay]
    print(data.groupby("exp_tag")["delay"].describe())
    if save_dir:
        if ax is None:
            internal_ax = True
            fig, ax = plt.subplots(1, 1, figsize=(10, 5))
        else:
            internal_ax = False
        # compare the delay with different exp_tags
        sns.boxplot(data=data, x="exp_tag", y="delay", ax=ax)

        # annotate each box with mean(delay)
        for i, exp_tag in enumerate(data["exp_tag"].unique()):
            mean_delay = data[data["exp_tag"] == exp_tag]["delay"].mean()
            ax.text(
                i,
                mean_delay,
                f"{mean_delay:.2f}s",
                ha="center",
                va="bottom",
                color="red",
            )

        ax.set_title(f"Parallel Optimization for {service_name} with {dataset}")
        # ax.set_yscale('log')
        plt.tight_layout()
        if internal_ax:
            tags = data["exp_tag"].unique()
            file_tag = "_".join(tags)
            plt.savefig(os.path.join(save_dir, f"{service}_{file_tag}.png"))
            plt.close()


def e2e_parallel_execution_4090D(
    service: str = None,
    parallel_modes=None,
    warmup: int = 10,
    cooldown: int = 0,
    save_dir: str = None,
):
    if parallel_modes is None:
        exp_homes = [
            "/mnt/data/rag-benchmark/exps/2025-2-1/light-embed/benchmarking-noparallel",
            "/mnt/data/rag-benchmark/exps/2025-2-1/light-embed/benchmarking-parallel-retrieval",
            "/mnt/data/rag-benchmark/exps/2025-2-1/light-embed/benchmarking-parallel-search",
            "/mnt/data/rag-benchmark/exps/2025-2-1/light-embed/benchmarking-parallel-rag",
        ]
    else:
        exp_homes = [
            f"/mnt/data/rag-benchmark/exps/2025-2-1/light-embed/benchmarking-{parallel_mode}"
            for parallel_mode in parallel_modes
        ]
    rags = ["naive", "advanced", "dynamic"]
    datasets = ["NQ", "WQA", "MSMARCO"]
    # datasets = ["natural_questions", "squad", "trivia"]
    nrows = len(datasets)
    ncols = len(rags)

    fig, axes = plt.subplots(
        nrows=nrows,
        ncols=ncols,
        figsize=(ncols * 8, nrows * 8),
        sharex=True,
        sharey="row",
    )
    axes = axes.flatten()

    for i, dataset in enumerate(datasets):
        for j, rag in enumerate(rags):
            exp_dirs = [
                f"{exp_home}/{rag}-{dataset}-OneByOne-0.0-500"
                for exp_home in exp_homes
                if os.path.exists(f"{exp_home}/{rag}-{dataset}-OneByOne-0.0-500")
            ]
            if len(exp_dirs) == 0:
                continue
            ax = axes[i * ncols + j]
            e2e_parallel_execution(
                exp_dirs=exp_dirs,
                service=service,
                warmup=warmup,
                cooldown=cooldown,
                ax=ax,
                save_dir=save_dir,
                max_delay=500,
            )

            sub_save_dir = os.path.join(save_dir, os.path.basename(exp_dirs[0]))
            os.makedirs(sub_save_dir, exist_ok=True)
            e2e_parallel_execution(
                exp_dirs=exp_dirs,
                service=service,
                warmup=warmup,
                cooldown=cooldown,
                save_dir=sub_save_dir,
                max_delay=500,
            )
    if save_dir:
        fig.tight_layout()
        fig.savefig(os.path.join(save_dir, f"{service}_all.png"))
        plt.close()


def e2e_parallel_execution_4090_bars(
    service: str = None,
    parallel_modes=None,
    warmup: int = 10,
    cooldown: int = 0,
    save_dir: str = None,
    max_delay: float = 1e9,
):
    if parallel_modes is None:
        exp_homes = [
            "/mnt/data/rag-benchmark/exps/2025-2-1/light-embed/benchmarking-noparallel",
            "/mnt/data/rag-benchmark/exps/2025-2-1/light-embed/benchmarking-parallel-retrieval",
            "/mnt/data/rag-benchmark/exps/2025-2-1/light-embed/benchmarking-parallel-search",
            "/mnt/data/rag-benchmark/exps/2025-2-1/light-embed/benchmarking-parallel-rag",
        ]
    else:
        exp_homes = [
            f"/mnt/data/rag-benchmark/exps/2025-2-1/light-embed/benchmarking-{parallel_mode}"
            for parallel_mode in parallel_modes
        ]
    rags = ["naive", "advanced", "dynamic"]
    datasets = ["NQ", "WQA", "MSMARCO"]

    data = []
    for i, rag in enumerate(rags):
        for j, dataset in enumerate(datasets):
            exp_dirs = [
                f"{exp_home}/{rag}-{dataset}-OneByOne-0.0-500"
                for exp_home in exp_homes
                if os.path.exists(f"{exp_home}/{rag}-{dataset}-OneByOne-0.0-500")
            ]
            if len(exp_dirs) == 0:
                print(f"Skipping {rag}-{dataset}")
                continue
            # plot len(exp_dirs) bars at x, each for a parallel mode
            for k, exp_dir in enumerate(exp_dirs):
                df, df_stats = load_exps([exp_dir], warmup=warmup, cooldown=cooldown)
                if service:
                    df = df[df["service_name"] == service]
                else:
                    df = df[df["service_name"].isin(entry_services)]
                service_names = df["service_name"].unique()
                assert (
                    len(service_names) == 1
                ), f"Multiple service names found: {service_names}"
                service_name = service_names[0]
                assert (
                    len(df["dataset"].unique()) == 1
                ), f"Multiple datasets found: {datasets}"
                dataset = df["dataset"].unique()[0]
                df = df[df["delay"] < max_delay]
                mean_delay = df["delay"].mean()
                parallel_mode = "-".join(exp_dir.split("/")[-2].split("-")[1:])
                element = {
                    "rag": rag,
                    "dataset": dataset,
                    "parallel_mode": parallel_mode,
                    "mean_delay": mean_delay,
                }
                data.append(element)
                if rag in ["naive", "advanced"] and parallel_mode == "parallel-rag":
                    data.append({**element, "parallel_mode": "parallel-rag-noes"})
    df = pd.DataFrame(data)
    # rename parallel-rag to parallel-es-rag, and parallel-rag-noes to parallel-rag
    df["parallel_mode"] = df["parallel_mode"].apply(
        lambda x: "parallel-es-rag" if x == "parallel-rag" else x
    )
    df["parallel_mode"] = df["parallel_mode"].apply(
        lambda x: "parallel-rag" if x == "parallel-rag-noes" else x
    )
    df["rag+dataset"] = df["rag"] + "\n" + df["dataset"]
    print(df)
    # we need to compare the mean_delay with different parallel_mode for each exp
    fig, ax = plt.subplots(1, 1, figsize=(10, 5))
    sns.barplot(
        data=df,
        x="rag+dataset",
        y="mean_delay",
        hue="parallel_mode",
        hue_order=["noparallel", "parallel-rag", "parallel-es-rag"],
        ax=ax,
    )
    ax.set_title(
        f"Parallel Optimization for {service} with different RAGs and datasets"
    )
    ax.set_ylabel("Mean Delay (s)")
    ax.set_xlabel("RAG + Dataset")
    ax.legend(title="Parallel Mode")
    plt.tight_layout()

    if save_dir:
        plt.savefig(os.path.join(save_dir, f"{service}_all_bars.png"))
        plt.close()

    # Pivot the DataFrame
    df_pivot = (
        df.drop(columns=["rag+dataset"])
        .pivot(index=["rag", "dataset"], columns="parallel_mode", values="mean_delay")
        .reset_index()
    )

    # Rename the columns
    df_pivot.columns.name = None
    df_pivot["parallel-speedup"] = df_pivot["noparallel"] / df_pivot["parallel-rag"]
    df_pivot["parallel-es-speedup"] = (
        df_pivot["noparallel"] / df_pivot["parallel-es-rag"]
    )
    # reverse the row order
    df_pivot = df_pivot.iloc[::-1]
    df_pivot = df_pivot[
        [
            "rag",
            "dataset",
            "noparallel",
            "parallel-rag",
            "parallel-es-rag",
            "parallel-speedup",
            "parallel-es-speedup",
        ]
    ]
    df_pivot = df_pivot.sort_values(by=["rag", "dataset"])
    print(df_pivot)


def get_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("-e", "--exp_dirs", nargs="+", type=str)
    parser.add_argument("-name", "--exp_name", type=str, default=None)
    parser.add_argument("-o", "--output_dir", type=str, default=None)
    parser.add_argument("-s", "--service", type=str, default=None)
    parser.add_argument("--warmup", type=int, default=10)
    parser.add_argument("--cooldown", type=int, default=0)
    parser.add_argument("--plotall", action="store_true")
    parser.add_argument("-p", "--parallel_modes", nargs="+", type=str)
    args = parser.parse_args()

    if args.output_dir and not os.path.exists(args.output_dir):
        os.makedirs(args.output_dir)

    return args


if __name__ == "__main__":
    args = get_args()
    exp_dirs = [
        f"/mnt/data/rag-benchmark/exps/2025-2-1/light-embed/benchmarking-{parallel_mode}/{args.exp_name}"
        for parallel_mode in args.parallel_modes
    ]
    # e2e_parallel_execution(
    #     exp_dirs=exp_dirs,
    #     service=args.service,
    #     warmup=args.warmup,
    #     cooldown=args.cooldown,
    #     save_dir=args.output_dir,
    # )
    if args.plotall:
        # e2e_parallel_execution_4090D(
        #     service=args.service,
        #     parallel_modes=args.parallel_modes,
        #     warmup=args.warmup,
        #     cooldown=args.cooldown,
        #     save_dir=args.output_dir,
        # )
        e2e_parallel_execution_4090_bars(
            service=args.service,
            parallel_modes=args.parallel_modes,
            warmup=args.warmup,
            cooldown=args.cooldown,
            save_dir=args.output_dir,
            max_delay=500,
        )
