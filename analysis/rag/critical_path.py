# %%
import os, sys
import numpy as np
import json
import tqdm
import matplotlib.pyplot as plt
import pandas as pd
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import argparse
from functools import cache

import utils as analysis_utils
from utils import COL_NAMES
from utils import load_csv, load_client_csv, load_server_csv
from utils import load_csv, load_client_csv, load_server_csv
from utils import load_monitor_data, is_exp_dir
from utils import generate_stats
from utils import load_all_bench, load_exps

from breakdown import (
    client_services,
    entry_services,
    indexing_services,
    chatting_services,
    all_services,
)
from breakdown import service_color_palette


def extract_critical_path(exp_dir: str, warmup: int = 10, cooldown: int = 0):
    """
    Extract the critical path from the experiment directory.
    """
    df = load_monitor_data(exp_dir=exp_dir, warmup=warmup, cooldown=cooldown)
    # delete client services
    df = df[~df["service_name"].apply(lambda x: x.startswith("client"))]
    print(df["service_name"].unique())
    paths = [
        [
            "load_docs",
            "split_docs",
            "embed_documents",
            "vdb_store",
            "vdb_search",
            "post_retrieval",
            "generation",
        ],
        [
            "query_translation",
            "embed_query",
            "vdb_search",
            "post_retrieval",
            "generation",
        ],
        ["retrieval_judger", "generation"],
    ]
    if "dynamic-" in exp_dir:
        paths[1].append("retrieval_judger")
    data = []

    for request_id, group in df.groupby("request_id"):
        # print(group)
        item = {"request_id": request_id}
        critical_path_delay = 0
        critical_path_id = 0
        for i, path in enumerate(paths):
            path_data = group[group["service_name"].isin(path)]
            # make sure the path_data contains all the services in the path
            if path_data["service_name"].nunique() != len(path):
                missing_services = set(path) - set(path_data["service_name"].unique())
                # print(f"WARNING: Request {request_id} Path {i} missing services: {missing_services}")
                item[f"path_{i}"] = 0.0
                continue
            path_data = path_data.copy()
            # sum the delay of each service in the path
            path_data = path_data[path_data["delay"] <= 100]
            path_delay = path_data["delay"].sum()
            item[f"path_{i}"] = path_delay
            if path_delay > critical_path_delay:
                critical_path_delay = path_delay
                critical_path_id = i
        item["critical_path_id"] = critical_path_id
        item["critical_path_delay"] = critical_path_delay
        for i, path in enumerate(paths):
            item[f"diff_{i}"] = item["critical_path_delay"] - item[f"path_{i}"]
        data.append(item)
    return pd.DataFrame(data)


# %%

if __name__ == "__main__":
    # exp_dir = "/mnt/data/rag-benchmark/exps/2025-2-1/light-embed/benchmarking-parallel-rag/dynamic-MSMARCO-OneByOne-0.0-500"
    # exp_dir = "/mnt/data/rag-benchmark/exps/2025-2-1/light-embed/benchmarking-parallel-rag-es/dynamic-MSMARCO-OneByOne-0.0-500"
    # exp_dir = "/mnt/data/rag-benchmark/exps/2025-2-1/light-embed/benchmarking-parallel-rag-noes/dynamic-MSMARCO-OneByOne-0.0-500"

    # exp_dir = "/mnt/data/rag-benchmark/exps/2025-2-1/light-embed/benchmarking-noparallel/dynamic-MSMARCO-OneByOne-0.0-500"
    # exp_dir = "/mnt/data/rag-benchmark/exps/2025-2-1/light-embed/benchmarking-noparallel/dynamic-NQ-OneByOne-0.0-500"
    # exp_dir = "/mnt/data/rag-benchmark/exps/2025-2-1/light-embed/benchmarking-noparallel/dynamic-WQA-OneByOne-0.0-500"

    exp_dir = "/mnt/data/rag-benchmark/exps/2025-2-1/light-embed/benchmarking-noparallel/advanced-MSMARCO-OneByOne-0.0-500"
    # exp_dir = "/mnt/data/rag-benchmark/exps/2025-2-1/light-embed/benchmarking-noparallel/advanced-NQ-OneByOne-0.0-500"
    # exp_dir = "/mnt/data/rag-benchmark/exps/2025-2-1/light-embed/benchmarking-noparallel/advanced-WQA-OneByOne-0.0-500"
    df = extract_critical_path(exp_dir=exp_dir, warmup=10, cooldown=0)
    # print(len(df))
    # print(df.describe())
    print(df["critical_path_id"].value_counts())
    for critical_pid, group in df.groupby("critical_path_id"):
        print(f"Critical path {critical_pid}:")
        print(group.describe())

    # plot the distribution of path_1 - path_0
    diff = df["path_1"] - df["path_0"]
    sns.histplot(diff, kde=True)
    plt.xlabel("Path 1 - Path 0")
    plt.ylabel("Frequency")
    plt.title("Distribution of Path 1 - Path 0")
    plt.show()

# %%
