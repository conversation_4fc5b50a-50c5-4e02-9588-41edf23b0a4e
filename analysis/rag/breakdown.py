# %%
import os, sys
import numpy as np
import json
import tqdm
import matplotlib.pyplot as plt
import pandas as pd
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import argparse

import utils as analysis_utils
from utils import COL_NAMES
from utils import load_csv, load_client_csv, load_server_csv
from utils import load_csv, load_client_csv, load_server_csv
from utils import load_monitor_data
from utils import generate_stats
from utils import load_all_bench


# %%
all_services = []
client_services = [
    "client.naive_rag",
    "client.advanced_rag",
    "client.dynamic_rag",
    "client.contextual_rag",
]
entry_services = ["naive_rag", "advanced_rag", "dynamic_rag", "contextual_rag"]
chatting_entry_services = [
    "naive_rag_chatting",
    "multiquery_rag_chatting",
    "dynamic_rag_chatting",
]
indexing_services = [
    "load_docs",
    "split_docs",
    "situate_chunks",
    "embed_documents",
    "vdb_store",
]
chatting_services = [
    "retrieval_judger",
    "query_translation",
    "embed_query",
    "vdb_search",
    "post_retrieval",
    "generation",
    "batch_embed_query",
    "batch_vdb_search",
    "normal_generation",
    "direct_generation",
]
all_services += client_services
all_services += entry_services
all_services += indexing_services
all_services += chatting_services

service_color_palette = {
    service: color
    for service, color in zip(
        all_services, sns.color_palette("pastel", len(all_services))
    )
}


# %%


def get_agent_name(df: pd.DataFrame) -> str:
    service_names = df["service_name"].unique()
    if "client.naive_rag" in service_names:
        return "naive_rag"
    elif "client.advanced_rag" in service_names:
        return "advanced_rag"
    elif "client.dynamic_rag" in service_names:
        return "dynamic_rag"
    elif "client.contextual_rag" in service_names:
        return "contextual_rag"
    else:
        return "unknown"


# plot the distribution of delays for a given service
def plot_delay_distribution(
    df: pd.DataFrame,
    service_name: str = "client.naive_rag",
    ax=None,
    save_dir: str = None,
):
    if ax is None:
        # clear plt
        plt.clf()
        fig, ax = plt.subplots()
    data = df[df["service_name"] == service_name]
    sns.histplot(data["delay"], bins=100, ax=ax)

    # add cdflines
    stats_df = generate_stats(df)
    ax.axvline(
        stats_df[stats_df["service_name"] == service_name]["mean_delay"].values[0],
        color="red",
        linestyle="--",
        label="mean",
    )
    ax.axvline(
        stats_df[stats_df["service_name"] == service_name]["median_delay"].values[0],
        color="green",
        linestyle="--",
        label="median",
    )
    ax.axvline(
        stats_df[stats_df["service_name"] == service_name]["p95_delay"].values[0],
        color="blue",
        linestyle="--",
        label="95th percentile",
    )
    ax.axvline(
        stats_df[stats_df["service_name"] == service_name]["p99_delay"].values[0],
        color="purple",
        linestyle="--",
        label="99th percentile",
    )
    ax.set_title(f"Delay distribution of {service_name}")
    ax.set_xlabel("Delay (ms)")
    ax.set_ylabel("Frequency")
    ax.legend()

    # adjust layout
    plt.tight_layout()

    # save the plot
    if save_dir:
        os.makedirs(save_dir, exist_ok=True)
        plt.savefig(os.path.join(save_dir, f"{service_name}_delay_distribution.png"))


def plot_delay_distributions(
    df: pd.DataFrame, services: list = ["client.naive_rag"], save_dir: str = "plots"
):
    # clear plt
    plt.clf()
    # remove service that is not in df
    services = [
        service for service in services if service in df["service_name"].unique()
    ]
    fig, axes = plt.subplots(
        len(services) // 4 + (len(services) % 4 > 0),
        4,
        figsize=(20, 10),
        sharex=True,
        sharey=True,
    )
    for i, service_name in enumerate(services):
        plot_delay_distribution(df, service_name, ax=axes[i // 4][i % 4])

    # adjust layout
    plt.tight_layout()

    # save the plot
    if save_dir:
        os.makedirs(save_dir, exist_ok=True)
        plt.savefig(os.path.join(save_dir, f"service_delay_distributions.png"))


def plot_avg_delay_bars(
    df: pd.DataFrame, services: list = ["client.naive_rag"], save_dir: str = "plots"
):
    plt.clf()
    font_size = 20
    agent_name = get_agent_name(df)
    e2e_mean_delay = df[df["service_name"] == agent_name]["delay"].mean()
    data = df[df["service_name"].isin(services)]
    stats_df = generate_stats(data)
    ordered_services = stats_df.sort_values("mean_delay", ascending=False)[
        "service_name"
    ].values.tolist()
    # plot the delays for each service
    sns.barplot(
        x="service_name",
        y="delay",
        data=data,
        order=ordered_services,
        hue="service_name",
        palette=service_color_palette,
        dodge=False,
    )
    # add the mean delay for each service
    mean_delays = data.groupby("service_name")["delay"].mean()
    for service in ordered_services:
        plt.text(
            service,
            mean_delays[service],
            f"{mean_delays[service]:.2f}s,\n{100*mean_delays[service] / e2e_mean_delay:.2f}%",
            ha="left",
            va="bottom",
            fontsize=font_size,
        )
    plt.ylabel("Delay (s)", fontsize=font_size)
    plt.xlabel("Service", fontsize=font_size)
    plt.title(f"Delay for each {agent_name} services", fontsize=font_size)
    plt.xticks(rotation=45)

    # adjust tick size
    plt.tick_params(axis="x", which="major", labelsize=font_size)
    plt.tick_params(axis="y", which="major", labelsize=font_size)

    # adjust layout
    plt.tight_layout()

    # save the plot
    if save_dir:
        os.makedirs(save_dir, exist_ok=True)
        plt.savefig(os.path.join(save_dir, f"service_delay_bars.png"))

    plt.show()


def plot_sum_delay_bars(
    df: pd.DataFrame, services: list = ["client.naive_rag"], save_dir: str = "plots"
):
    plt.clf()
    font_size = 20
    agent_name = get_agent_name(df)
    e2e_sum_delay = df[df["service_name"] == agent_name]["delay"].sum()
    data = df[df["service_name"].isin(services)]
    stats_df = generate_stats(data)
    stats_df["sum_time"] = stats_df["mean_delay"] * stats_df["num_requests"]
    ordered_services = stats_df.sort_values("sum_time", ascending=False)[
        "service_name"
    ].values.tolist()
    # plot the delays for each service
    sns.barplot(
        x="service_name",
        y="sum_time",
        data=stats_df,
        order=ordered_services,
        hue="service_name",
        palette=service_color_palette,
        dodge=False,
    )

    # add the mean delay for each service
    for service in ordered_services:
        plt.text(
            service,
            stats_df[stats_df["service_name"] == service]["sum_time"].values[0],
            f"{100*stats_df[stats_df['service_name'] == service]['sum_time'].values[0] / e2e_sum_delay:.2f}%",
            ha="left",
            va="bottom",
            fontsize=font_size,
        )

    plt.ylabel("Total Time (s)", fontsize=font_size)
    plt.xlabel("Service", fontsize=font_size)
    plt.title(f"Total Time for {agent_name} services", fontsize=font_size)
    plt.xticks(rotation=45)

    # adjust tick size
    plt.tick_params(axis="x", which="major", labelsize=font_size)
    plt.tick_params(axis="y", which="major", labelsize=font_size)

    # adjust layout
    plt.tight_layout()

    # save the plot
    if save_dir:
        os.makedirs(save_dir, exist_ok=True)
        plt.savefig(os.path.join(save_dir, f"service_sum_time_bars.png"))

    plt.show()


# %%


def print_latency_breakdown(exp_dir: str, verbose: bool = False):
    df = load_monitor_data(
        exp_dir,
        warmup=10,
        cooldown=0,
    )
    services = indexing_services + chatting_services
    # data = df[df["service_name"].isin(services)]
    # stats_df = generate_stats(data)
    stats_df = generate_stats(df)

    e2e_stats = stats_df[stats_df["service_name"] == get_agent_name(df)]
    e2e_throughput = e2e_stats["throughput"].values[0]
    e2e_lat = e2e_stats["mean_delay"].values[0]

    indexing_lat = stats_df[stats_df["service_name"] == "indexing"][
        "mean_delay"
    ].values[0]
    chatting_lat = stats_df[stats_df["service_name"].isin(chatting_entry_services)][
        "mean_delay"
    ].values[0]
    print(
        f"{os.path.basename(exp_dir)}: {e2e_throughput:.3f}, {e2e_lat:.3f}, {indexing_lat:.3f}, {chatting_lat:.3f}"
    )
    # print(f"Services: {services}")
    values = []
    for service in services:
        if service not in stats_df["service_name"].values:
            if verbose:
                print(f"Service {service} not found in the data")
            values.append(0)
            continue
        avg_delay = stats_df[stats_df["service_name"] == service]["mean_delay"].values[
            0
        ]
        total_time = stats_df[stats_df["service_name"] == service]["total_time"].values[
            0
        ]
        if verbose:
            print(
                f"Service: {service}, Avg Delay: {avg_delay:.2f}s, Total Time: {total_time:.2f}s"
            )
        values.append(avg_delay)

    if "dynamic_rag" in df["service_name"].values:
        # first find all reques_id group that has vdb_search
        vdb_search_request_ids = df[
            df["service_name"].isin(["vdb_search", "batch_vdb_search"])
        ]["request_id"]
        vdb_search_request_ids = vdb_search_request_ids.unique()

        retrieval_branch_stats = generate_stats(
            df[df["request_id"].isin(vdb_search_request_ids)]
        )
        retrieval_branch_generation_avg_delay = retrieval_branch_stats[
            retrieval_branch_stats["service_name"] == "generation"
        ]["mean_delay"].values[0]
        retrieval_branch_count = retrieval_branch_stats[
            retrieval_branch_stats["service_name"] == "generation"
        ]["num_requests"].values[0]
        assert retrieval_branch_count == len(vdb_search_request_ids)

        non_retrieval_branch_stats = generate_stats(
            df[~df["request_id"].isin(vdb_search_request_ids)]
        )
        non_retrieval_branch_generation_avg_delay = non_retrieval_branch_stats[
            non_retrieval_branch_stats["service_name"] == "generation"
        ]["mean_delay"].values[0]
        non_retrieval_branch_count = non_retrieval_branch_stats[
            non_retrieval_branch_stats["service_name"] == "generation"
        ]["num_requests"].values[0]
        assert non_retrieval_branch_count == (
            df["request_id"].nunique() - len(vdb_search_request_ids)
        ), f"{retrieval_branch_count} != {df['request_id'].nunique()} - {len(vdb_search_request_ids)}"

        if verbose:
            print(
                f"Retrieval Branch Generation Avg Delay: {retrieval_branch_generation_avg_delay:.2f}s, count: {retrieval_branch_count}"
            )
            print(
                f"Non-Retrieval Branch Generation Avg Delay: {non_retrieval_branch_generation_avg_delay:.2f}s, count: {non_retrieval_branch_count}"
            )

        values[-1] = retrieval_branch_generation_avg_delay
        values.append(non_retrieval_branch_generation_avg_delay)
        print(f"non_retrieval_branch_count: {non_retrieval_branch_count}")
    print(", ".join([f"{v:.3f}" for v in values]))
    return values


def plot_single(exp_dir: str, save_dir: str):
    df = load_monitor_data(
        exp_dir,
        warmup=10,
        cooldown=0,
    )
    # print(df.columns, df["service_name"].unique())
    if "naive" in exp_dir:
        client_service = "client.naive_rag"
    elif "advanced" in exp_dir:
        client_service = "client.advanced_rag"
    elif "dynamic" in exp_dir:
        client_service = "client.dynamic_rag"
    elif "contextual" in exp_dir:
        client_service = "client.contextual_rag"
    else:
        raise ValueError("Unknown exp_dir")
    plot_delay_distribution(df, service_name=client_service, save_dir=save_dir)
    plot_delay_distributions(df, services=all_services, save_dir=save_dir)
    plot_avg_delay_bars(
        df, services=indexing_services + chatting_services, save_dir=save_dir
    )
    plot_sum_delay_bars(
        df, services=indexing_services + chatting_services, save_dir=save_dir
    )


def get_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("--exp_dir", "-e", type=str, required=True)
    parser.add_argument("--save_dir", "-s", type=str, default="./.cache/plots")
    parser.add_argument("--noplot", action="store_true")
    args = parser.parse_args()

    if not os.path.exists(args.exp_dir):
        raise FileNotFoundError(f"{args.exp_dir} does not exist")
    args.save_dir = os.path.join(args.save_dir, args.exp_dir.split("/")[-1])
    os.makedirs(args.save_dir, exist_ok=True)

    return args


# %%
if __name__ == "__main__":
    args = get_args()

    if "results" in os.listdir(args.exp_dir):
        if args.noplot:
            print_latency_breakdown(args.exp_dir)
        else:
            plot_single(args.exp_dir, args.save_dir)
            exp = args.exp_dir.split("/")[-1]
            print(f"{exp} Plots saved to {args.save_dir}")
    else:
        for exp in sorted(os.listdir(args.exp_dir)):
            exp_dir = os.path.join(args.exp_dir, exp)
            if os.path.isdir(exp_dir) and "results" in os.listdir(exp_dir):
                if args.noplot:
                    print_latency_breakdown(exp_dir)
                else:
                    print(f"Plotting {exp}")
                    plot_single(exp_dir, os.path.join(args.save_dir, exp))
                    print(f"{exp} Plots saved to {args.save_dir}/{exp}")
