# %%
import os, sys
import numpy as np
import json
import tqdm
import matplotlib.pyplot as plt
import pandas as pd
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import argparse

import utils as analysis_utils
from utils import COL_NAMES
from utils import load_csv, load_client_csv, load_server_csv
from utils import load_csv, load_client_csv, load_server_csv
from utils import load_monitor_data, is_exp_dir
from utils import generate_stats
from utils import load_all_bench, load_exps

from breakdown import (
    client_services,
    entry_services,
    indexing_services,
    chatting_services,
    all_services,
)
from breakdown import service_color_palette

# %%


def plot_delay_distribution(
    df: pd.DataFrame, service_name: str, output_dir: str = None, ax=None
):
    if ax is None:
        local_ax = True
        fig, ax = plt.subplots(figsize=(12, 6))
    df = df[df["service_name"] == service]
    sns.histplot(df, x="delay", kde=True, ax=ax)
    # add vertical lines for mean, median, p95, p99
    ax.axvline(df["delay"].mean(), color="red", linestyle="--", label="mean")
    ax.axvline(df["delay"].median(), color="green", linestyle="--", label="median")
    ax.axvline(df["delay"].quantile(0.95), color="orange", linestyle="--", label="p95")
    ax.axvline(df["delay"].quantile(0.99), color="purple", linestyle="--", label="p99")
    ax.legend()
    ax.set_title(f"Delay distribution for {service_name}")
    ax.set_xlabel("Delay (s)")
    ax.set_ylabel("Count")
    if local_ax and output_dir:
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, f"{service_name}_delay_distribution.png"))
    elif local_ax:
        print(f"Delay distribution for {service_name}")
        print(f"{df['delay'].describe()}")


def get_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("-e", "--exp_dir", type=str, required=True)
    parser.add_argument("-o", "--output_dir", type=str, default=None)
    parser.add_argument("-s", "--services", nargs="+", type=str, default=None)
    parser.add_argument("-t", "--targets", nargs="+", type=str, default=None)
    parser.add_argument("-bts", "--begin-ts", type=str, default=None)
    parser.add_argument("-ets", "--end-ts", type=str, default=None)
    parser.add_argument("--warmup", type=int, default=10)
    parser.add_argument("--cooldown", type=int, default=0)
    parser.add_argument("--interactive", action="store_true")
    parser.add_argument("--client-only", action="store_true")
    parser.add_argument("--no-client", action="store_true")
    parser.add_argument("--exclude-services", nargs="+", type=str, default=None)

    args = parser.parse_args()

    if args.output_dir is not None:
        os.makedirs(args.output_dir, exist_ok=True)

    return args


if __name__ == "__main__":
    args = get_args()
    df = load_monitor_data(
        exp_dir=args.exp_dir, warmup=args.warmup, cooldown=args.cooldown
    )
    if args.services is not None:
        df = df[df["service_name"].isin(args.services)]

    for service in df["service_name"].unique():
        plot_delay_distribution(
            df[df["service_name"] == service], service, output_dir=args.output_dir
        )

# %%
