# %%
import os, sys
import numpy as np
import json
import tqdm
import matplotlib.pyplot as plt
import pandas as pd
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import argparse
from functools import cache

import utils as analysis_utils
from utils import COL_NAMES
from utils import load_csv, load_client_csv, load_server_csv
from utils import load_csv, load_client_csv, load_server_csv
from utils import load_monitor_data, is_exp_dir
from utils import generate_stats
from utils import load_all_bench, load_exps

from breakdown import (
    client_services,
    entry_services,
    indexing_services,
    chatting_services,
    all_services,
)
from breakdown import service_color_palette


# %%
def plot_timeline(
    data: pd.DataFrame,
    services: list | None = None,
    freq: str = "3s",
    target: str = "active",
    save_dir: str = None,
    interactive: bool = False,
    begin_ts: str = None,
    end_ts: str = None,
    tag: str = "timeline",
    begin_offset: int = 0,
    end_offset: int = 0,
    max_duration: int = 0,
):
    data = data.sort_values("start_time")
    if services is None or len(services) == 0:
        # services = data["service_name"].unique()
        # only service without parent_service or without child_service are selected
        services = []
        for row in data[["service_name", "parent_service_name"]].itertuples():
            if (
                row.service_name not in services
                and row.parent_service_name not in services
            ):
                services.append(row.service_name)

    begin_ts = (
        pd.to_datetime(begin_ts, unit="s")
        if begin_ts is not None
        else data["start_time"].min()
    )
    end_ts = (
        pd.to_datetime(end_ts, unit="s")
        if end_ts is not None
        else data["end_time"].max()
    )
    begin_ts = begin_ts + pd.Timedelta(seconds=begin_offset)
    end_ts = end_ts - pd.Timedelta(seconds=end_offset)
    if max_duration > 0:
        end_ts = min(end_ts, begin_ts + pd.Timedelta(seconds=max_duration))
    data = data[(data["start_time"] >= begin_ts) & (data["end_time"] <= end_ts)]

    heatmap_data = pd.DataFrame()
    call_data = {}
    time_series = pd.date_range(
        start=data["start_time"].min(), end=data["end_time"].max(), freq=freq
    )
    # print(time_series)

    # Aggregate calls for each service
    for service in services:
        service_df = data[data["service_name"] == service]
        if service_df.empty:
            continue

        if target == "active":
            # Count the number of active calls in each interval
            call_counts = service_df.apply(
                lambda row: (
                    (time_series >= row["start_time"]) & (time_series < row["end_time"])
                ).astype(int),
                axis=1,
            )
        elif target == "in":
            # Count the number of incoming calls in each interval
            call_counts = service_df.apply(
                lambda row: (
                    (time_series <= row["start_time"])
                    & (time_series.shift(1) > row["start_time"])
                ).astype(int),
                axis=1,
            )
        elif target == "out":
            # Count the number of outgoing calls in each interval
            call_counts = service_df.apply(
                lambda row: (
                    (time_series >= row["end_time"])
                    & (time_series.shift(-1) < row["end_time"])
                ).astype(int),
                axis=1,
            )
        elif target == "in-or-out":
            # Count the number of processing calls in each interval
            call_counts = service_df.apply(
                lambda row: (
                    (
                        (time_series <= row["start_time"])
                        & (time_series.shift(1) > row["start_time"])
                    )
                    | (
                        (time_series >= row["end_time"])
                        & (time_series.shift(-1) < row["end_time"])
                    )
                ).astype(int),
                axis=1,
            )
        elif target == "in-and-out":
            # Count the number of processing calls in each interval
            call_counts = service_df.apply(
                lambda row: (
                    (time_series <= row["start_time"])
                    & (time_series.shift(1) > row["end_time"])
                ).astype(int),
                axis=1,
            )
        else:
            raise ValueError("Invalid target type")

        call_counts = call_counts.sum(axis=0)

        mean_delay = service_df["delay"].mean()
        # Add the aggregated data to the heatmap DataFrame
        heatmap_data[f"{service}({mean_delay:.2f}s)"] = call_counts
        call_data[service] = call_counts

    # Transpose the DataFrame for heatmap visualization
    heatmap_data = heatmap_data.T

    # save the data with max value
    save_df = heatmap_data.copy()
    save_df["max"] = save_df.max(axis=1)
    save_df = save_df[["max"] + [col for col in save_df.columns if col != "max"]]
    if save_dir is not None:
        save_df.to_csv(
            f"{save_dir}/{tag}_{target}.csv", index=True, index_label="service"
        )
    else:
        print(f"------------------ {target} ------------------")
        print(save_df)
        print("------------------------------------------------\n\n")

    # Create a heatmap
    fig = px.imshow(
        heatmap_data,
        labels=dict(x="Time", y="Service", color="Call Count"),
        x=time_series,
        y=heatmap_data.index,
        # color_continuous_scale="Viridis",
    )

    # Update layout for better visualization
    fig.update_layout(
        title=f"Aggregated {target} Service Call Heatmap",
        xaxis_title="Time",
        yaxis_title="Service",
        coloraxis_colorbar=dict(title="Call Count"),
    )
    # save and show the plot
    if save_dir is not None:
        fig.write_image(f"{save_dir}/{tag}_{target}.svg")
    if interactive:
        fig.show()

    # Create line plots for each service
    fig = go.Figure()
    for service in services:
        if service not in call_data.keys():
            continue
        x = time_series
        y = call_data[service]
        fig.add_trace(go.Scatter(x=x, y=y, mode="lines", name=service))
    fig.update_layout(
        xaxis_title="Time",
        yaxis_title=f"{target} Call Count",
        title=f"Aggregated {target} Service Call Count",
        # showlegend=True,
        # legend=dict(
        #     title="Services",  # Optional: Add a title to the legend
        #     x=1.02,           # Position legend to the right of the plot
        #     y=1,
        #     xanchor="left",   # Anchor legend at the left side of the x position
        #     yanchor="top"     # Anchor legend at the top of the y position
        # ),
        # margin=dict(r=150)    # Ensure right margin is large enough for the legend
    )
    # save and show the plot
    if save_dir is not None:
        fig.write_image(f"{save_dir}/{tag}_{target}_line.svg")
    if interactive:
        fig.show()


def plot_moving_delay(
    data: pd.DataFrame,
    services: list | None = None,
    freq: str = "3s",
    save_dir: str = None,
    interactive: bool = False,
    begin_ts: str = None,
    end_ts: str = None,
    relative_time: bool = False,
    tag: str = "delay",
    begin_offset: int = 0,
    end_offset: int = 0,
    max_duration: int = 0,
):
    data = data.sort_values("start_time")
    if services is None or len(services) == 0:
        # services = data["service_name"].unique()
        # only service without parent_service or without child_service are selected
        services = []
        for row in data[["service_name", "parent_service_name"]].itertuples():
            if (
                row.service_name not in services
                and row.parent_service_name not in services
            ):
                services.append(row.service_name)

    begin_ts = (
        pd.to_datetime(begin_ts, unit="s")
        if begin_ts is not None
        else data["start_time"].min()
    )
    end_ts = (
        pd.to_datetime(end_ts, unit="s")
        if end_ts is not None
        else data["end_time"].max()
    )
    begin_ts = begin_ts + pd.Timedelta(seconds=begin_offset)
    end_ts = end_ts - pd.Timedelta(seconds=end_offset)
    if max_duration > 0:
        end_ts = min(end_ts, begin_ts + pd.Timedelta(seconds=max_duration))
    data = data[(data["start_time"] >= begin_ts) & (data["end_time"] <= end_ts)]

    data = data[data["service_name"].isin(services)].sort_values("end_time")
    data = data.set_index("end_time")

    # for each service, calculate the moving average of the delay
    moving_avg_data = {}
    for service in services:
        service_df: pd.DataFrame = data[data["service_name"] == service]
        if service_df.empty:
            continue
        service_df = service_df.sort_values("end_time")
        moving_avg = service_df["delay"].rolling(freq).mean()
        moving_avg_data[service] = {
            "ts": moving_avg.index,
            "delays": moving_avg.values,
        }
    save_df = None
    min_ts = data.index.min()
    for service in services:
        if service not in moving_avg_data.keys():
            continue
        x = moving_avg_data[service]["ts"]
        y = moving_avg_data[service]["delays"]
        x = (x - min_ts).total_seconds()
        if save_df is None:
            save_df = pd.DataFrame({"ts": x, service: y})
        else:
            save_df = save_df.merge(
                pd.DataFrame({"ts": x, service: y}), on="ts", how="outer"
            )
    save_df = save_df.set_index("ts")
    if save_dir is not None:
        save_df.to_csv(f"{save_dir}/moving_avg_{tag}.csv", index=True, index_label="ts")
    else:
        print(f"------------------ Moving Average Delay ------------------")
        print(save_df.T)
        print("------------------------------------------------\n\n")

    # crete line plots for each service
    fig, ax = plt.subplots(figsize=(10, 6))
    for service in services:
        if service not in moving_avg_data.keys():
            continue
        x = moving_avg_data[service]["ts"]
        y = moving_avg_data[service]["delays"]
        ax.plot(x, y, label=service)
    ax.set_xlabel("Time")
    ax.set_ylabel("Moving Average Delay (s)")
    # ax.set_xscale("log")
    # ax.set_yscale("log")
    ax.set_title("Moving Average Delay of Services")
    if relative_time:
        ax.xaxis.set_major_formatter(mdates.DateFormatter("%H:%M:%S"))
        ax.xaxis.set_major_locator(mdates.MinuteLocator(interval=1))
        plt.xticks(rotation=45)
    ax.legend()
    # save and show the plot
    if save_dir is not None:
        fig.savefig(f"{save_dir}/moving_avg_{tag}.png")

    # Create line plots for each service
    fig = go.Figure()
    for service in services:
        if service not in moving_avg_data.keys():
            continue
        x = moving_avg_data[service]["ts"]
        y = moving_avg_data[service]["delays"]
        fig.add_trace(
            go.Scatter(
                x=x,
                y=y,
                mode="lines",
                name=service,
            )
        )

    fig.update_layout(
        xaxis_title="Time",
        yaxis_title="Moving Average Delay (s)",
        title="Moving Average Delay of Services",
        # showlegend=True,
        # legend=dict(
        #     title="Services",  # Optional: Add a title to the legend
        #     x=1.02,           # Position legend to the right of the plot
        #     y=1,
        #     xanchor="left",   # Anchor legend at the left side of the x position
        #     yanchor="top"     # Anchor legend at the top of the y position
        # ),
        # margin=dict(r=150)    # Ensure right margin is large enough for the legend
    )
    # save and show the plot
    if save_dir is not None:
        fig.write_image(f"{save_dir}/moving_avg_{tag}.svg")
    if interactive:
        fig.show()


def get_args():
    parser = argparse.ArgumentParser()
    # parser.add_argument("-es", "--exp_dirs", nargs="+", type=str)
    # parser.add_argument("-name", "--exp_name", type=str, default=None)
    parser.add_argument("-e", "--exp_dir", type=str, required=True)
    parser.add_argument("-o", "--output_dir", type=str, default=None)
    parser.add_argument("-s", "--services", nargs="+", type=str, default=None)
    parser.add_argument("-f", "--frequency", type=str, default="1s")
    parser.add_argument("-t", "--targets", nargs="+", type=str, default=None)
    parser.add_argument("-bts", "--begin-ts", type=str, default=None)
    parser.add_argument("-ets", "--end-ts", type=str, default=None)
    parser.add_argument("-boff", "--begin-offset", type=int, default=0)
    parser.add_argument("-eoff", "--end-offset", type=int, default=0)
    parser.add_argument("-maxd", "--max_duration", type=int, default=0)
    parser.add_argument("--warmup", type=int, default=10)
    parser.add_argument("--cooldown", type=int, default=0)
    parser.add_argument("--interactive", action="store_true")
    parser.add_argument("--client-only", action="store_true")
    parser.add_argument("--no-client", action="store_true")
    parser.add_argument("-rt", "--relative-time", action="store_true")
    parser.add_argument("--exclude-services", nargs="+", type=str, default=None)

    args = parser.parse_args()

    if args.output_dir is not None:
        os.makedirs(args.output_dir, exist_ok=True)

    return args


def get_services(args):
    if args.services is not None:
        services = args.services
    else:
        services = []
        services += client_services
        services += entry_services
        services += indexing_services
        services += chatting_services
    if args.client_only:
        services = [
            f"client.{service}"
            for service in services
            if not service.startswith("client")
        ]
    elif args.no_client:
        services = [
            service for service in services if not service.startswith("client.")
        ]

    if args.exclude_services is not None:
        services = [
            service for service in services if service not in args.exclude_services
        ]
    return services


@cache
def load_vllm_request_metrics(exp_dir: str, warmup: int = 10, cooldown: int = 0):
    metrics_dir = os.path.join(exp_dir, "results/vllm-embedding/vllm_request_metrics")
    file_path = os.path.join(metrics_dir, "request_metrics.csv")
    df = pd.read_csv(file_path)
    df = df.iloc[warmup : len(df) - cooldown]
    # arrival_time looks like 1743610335.0016234, convert to datetime
    df["start_time"] = pd.to_datetime(df["arrival_time"], unit="s")
    df["end_time"] = pd.to_datetime(df["finished_time"], unit="s")
    df["delay"] = df["end_time"] - df["start_time"]
    df["delay"] = df["delay"].dt.total_seconds()
    df["service_name"] = "vllm.embedding"
    return df


if __name__ == "__main__":
    args = get_args()
    services = get_services(args)

    freq = args.frequency

    targets = []
    if args.targets is not None:
        targets = args.targets
    else:
        targets = ["active", "in", "out", "in-or-out", "in-and-out", "delay"]

    warmup = args.warmup
    cooldown = args.cooldown
    interactive = args.interactive

    def plot_single(
        exp_dir: str,
        output_dir: str,
        begin_ts: str = None,
        end_ts: str = None,
        begin_offset: int = 0,
        end_offset: int = 0,
        max_duration: int = 0,
    ):
        df = load_monitor_data(exp_dir=exp_dir, warmup=warmup, cooldown=cooldown)

        for target in targets:
            if target == "delay":
                plot_moving_delay(
                    df,
                    services=services,
                    freq=freq,
                    save_dir=output_dir,
                    interactive=interactive,
                    begin_ts=begin_ts,
                    end_ts=end_ts,
                    relative_time=args.relative_time,
                    begin_offset=begin_offset,
                    end_offset=end_offset,
                    max_duration=max_duration,
                )
            elif target.startswith("vllm"):
                vllm_df = load_vllm_request_metrics(
                    exp_dir=exp_dir, warmup=warmup, cooldown=cooldown
                )
                if target == "vllm-delay":
                    plot_moving_delay(
                        vllm_df,
                        services=services,
                        freq=freq,
                        save_dir=output_dir,
                        interactive=interactive,
                        begin_ts=begin_ts,
                        end_ts=end_ts,
                        relative_time=args.relative_time,
                        tag="vllm-delay",
                        begin_offset=begin_offset,
                        end_offset=end_offset,
                        max_duration=max_duration,
                    )
                else:
                    plot_timeline(
                        vllm_df,
                        services=services,
                        freq=freq,
                        target=target.replace("vllm-", ""),
                        save_dir=output_dir,
                        interactive=interactive,
                        begin_ts=begin_ts,
                        end_ts=end_ts,
                        tag="vllm-timeline",
                        begin_offset=begin_offset,
                        end_offset=end_offset,
                        max_duration=max_duration,
                    )
            else:
                plot_timeline(
                    df,
                    services=services,
                    freq=freq,
                    target=target,
                    save_dir=output_dir,
                    interactive=interactive,
                    begin_ts=begin_ts,
                    end_ts=end_ts,
                    begin_offset=begin_offset,
                    end_offset=end_offset,
                    max_duration=max_duration,
                )

    if is_exp_dir(args.exp_dir):
        plot_single(
            exp_dir=args.exp_dir,
            output_dir=args.output_dir,
            begin_ts=args.begin_ts,
            end_ts=args.end_ts,
            begin_offset=args.begin_offset,
            end_offset=args.end_offset,
            max_duration=args.max_duration,
        )
    else:
        # walk through the directory and plot_single for each dir that is_exp_dir
        base_tag = os.path.basename(args.exp_dir)
        for exp_name in os.listdir(args.exp_dir):
            exp_dir = os.path.join(args.exp_dir, exp_name)
            if is_exp_dir(exp_dir):
                output_dir = None
                if args.output_dir is not None:
                    output_dir = os.path.join(
                        args.output_dir,
                        base_tag,
                        os.path.basename(exp_name),
                        "timeline",
                    )
                    os.makedirs(output_dir, exist_ok=True)
                plot_single(
                    exp_dir=exp_dir,
                    output_dir=output_dir,
                    begin_ts=args.begin_ts,
                    end_ts=args.end_ts,
                    begin_offset=args.begin_offset,
                    end_offset=args.end_offset,
                    max_duration=args.max_duration,
                )

# %%
