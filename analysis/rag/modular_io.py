# %%
import os, sys
import numpy as np
import json
import tqdm
import matplotlib.pyplot as plt
import pandas as pd
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import argparse
import logging

import utils as analysis_utils
from utils import COL_NAMES
from utils import load_csv, load_client_csv, load_server_csv
from utils import load_csv, load_client_csv, load_server_csv
from utils import load_monitor_data
from utils import generate_stats
from utils import load_all_bench

from fast_service.benchmark.request_loader import RequestLoader
from fast_service.benchmark.intermediate import module_benchmark as mbench

from rag import rag_fsm, init_settings
from rag import RAGE2EInput, RAGE2EOutput
from rag import naive_rag
from rag import advanced_rag
from rag import dynamic_rag
from rag import contextual_rag
from rag.settings import (
    SplitterSettings,
    VDBSettings,
    EmbeddingSettings,
    RetrieverSettings,
    GenerationSettings,
)

from breakdown import (
    client_services,
    entry_services,
    indexing_services,
    chatting_services,
    all_services,
)
from breakdown import service_color_palette


# %%
def get_all_settings():
    splitter_settings = SplitterSettings.from_default_settings()
    vdb_settings = VDBSettings.from_default_settings()
    embedding_settings = EmbeddingSettings.from_default_settings()
    retriever_settings = RetrieverSettings.from_default_settings()
    generation_settings = GenerationSettings.from_default_settings()

    return (
        splitter_settings,
        vdb_settings,
        embedding_settings,
        retriever_settings,
        generation_settings,
    )


def wiki_qa_parser(line: str):
    (
        splitter_settings,
        vdb_settings,
        embedding_settings,
        retriever_settings,
        generation_settings,
    ) = get_all_settings()
    qid, question, wiki_query = line.strip().split("\t")
    vdb_settings.collection_name = f"query_{qid}"
    return RAGE2EInput(
        query=question,
        uri=wiki_query,
        type="wiki",
        splitter_settings=splitter_settings,
        vdb_settings=vdb_settings,
        embedding_settings=embedding_settings,
        retriever_settings=retriever_settings,
        generation_settings=generation_settings,
    )


def ms_marco_parser(line: str):
    (
        splitter_settings,
        vdb_settings,
        embedding_settings,
        retriever_settings,
        generation_settings,
    ) = get_all_settings()
    query_id, query, query_type, urls_str = line.strip().split("\t")
    urls = eval(urls_str)
    vdb_settings.collection_name = f"query_{query_id}"
    return RAGE2EInput(
        query=query,
        uri=urls,
        type="web",
        splitter_settings=splitter_settings,
        vdb_settings=vdb_settings,
        embedding_settings=embedding_settings,
        retriever_settings=retriever_settings,
        generation_settings=generation_settings,
    )


def natural_question_parser(line: str):
    (
        splitter_settings,
        vdb_settings,
        embedding_settings,
        retriever_settings,
        generation_settings,
    ) = get_all_settings()
    query_id, query, url, title, path = line.strip().split("\t")
    # query_id is int, we want it to be non-negative
    vdb_settings.collection_name = f"query_{query_id}".replace("-", "_")
    return RAGE2EInput(
        query=query,
        uri=path,
        type="local_html",
        splitter_settings=splitter_settings,
        vdb_settings=vdb_settings,
        embedding_settings=embedding_settings,
        retriever_settings=retriever_settings,
        generation_settings=generation_settings,
    )


DS2PARSER = {
    "wiki_qa": wiki_qa_parser,
    "ms_marco": ms_marco_parser,
    "natural_questions": natural_question_parser,
}


def get_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("-s", "--storage", type=str, required=True)
    parser.add_argument("-n", "--request_num", default=20, type=int)
    parser.add_argument("-w", "--warm_up", default=10, type=int)
    parser.add_argument(
        "-o", "--output_file", type=str, default="./.cache/modular_io/stats.csv"
    )

    parser.add_argument("--log", type=str, default=None)
    args = parser.parse_args()

    if args.storage.startswith("auto::"):
        args.storage = args.storage.replace("auto::", "")
        tag = os.path.basename(args.storage)
        args.storage = os.path.join(
            args.storage, "results/server/internal/intermediate/requests"
        )
        args.output_file = os.path.join("./.cache/plots/modular_io", tag, "stats.csv")

    if not os.path.exists(os.path.dirname(args.output_file)):
        os.makedirs(os.path.dirname(args.output_file))

    if args.log is not None:
        logging.basicConfig(
            filename=args.log,
            level=logging.INFO,
            format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        )
    else:
        logging.basicConfig(
            level=logging.INFO,
            format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        )

    return args


def get_request_loader(args, module_name: str, function_name: str):
    _, descriptor = mbench.get_function_invoker(
        module_name, function_name, fast_service_manager=rag_fsm
    )
    dir_path = os.path.join(args.storage, f"{module_name}.{function_name}")
    line_to_request = mbench.get_request_loader(dir_path, descriptor)
    req_path = os.path.join(dir_path, "requests.txt")
    loader = RequestLoader(
        line_to_request=line_to_request,
        file_path=req_path,
        request_num=args.request_num + args.warm_up,
        buffer_size=100,
    )
    for i in range(args.warm_up):
        loader.next()
    return loader


# %%

if __name__ == "__main__":
    args = get_args()
    data = []
    for subdir in os.listdir(args.storage):
        print(f"analyzing {subdir}")
        if len(subdir.split(".")) < 2:
            print(f"skipping {subdir}")
            continue
        function_name = subdir.split(".")[-1]
        module_name = ".".join(subdir.split(".")[:-1])
        loader = get_request_loader(args, module_name, function_name)
        for i in range(args.request_num):
            if not loader.has_next():
                break
            record: dict = loader.next()
            element = {
                "module_name": module_name,
                "function_name": function_name,
                "id": i,
                "input_length": len(record.__str__()),
            }
            data.append(element)
    df = pd.DataFrame(data)
    df.to_csv(args.output_file, index=False)

    # print stats
    print(df.drop(columns=["id"]).groupby(["function_name"]).describe())

    # plot the distribution of the input_length for each function_name
    all_functions = df["function_name"].unique()
    plotted_functions = [
        service
        for service in indexing_services + chatting_services
        if service in all_functions
    ]
    df = df[df["function_name"].isin(plotted_functions)]
    N = len(df["function_name"].unique())
    nrows = np.sqrt(N).astype(int)
    ncols = np.ceil(N / nrows).astype(int)
    font_size = 20
    # fig, axes = plt.subplots(nrows=nrows, ncols=ncols, figsize=(ncols * 8, nrows * 8), sharex=True, sharey=True)
    fig, axes = plt.subplots(nrows=nrows, ncols=ncols, figsize=(ncols * 8, nrows * 8))
    axes = axes.flatten()

    # for i, (function_name, group) in enumerate(df.groupby(["function_name"])):
    df_gyf = df.groupby(by="function_name")
    for i, function_name in enumerate(plotted_functions):
        group = df_gyf.get_group(function_name)
        ax = axes[i]
        color = service_color_palette[function_name]
        sns.histplot(group, x="input_length", ax=ax, color=color)
        # add vertical lines for the mean, p50, p95 and p99
        mean = group["input_length"].mean()
        p50 = group["input_length"].quantile(0.5)
        p95 = group["input_length"].quantile(0.95)
        p99 = group["input_length"].quantile(0.99)
        ax.axvline(mean, color="r", linestyle="--", label="mean")
        ax.axvline(p50, color="g", linestyle="--", label="p50")
        ax.axvline(p95, color="b", linestyle="--", label="p95")
        ax.axvline(p99, color="y", linestyle="--", label="p99")
        ax.legend()
        ax.set_title(function_name, fontsize=font_size)
        ax.set_xlabel("input_length", fontsize=font_size)
        ax.set_ylabel("count", fontsize=font_size)
        ax.tick_params(axis="both", which="major", labelsize=font_size, rotation=45)
        # ax.tick_params(axis="both", which="minor", labelsize=font_size, rotation=45)
        # ax.set_xscale("log")

    # remove the empty axes
    for i in range(N, nrows * ncols):
        fig.delaxes(axes[i])

    # tight layout
    plt.tight_layout()

    # save the plot
    plt.savefig(args.output_file.replace(".csv", ".png"))
