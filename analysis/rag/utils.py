# %%
import os
import numpy as np
import json
import matplotlib.pyplot as plt
import pandas as pd
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
import matplotlib.pyplot as plt
import matplotlib.dates as mdates


# %%
COL_NAMES = [
    "end_ts",
    "request_id",
    "parent_service_name",
    "service_name",
    "delay",
    "call_id",
    "parent_call_id",
]


def load_csv(filename):
    df = pd.read_csv(filename, names=COL_NAMES)
    df["start_ts"] = df["end_ts"] - df["delay"]
    df["start_time"] = pd.to_datetime(df["start_ts"], unit="s")
    df["end_time"] = pd.to_datetime(df["end_ts"], unit="s")
    df["row_id"] = df.index
    df["service_name"] = df["service_name"].apply(lambda x: x.split(".")[-1])
    df["parent_service_name"] = df["parent_service_name"].apply(
        lambda x: x.split(".")[-1] if isinstance(x, str) else "None"
    )
    return df


def load_client_csv(filename):
    df = load_csv(filename)
    df["service_name"] = df["service_name"].apply(lambda x: "client." + x)
    return df


def load_server_csv(filename):
    df = load_csv(filename)
    return df


def is_exp_dir(exp_dir: str):
    if exp_dir is None:
        return False
    if not os.path.isdir(exp_dir):
        return False
    return os.path.exists(os.path.join(exp_dir, "results"))


def load_all_client_monitor(monitor_dir: str):
    client_dfs = []
    target_dirs = []
    if os.path.exists(os.path.join(monitor_dir, "client", "monitor")):
        target_dirs.append(os.path.join(monitor_dir, "client", "monitor"))
    else:
        for subdir in os.listdir(monitor_dir):
            if os.path.exists(os.path.join(monitor_dir, subdir, "client", "monitor")):
                target_dirs.append(
                    os.path.join(monitor_dir, subdir, "client", "monitor")
                )
    for target_dir in target_dirs:
        for dir in os.listdir(target_dir):
            if dir.startswith("rag_client") and not dir.endswith("merged"):
                client_path = os.path.join(target_dir, dir, "remote_delay.csv")
                if os.path.exists(client_path):
                    client_df = load_client_csv(client_path)
                    client_df["source"] = dir
                    client_dfs.append(client_df)
                client_path = os.path.join(target_dir, dir, "e2e_delay.csv")
                if os.path.exists(client_path):
                    client_df = load_client_csv(client_path)
                    client_df["source"] = dir
                    client_dfs.append(client_df)
    client_df = pd.concat(client_dfs, axis=0)
    return client_df


def load_all_server_monitor(monitor_dir: str):
    server_dfs = []
    for dir in os.listdir(monitor_dir):
        if dir.startswith("server"):
            for sub_dir in os.listdir(os.path.join(monitor_dir, dir, "monitor")):
                if sub_dir.startswith("rag_server"):
                    server_path = os.path.join(
                        monitor_dir, dir, "monitor", sub_dir, "e2e_delay.csv"
                    )
                    if os.path.exists(server_path):
                        server_df = load_server_csv(server_path)
                        server_dfs.append(server_df)
    if len(server_dfs) == 0:
        return None

    server_df = pd.concat(server_dfs, axis=0)
    return server_df


def load_monitor_data(
    exp_dir: str, warmup: int = 10, cooldown: int = 0
) -> pd.DataFrame:
    if "results" in os.listdir(exp_dir):
        print(f"Loading monitor data from {exp_dir}")
        client_df = load_all_client_monitor(os.path.join(exp_dir, "results"))
    else:
        # walk through exp_dir until find all results
        cliend_dfs = []
        for subdir in os.listdir(exp_dir):
            if "results" in os.listdir(os.path.join(exp_dir, subdir)):
                client_df = load_all_client_monitor(
                    os.path.join(exp_dir, subdir, "results")
                )
                client_df["source"] = subdir + "/" + client_df["source"]
                cliend_dfs.append(client_df)
        if len(cliend_dfs) == 0:
            raise ValueError("No client monitor found in exp_dir")
        client_df = pd.concat(cliend_dfs, axis=0)
        # client_df["source"] = exp_dir

    warmup = warmup * client_df["source"].nunique()
    df_by_request_id = (
        client_df.groupby("request_id").agg("start_time").min().reset_index()
    )
    df_by_request_id.columns = ["request_id", "start_time"]
    df_by_request_id = df_by_request_id.sort_values("start_time").reset_index()
    req_ids = df_by_request_id["request_id"].values[
        warmup : len(df_by_request_id) - cooldown
    ]
    client_df = client_df[client_df["request_id"].isin(req_ids)]

    server_df = load_all_server_monitor(os.path.join(exp_dir, "results"))
    if server_df is not None:
        server_df = server_df[server_df["request_id"].isin(req_ids)]
        df = pd.concat([client_df, server_df], axis=0)
    else:
        df = client_df

    # # load other csv files, as server monitor
    # for dname in os.listdir(os.path.join(exp_dir, "results")):
    #     if dname.startswith("client") or dname.startswith("server"):
    #         continue
    #     for root, dirs, files in os.walk(os.path.join(exp_dir, "results", dname)):
    #         for file in files:
    #             if file.endswith("e2e_delay.csv") and not root.endswith("stats"):
    #                 df_path = os.path.join(root, file)
    #                 tmp = load_server_csv(df_path)
    #                 tmp = tmp[tmp["request_id"].isin(req_ids)]
    #                 df = pd.concat([df, tmp], axis=0)

    df = df.sort_values("start_time").reset_index()
    return df


def generate_stats(df: pd.DataFrame) -> pd.DataFrame:
    df_by_service = df.groupby("service_name")
    # get the stats for each service, including
    # the number of requests, the mean, median, std, min, max delay, and 95th and 99th percentile delay
    # the min start time, max end time, and the total time
    stats = {}
    for service_name, df_service in df_by_service:
        num_requests = df_service.shape[0]
        total_time = (
            df_service["end_time"].max() - df_service["start_time"].min()
        ).total_seconds()
        throughput = num_requests / total_time
        stats[service_name] = {
            "service_name": service_name,
            "throughput": throughput,
            "mean_delay": df_service["delay"].mean(),
            "median_delay": df_service["delay"].median(),
            "std_delay": df_service["delay"].std(),
            "min_delay": df_service["delay"].min(),
            "max_delay": df_service["delay"].max(),
            "p95_delay": df_service["delay"].quantile(0.95),
            "p99_delay": df_service["delay"].quantile(0.99),
            "min_start_time": df_service["start_time"].min(),
            "max_end_time": df_service["end_time"].max(),
            "total_time": total_time,
            "num_requests": num_requests,
        }
    stats_df = (
        pd.DataFrame(stats.values())
        .sort_values("min_start_time")
        .reset_index(drop=True)
    )
    return stats_df


def load_exps(exp_dirs: list, warmup: int = 10, cooldown: int = 0):
    prefix_list = ["naive-", "advanced-", "dynamic-", "contextual-"]
    df_list = []
    df_stats_list = []
    for exp_dir in exp_dirs:
        exp_name = os.path.basename(exp_dir)
        if not any([exp_name.startswith(prefix) for prefix in prefix_list]):
            continue
        rag_type, dataset, mode, lr, nreqs = exp_name.split("-")
        metadata = {
            "exp_dir": exp_dir,
            "exp_name": exp_name,
            "rag_type": rag_type,
            "dataset": dataset,
            "mode": mode,
            "lr": float(lr),
            "nreqs": int(nreqs),
        }
        df = load_monitor_data(exp_dir, warmup=warmup, cooldown=cooldown)
        if len(df) == 0:
            continue
        df_stats = generate_stats(df)
        for key, value in metadata.items():
            df_stats[key] = value
            df[key] = value
        df_list.append(df)
        df_stats_list.append(df_stats)
    df = pd.concat(df_list, axis=0)
    df_stats = pd.concat(df_stats_list, axis=0)
    return df, df_stats


def load_all_bench(bench_dir: str, warmup: int = 10, cooldown: int = 0):
    df, df_stats = load_exps(
        [os.path.join(bench_dir, exp_name) for exp_name in os.listdir(bench_dir)],
        warmup=warmup,
        cooldown=cooldown,
    )

    # if "client.naive_rag" in df["service_name"].values:
    #     df["delay_percentage"] = df["delay"]
    #     client_naive_rag = df[df["service_name"] == "client.naive_rag"]
    #     df = df.merge(
    #         client_naive_rag[["request_id", "exp_name", "delay"]],
    #         on=["request_id", "exp_name"],
    #         suffixes=("", "_client"),
    #     )
    #     df["delay_percentage"] = df["delay"] / df["delay_client"]

    # if "client.naive_rag" in df_stats["service_name"].values:
    #     df_stats["delay_percentage"] = df_stats["mean_delay"]
    #     client_naive_rag = df_stats[df_stats["service_name"] == "client.naive_rag"]
    #     df_stats = df_stats.merge(
    #         client_naive_rag[["exp_name", "mean_delay"]],
    #         on=["exp_name"],
    #         suffixes=("", "_client"),
    #     )
    #     df_stats["delay_percentage"] = (
    #         df_stats["mean_delay"] / df_stats["mean_delay_client"]
    #     )

    return df, df_stats


# %%
