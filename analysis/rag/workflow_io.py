# %%
import os, sys
import numpy as np
import json
import tqdm
import matplotlib.pyplot as plt
import pandas as pd
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import argparse
import logging

import utils as analysis_utils
from utils import COL_NAMES
from utils import load_csv, load_client_csv, load_server_csv
from utils import load_csv, load_client_csv, load_server_csv
from utils import load_monitor_data
from utils import generate_stats
from utils import load_all_bench

from fast_service.benchmark.request_loader import RequestLoader

from rag import rag_fsm, init_settings
from rag import RAGE2EInput, RAGE2EOutput
from rag import naive_rag
from rag import advanced_rag
from rag import dynamic_rag
from rag import contextual_rag
from rag.settings import (
    SplitterSettings,
    VDBSettings,
    EmbeddingSettings,
    RetrieverSettings,
    GenerationSettings,
)


# %%
def get_all_settings():
    splitter_settings = SplitterSettings.from_default_settings()
    vdb_settings = VDBSettings.from_default_settings()
    embedding_settings = EmbeddingSettings.from_default_settings()
    retriever_settings = RetrieverSettings.from_default_settings()
    generation_settings = GenerationSettings.from_default_settings()

    return (
        splitter_settings,
        vdb_settings,
        embedding_settings,
        retriever_settings,
        generation_settings,
    )


def wiki_qa_parser(line: str):
    (
        splitter_settings,
        vdb_settings,
        embedding_settings,
        retriever_settings,
        generation_settings,
    ) = get_all_settings()
    qid, question, wiki_query = line.strip().split("\t")
    vdb_settings.collection_name = f"query_{qid}"
    return RAGE2EInput(
        query=question,
        uri=wiki_query,
        type="wiki",
        splitter_settings=splitter_settings,
        vdb_settings=vdb_settings,
        embedding_settings=embedding_settings,
        retriever_settings=retriever_settings,
        generation_settings=generation_settings,
    )


def ms_marco_parser(line: str):
    (
        splitter_settings,
        vdb_settings,
        embedding_settings,
        retriever_settings,
        generation_settings,
    ) = get_all_settings()
    query_id, query, query_type, urls_str = line.strip().split("\t")
    urls = eval(urls_str)
    vdb_settings.collection_name = f"query_{query_id}"
    return RAGE2EInput(
        query=query,
        uri=urls,
        type="web",
        splitter_settings=splitter_settings,
        vdb_settings=vdb_settings,
        embedding_settings=embedding_settings,
        retriever_settings=retriever_settings,
        generation_settings=generation_settings,
    )


def natural_question_parser(line: str):
    (
        splitter_settings,
        vdb_settings,
        embedding_settings,
        retriever_settings,
        generation_settings,
    ) = get_all_settings()
    query_id, query, url, title, path = line.strip().split("\t")
    # query_id is int, we want it to be non-negative
    vdb_settings.collection_name = f"query_{query_id}".replace("-", "_")
    return RAGE2EInput(
        query=query,
        uri=path,
        type="local_html",
        splitter_settings=splitter_settings,
        vdb_settings=vdb_settings,
        embedding_settings=embedding_settings,
        retriever_settings=retriever_settings,
        generation_settings=generation_settings,
    )


def get_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("-r", "--rag", default="naive")
    parser.add_argument("-d", "--dataset", default="natural_questions")
    parser.add_argument("--data_dir", default="./.cache")
    parser.add_argument("-f", "--file_path", default=None)
    parser.add_argument("-qt", "--query_tokenizer", default=None)
    parser.add_argument("-af", "--answer_file_path", default=None)
    parser.add_argument("-at", "--answer_tokenizer", default=None)
    # parser.add_argument("-c", "--config", default="../config/rag-client.yml")
    # parser.add_argument("-s", "--settings", default="../config/settings.yml")
    # parser.add_argument("-m", "--mode", default="one-by-one")
    parser.add_argument("-n", "--request_num", default=20, type=int)
    parser.add_argument("-w", "--warm_up", default=10, type=int)
    # parser.add_argument("-b", "--batch_size", default=1, type=int)
    # parser.add_argument("-l", "--lambda_rate", default=1.0, type=float)
    parser.add_argument("-o", "--output_file", type=str, default=None)

    # parser.add_argument("--module", type=str, default=None)
    # parser.add_argument("--function", type=str, default=None)
    # parser.add_argument("--intermediate_storage", type=str, default=None)

    parser.add_argument("--log", type=str, default=None)
    args = parser.parse_args()

    if args.file_path is None:
        args.file_path = os.path.join(args.data_dir, f"{args.dataset}.txt")

    if args.output_file is not None:
        if not args.output_file.endswith(".csv"):
            args.output_file = args.output_file + ".csv"
    else:
        tag = f"{args.query_tokenizer.split('/')[-1]}_{args.answer_tokenizer.split('/')[-1]}_{args.request_num}_{args.warm_up}"
        args.output_file = os.path.join(
            "./.cache", "workflow_io", f"{args.dataset}_{tag}stats.csv"
        )

    if args.query_tokenizer is not None:
        from transformers import AutoTokenizer

        args.query_tokenizer = AutoTokenizer.from_pretrained(args.query_tokenizer)

    if args.answer_tokenizer is not None:
        from transformers import AutoTokenizer

        args.answer_tokenizer = AutoTokenizer.from_pretrained(args.answer_tokenizer)

    if not os.path.exists(os.path.dirname(args.output_file)):
        os.makedirs(os.path.dirname(args.output_file))

    if args.log is not None:
        logging.basicConfig(
            filename=args.log,
            level=logging.INFO,
            format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        )
    else:
        logging.basicConfig(
            level=logging.INFO,
            format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        )

    return args


DS2PARSER = {
    "wiki_qa": wiki_qa_parser,
    "ms_marco": ms_marco_parser,
    "natural_questions": natural_question_parser,
}


def get_request_loader(args):
    loader = RequestLoader(
        line_to_request=DS2PARSER[args.dataset],
        file_path=args.file_path,
        request_num=args.request_num + args.warm_up,
        buffer_size=100,
    )
    for i in range(args.warm_up):
        loader.next()
    return loader


def load_response(args) -> list[str]:
    with open(args.answer_file_path, "r") as f:
        lines = f.readlines()
    data = []
    for line in lines:
        answer = line[len("answer=") :][1:-1]
        data.append(answer)
    return data[args.warm_up :]


# %%

if __name__ == "__main__":
    args = get_args()
    loader = get_request_loader(args)

    if args.answer_file_path is not None:
        answers = load_response(args)
    else:
        answers = None

    data = []
    for i in range(args.request_num):
        req = loader.next()
        # count the size in bytes of the request
        qlen = len(req.query)

        # count the number of tokens in req.query
        if args.query_tokenizer is not None:
            query_ntokens = len(args.query_tokenizer.encode(req.query))
        else:
            query_ntokens = None

        element = {
            "request_id": i,
            "query_length": qlen,
            "query_ntokens": query_ntokens,
            "query": req.query,
        }
        if answers is not None:
            if args.answer_tokenizer is not None:
                answer_ntokens = len(args.answer_tokenizer.encode(answers[i]))
            else:
                answer_ntokens = None
            element["answer_length"] = len(answers[i])
            element["answer_ntokens"] = answer_ntokens
            element["answer"] = answers[i]

        data.append(element)

    df = pd.DataFrame(data)
    if len(df) <= 10:
        print(df)

    # print the stats
    print(df.drop(columns=["request_id"]).describe())

    # save the data
    if args.output_file is not None:
        df.to_csv(args.output_file, index=False)

    # plot the distribution of query_length, query_ntokens, answer_length, answer_ntokens
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    cols = ["query_length", "query_ntokens", "answer_length", "answer_ntokens"]

    # add vertical lines for the mean, p50, p95 and p99 for each plot
    for i, ax in enumerate(axes.flatten()):
        col = cols[i]
        sns.histplot(df[cols[i]], ax=ax)
        mean = df[col].mean()
        p50 = df[col].quantile(0.5)
        p95 = df[col].quantile(0.95)
        p99 = df[col].quantile(0.99)
        ax.axvline(mean, color="r", linestyle="--", label=f"mean={mean:.2f}")
        ax.axvline(p50, color="g", linestyle="--", label=f"p50={p50:.2f}")
        ax.axvline(p95, color="b", linestyle="--", label=f"p95={p95:.2f}")
        ax.axvline(p99, color="y", linestyle="--", label=f"p99={p99:.2f}")
        ax.legend()

    # save the plot
    if args.output_file is not None:
        plt.savefig(args.output_file.replace(".csv", ".png"))
    plt.show()
