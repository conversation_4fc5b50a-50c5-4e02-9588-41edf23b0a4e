from pydantic import BaseModel, Field
from fast_service import FastServiceManager, RequestContext, FastServiceFile
from sweagent.environment.swe_env import SWEEnv, EnvironmentArguments
from sweagent.types import AgentInfo
from typing import Any

from concurrent.futures import ThreadPoolExecutor, Future
from queue import Queue
from threading import Lock, Thread
from typing import Callable, Dict, Optional


class EnvThreadManager:
    def __init__(self, max_threads: int | None = None):
        self.executor = ThreadPoolExecutor(max_threads)
        self.env_thread_map: Dict[str, Queue] = {}
        self.lock = Lock()

    def submit_task(self, env_id: str, func: Callable | str, *args, **kwargs) -> Future:
        with self.lock:
            if env_id not in self.env_thread_map:
                raise ValueError(f"Environment with id {env_id} does not exist")

        # Task submission doesn't require holding the lock
        thread_queue = self.env_thread_map[env_id]
        future = Future()
        thread_queue.put((func, args, kwargs, future))
        return future

    def create_env(self, env_id: str, *args, **kwargs) -> Future:
        future = Future()
        with self.lock:
            if env_id in self.env_thread_map:
                future.set_exception(
                    ValueError(f"Environment with id {env_id} already exists")
                )
                return future

            # Create a thread-specific task queue
            thread_queue = Queue()
            self.env_thread_map[env_id] = thread_queue
            Thread(
                target=self._thread_worker,
                args=(thread_queue,),
                daemon=True,
            ).start()

        thread_queue.put(("create", args, kwargs, future))
        return future

    def close_env(self, env_id: str) -> Future:
        future = Future()
        with self.lock:
            if env_id not in self.env_thread_map:
                future.set_exception(
                    ValueError(f"Environment with id {env_id} does not exist")
                )
                return future

            thread_queue = self.env_thread_map.pop(env_id)

        thread_queue.put(("close", (), {}, future))
        return future

    def _thread_worker(self, task_queue: Queue):
        while True:
            func, args, kwargs, future = task_queue.get()
            try:
                if func == "create":
                    env = SWEEnv(*args, **kwargs)
                    future.set_result(True)
                elif func == "reset_for_new_attempt":
                    env.reset_for_new_attempt()
                    future.set_result(True)
                elif func == "get_container_id":
                    result = env.container_obj.id
                    future.set_result(result)
                elif func == "get_attr":
                    result = getattr(env, args[0], None)
                    future.set_result(result)
                elif func == "reset_container":
                    env.close()
                    env.container = None
                    env.container_obj = None
                    env._reset_container()
                    future.set_result(True)
                elif func == "step":
                    result = env.step(*args, **kwargs)
                    future.set_result(result)
                elif func == "communicate":
                    result = env.communicate(*args, **kwargs)
                    future.set_result(result)
                elif func == "add_commands":
                    env.add_commands(*args, **kwargs)
                    future.set_result(True)
                elif func == "reset":
                    result = env.reset(*args, **kwargs)
                    future.set_result(result)
                elif func == "close":
                    env.close()
                    future.set_result(True)
                    break
                else:
                    result = func(*args, **kwargs)
                    future.set_result(result)
            except Exception as e:
                future.set_exception(e)


_env_counter = 0
_env_counter_lock = Lock()
_env_thread_manager = EnvThreadManager()

SWE_fsm = FastServiceManager()


class EnvironmentInitArguments(BaseModel):
    args: EnvironmentArguments

    class Config:
        arbitrary_types_allowed = True


class EnvironmentContainerResetArguments(BaseModel):
    env_id: str


class EnvironmentStepArguments(BaseModel):
    env_id: str
    input: str


class EnvironmentCommunicateArguments(BaseModel):
    env_id: str
    input: str
    timeout_duration: int | float = 25
    no_output_timeout_duration: int | float | None = None
    set_last_action: bool = False
    redact_command_trace: bool = False


class EnvironmentAddCommandsArguments(BaseModel):
    env_id: str
    commands: list[dict]


class EnvironmentResetArguments(BaseModel):
    env_id: str
    index: int | None = None


class EnvironmentCloseArguments(BaseModel):
    env_id: str


class EnvironmentGetAttrArguments(BaseModel):
    env_id: str
    name: str


class EnvironmentResetForNewAttemptArguments(BaseModel):
    env_id: str


class EnvironmentContainerIDArguments(BaseModel):
    env_id: str


class EnvStepReturn(BaseModel):
    observation: str | None
    reward: int
    done: bool
    info: AgentInfo
    success: bool

    class Config:
        arbitrary_types_allowed = True


class EnvInitReturn(BaseModel):
    env_id: str


class ContainerResetReturn(BaseModel):
    success: bool


class EnvCommunicateReturn(BaseModel):
    output: str
    success: bool


class EnvAddCommandsReturn(BaseModel):
    success: bool


class EnvResetReturn(BaseModel):
    output: tuple[str | None, dict] | None
    success: bool


class EnvCloseReturn(BaseModel):
    success: bool


class EnvGetAttrReturn(BaseModel):
    value: Any


class EnvGetResetForNewAttemptReturn(BaseModel):
    success: bool


class EnvContainerIDReturn(BaseModel):
    container_id: str | None


@SWE_fsm.fast_service
def env_get_container_id(
    args: EnvironmentContainerIDArguments, fsm_context: RequestContext = None
) -> EnvContainerIDReturn:
    container_id = _env_thread_manager.submit_task(
        args.env_id,
        "get_container_id",
    ).result()
    return EnvContainerIDReturn(container_id=container_id)


@SWE_fsm.fast_service
def env_reset_for_new_attempt(
    args: EnvironmentResetForNewAttemptArguments, fsm_context: RequestContext = None
) -> EnvGetResetForNewAttemptReturn:
    success = _env_thread_manager.submit_task(
        args.env_id,
        "reset_for_new_attempt",
    ).result()
    return EnvGetResetForNewAttemptReturn(success=success)


@SWE_fsm.fast_service
def env_get_attr(
    args: EnvironmentGetAttrArguments, fsm_context: RequestContext = None
) -> EnvGetAttrReturn:
    value = _env_thread_manager.submit_task(
        args.env_id,
        "get_attr",
        args.name,
    ).result()
    return EnvGetAttrReturn(value=value)


@SWE_fsm.fast_service
def env_init(
    args: EnvironmentInitArguments, fsm_context: RequestContext = None
) -> EnvInitReturn:
    global _env_counter

    with _env_counter_lock:
        env_id = _env_counter
        _env_counter += 1

    env_id = str(env_id)

    try:
        _env_thread_manager.create_env(
            env_id=env_id,
            args=args.args,
        ).result()
    except Exception as e:
        raise Exception(f"Failed to create environment: {e}")

    return EnvInitReturn(env_id=env_id)


@SWE_fsm.fast_service
def env_reset_container(
    args: EnvironmentContainerResetArguments, fsm_context: RequestContext = None
) -> ContainerResetReturn:
    success = _env_thread_manager.submit_task(
        args.env_id,
        "reset_container",
    ).result()
    return ContainerResetReturn(success=success)


@SWE_fsm.fast_service
def env_step(
    args: EnvironmentStepArguments, fsm_context: RequestContext = None
) -> EnvStepReturn:
    obs, reward, done, info = _env_thread_manager.submit_task(
        args.env_id,
        "step",
        action=args.input,
    ).result()
    return EnvStepReturn(
        observation=obs, reward=reward, done=done, info=info, success=True
    )


@SWE_fsm.fast_service
def env_communicate(
    args: EnvironmentCommunicateArguments, fsm_context: RequestContext = None
) -> EnvCommunicateReturn:
    output = _env_thread_manager.submit_task(
        args.env_id,
        "communicate",
        input=args.input,
        timeout_duration=args.timeout_duration,
        no_output_timeout_duration=args.no_output_timeout_duration,
        set_last_action=args.set_last_action,
        redact_command_trace=args.redact_command_trace,
    ).result()
    return EnvCommunicateReturn(output=output, success=True)


@SWE_fsm.fast_service
def env_add_commands(
    args: EnvironmentAddCommandsArguments, fsm_context: RequestContext = None
) -> EnvAddCommandsReturn:
    success = _env_thread_manager.submit_task(
        args.env_id,
        "add_commands",
        args.commands,
    ).result()
    return EnvAddCommandsReturn(success=success)


@SWE_fsm.fast_service
def env_reset(
    args: EnvironmentResetArguments, fsm_context: RequestContext = None
) -> EnvResetReturn:
    try:
        output = _env_thread_manager.submit_task(
            args.env_id,
            "reset",
            index=args.index,
        ).result()
        return EnvResetReturn(output=output, success=True)
    except Exception as e:
        return EnvResetReturn(output=None, success=False)


@SWE_fsm.fast_service
def env_close(
    args: EnvironmentCloseArguments, fsm_context: RequestContext = None
) -> EnvCloseReturn:
    success = _env_thread_manager.close_env(args.env_id).result()
    return EnvCloseReturn(success=success)
