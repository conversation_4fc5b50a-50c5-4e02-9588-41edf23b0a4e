"""<PERSON>ript to run end-to-end evaluation on the benchmark"""

import argparse
import os
import dataclasses
from typing import <PERSON>ple


from fast_service import (
    FastServiceConfig,
    FastServiceBenchmark,
    FastServiceBenchmarkConfig,
    FastServiceModuleBenchmark,
    FastServiceModuleBenchmarkConfig,
)

from fast_service import FastServiceConfig
from sweagent.request import (
    get_args,
    ScriptWarrper,
    swe_agent_request,
    ScriptArguments,
)
from sweagent.environment.env_manager import SWE_fsm


def parse_args() -> Tuple[argparse.Namespace, ScriptArguments]:
    parser = argparse.ArgumentParser(
        description="Run end-to-end evaluation on the benchmark"
    )

    # benchmark loader
    parser.add_argument("--bm_file_path", default="./.cache/sweagent_data.txt")
    parser.add_argument("--bm_mode", default="one-by-one")
    parser.add_argument("--bm_request_num", default=100, type=int)
    parser.add_argument("--bm_module", type=str, default=None)
    parser.add_argument("--bm_function", type=str, default=None)
    parser.add_argument("--bm_intermediate_storage", type=str, default=None)

    benchmark_args, remaining = parser.parse_known_args()

    return benchmark_args, get_args(remaining)


if __name__ == "__main__":
    benchmark_args, sweagent_args = parse_args()

    def line_to_req(line: str):
        return ScriptWarrper(
            arguments=dataclasses.replace(
                sweagent_args,
                environment=dataclasses.replace(
                    sweagent_args.environment,
                    data_path=line.strip(),
                ),
            )
        )

    def invoke_service(request: ScriptWarrper):
        return swe_agent_request(request).success

    fast_service_config = FastServiceConfig.load_from_file(
        "config/swe_agent-client.yml"
    )
    SWE_fsm.setup_client_mode(fast_service_config)

    bm_config = FastServiceBenchmarkConfig(
        file_path=benchmark_args.bm_file_path,
        mode=benchmark_args.bm_mode,
        request_num=benchmark_args.bm_request_num,
    )

    if benchmark_args.bm_module is not None:
        assert benchmark_args.bm_function is not None
        assert benchmark_args.bm_intermediate_storage is not None
        dir_path = os.path.join(
            benchmark_args.bm_intermediate_storage,
            "requests",
            f"{benchmark_args.bm_module}.{benchmark_args.bm_function}",
        )
        module_config = FastServiceModuleBenchmarkConfig(
            module_name=benchmark_args.bm_module,
            function_name=benchmark_args.bm_function,
            dir_path=dir_path,
            base_config=bm_config,
        )
        benchmark = FastServiceModuleBenchmark(
            config=module_config, fast_service_manager=SWE_fsm
        )
    else:
        benchmark = FastServiceBenchmark(
            config=bm_config, line_to_request=line_to_req, invoke_service=invoke_service
        )

    print("start benchmark")
    benchmark.execute()
    print("end benchmark")
