import json
import argparse
import os
import datasets
from pathlib import Path


def config() -> argparse.Namespace:
    parser = argparse.ArgumentParser(
        description="Prepare data for end-to-end evaluation on the benchmark"
    )

    # task selection
    parser.add_argument(
        "--data_path",
        type=str,
        required=True,
        help="Path to the dataset. HF datasets are supported.",
    )
    parser.add_argument(
        "--split",
        type=str,
        default="dev",
        help="Split of the dataset. Default is 'dev'.",
    )
    parser.add_argument(
        "-d", "--dir", default="./.cache", help="Output directory for prepared data."
    )
    parser.add_argument(
        "-f", "--force", action="store_true", help="Force overwrite the existing data."
    )

    args = parser.parse_args()

    return args


if __name__ == "__main__":
    args = config()

    data_path = args.data_path
    split = args.split

    os.makedirs(args.dir, exist_ok=True)
    file_path = os.path.join(args.dir, "sweagent_data.txt")
    if os.path.exists(file_path) and not args.force:
        print(f"{file_path} already exists. Use -f to force overwrite.")
        exit()

    if Path(data_path).is_dir():
        try:
            dataset = datasets.load_from_disk(data_path)
        except FileNotFoundError:
            # Raised by load_from_disk if the directory is not a dataset directory
            dataset = datasets.load_dataset(data_path)
    else:
        dataset = datasets.load_dataset(data_path)

    if isinstance(dataset, dict):
        try:
            dataset = dataset[split]
        except KeyError:
            # Raised by dataset[split] if split is not a valid key
            print(f"Invalid split: {split}. Valid splits are: {list(dataset.keys())}")
            exit()

    jsons_path = os.path.join(args.dir, "sweagent_data_jsons")
    os.makedirs(jsons_path, exist_ok=True)
    with open(file_path, "w", encoding="utf-8") as file:
        for idx, data in enumerate(dataset):
            json_path = os.path.join(jsons_path, f"{idx}.json")
            with open(json_path, "w", encoding="utf-8") as json_file:
                json.dump([data], json_file)
            file.write(f"{json_path}\n")
