service_impl: fastapi
ip: 0.0.0.0
port: 1701
service_list:
  - module_name: sweagent.environment.env_manager
    name: env_init
    ip: 0.0.0.0
    port: 1701
  - module_name: sweagent.environment.env_manager
    name: env_step
    ip: 0.0.0.0
    port: 1701
  - module_name: sweagent.environment.env_manager
    name: env_reset
    ip: 0.0.0.0
    port: 1701
  - module_name: sweagent.environment.env_manager
    name: env_add_commands
    ip: 0.0.0.0
    port: 1701
  - module_name: sweagent.environment.env_manager
    name: env_communicate
    ip: 0.0.0.0
    port: 1701
  - module_name: sweagent.environment.env_manager
    name: env_close
    ip: 0.0.0.0
    port: 1701
  - module_name: sweagent.environment.env_manager
    name: container_reset
    ip: 0.0.0.0
    port: 1701
  - module_name: sweagent.environment.env_manager
    name: env_get_attr
    ip: 0.0.0.0
    port: 1701
  - module_name: sweagent.environment.env_manager
    name: env_reset_for_new_attempt
    ip: 0.0.0.0
    port: 1701
  - module_name: sweagent.environment.env_manager
    name: env_get_container_id
    ip: 0.0.0.0
    port: 1701

monitor: True
monitor_storage:
  type: csv
  store_dir: ./.cache/swe-agent/SWEEnv
