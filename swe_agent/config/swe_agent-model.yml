service_impl: fastapi
ip: 0.0.0.0
port: 1702
service_list:
  - module_name: sweagent.agent.model_manager
    name: init_model
    ip: 0.0.0.0
    port: 1702
  - module_name: sweagent.agent.model_manager
    name: query
    ip: 0.0.0.0
    port: 1702
  - module_name: sweagent.agent.model_manager
    name: reset_stats
    ip: 0.0.0.0
    port: 1702
  - module_name: sweagent.agent.model_manager
    name: history_to_messages
    ip: 0.0.0.0
    port: 1702
  - module_name: sweagent.agent.model_manager
    name: get_stats
    ip: 0.0.0.0
    port: 1702
  - module_name: sweagent.agent.model_manager
    name: replace_stats
    ip: 0.0.0.0
    port: 1702
  - module_name: sweagent.agent.model_manager
    name: add_stats
    ip: 0.0.0.0
    port: 1702


monitor: True
monitor_storage:
  type: csv
  store_dir: ./.cache/swe-agent/model