service_impl: fastapi
ip: 0.0.0.0
port: 1703
service_list:
  - module_name: sweagent.request
    name: swe_agent_request
    ip: 0.0.0.0
    port: 1703
  - module_name: sweagent.agent.model_manager
    name: init_model
    ip: sweagent-model
    port: 1702
  - module_name: sweagent.agent.model_manager
    name: query
    ip: sweagent-model
    port: 1702
  - module_name: sweagent.agent.model_manager
    name: reset_stats
    ip: sweagent-model
    port: 1702
  - module_name: sweagent.agent.model_manager
    name: history_to_messages
    ip: sweagent-model
    port: 1702
  - module_name: sweagent.agent.model_manager
    name: get_stats
    ip: sweagent-model
    port: 1702
  - module_name: sweagent.agent.model_manager
    name: replace_stats
    ip: sweagent-model
    port: 1702
  - module_name: sweagent.agent.model_manager
    name: add_stats
    ip: sweagent-model
    port: 1702
  - module_name: sweagent.environment.env_manager
    name: env_init
    ip: sweagent-SWEEnv
    port: 1701
  - module_name: sweagent.environment.env_manager
    name: env_step
    ip: sweagent-SWEEnv
    port: 1701
  - module_name: sweagent.environment.env_manager
    name: env_reset
    ip: sweagent-SWEEnv
    port: 1701
  - module_name: sweagent.environment.env_manager
    name: env_add_commands
    ip: sweagent-SWEEnv
    port: 1701
  - module_name: sweagent.environment.env_manager
    name: env_communicate
    ip: sweagent-SWEEnv
    port: 1701
  - module_name: sweagent.environment.env_manager
    name: env_close
    ip: sweagent-SWEEnv
    port: 1701
  - module_name: sweagent.environment.env_manager
    name: container_reset
    ip: sweagent-SWEEnv
    port: 1701
  - module_name: sweagent.environment.env_manager
    name: env_get_attr
    ip: sweagent-SWEEnv
    port: 1701
  - module_name: sweagent.environment.env_manager
    name: env_reset_for_new_attempt
    ip: sweagent-SWEEnv
    port: 1701
  - module_name: sweagent.environment.env_manager
    name: env_get_container_id
    ip: sweagent-SWEEnv
    port: 1701

monitor: True
monitor_storage:
  type: csv
  store_dir: ./.cache/swe-agent/server