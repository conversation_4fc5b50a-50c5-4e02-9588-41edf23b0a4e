[build-system]
requires = ["setuptools>=42", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "sweagent"
version = "0.7.0"
description = "modded sweagent"
authors = [
    { name = "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, SWE-Agent authors", email = "<EMAIL>" }
]
readme = "README.md"
requires-python = ">=3.12"  # Minimum Python version
dynamic = ["dependencies"]

[tool.setuptools.packages.find]
where = ["src"]  # Look for packages in the 'src' directory

[tool.setuptools.dynamic]
dependencies = {file = ["requirements.txt"]}