import hashlib
import datetime
import os
import time
import subprocess
import platform
from subprocess import PIPE, STDOUT
from pathlib import Path
import sys
from typing import Any, Callable

abs_path = os.path.abspath("src/swe_agent/sweagent")
module_dir = os.path.dirname(abs_path)
if module_dir not in sys.path:
    sys.path.append(module_dir)
from sweagent.utils.config import keys_config

DOCKER_START_UP_DELAY = float(keys_config.get("SWE_AGENT_DOCKER_START_UP_DELAY", 1))


def read_with_timeout(
    container: subprocess.Popen, pid_func: Callable, timeout_duration: int | float
) -> str:
    """
    Read data from a subprocess with a timeout.
    This function uses a file descriptor to read data from the subprocess in a non-blocking way.

    Args:
        container: The subprocess container.
        pid_func: A function that returns a list of process IDs (except the PID of the main process).
        timeout_duration: The timeout duration in seconds.

    Returns:
        output: The data read from the subprocess, stripped of trailing newline characters.

    Raises:
        TimeoutError: If the timeout duration is reached while reading from the subprocess.
    """
    buffer = b""
    fd = container.stdout.fileno()
    end_time = time.time() + timeout_duration

    # Select is not available on windows
    is_windows = platform.system() == "Windows"
    if not is_windows:
        import select
    else:
        os.set_blocking(fd, False)

    def ready_to_read(fd) -> bool:
        if is_windows:
            # We can't do the extra check
            return True
        return bool(select.select([fd], [], [], 0.01)[0])

    while time.time() < end_time:
        pids = pid_func()
        if len(pids) > 0:
            # There are still PIDs running
            time.sleep(0.05)
            continue
        if ready_to_read(fd):
            data = os.read(fd, 4096)
            if data:
                buffer += data
        else:
            # No more data to read
            break
        time.sleep(0.05)  # Prevents CPU hogging

    if container.poll() is not None:
        msg = f"Subprocess exited unexpectedly.\nCurrent buffer: {buffer.decode()}"
        raise RuntimeError(msg)
    if time.time() >= end_time:
        msg = f"Timeout reached while reading from subprocess.\nCurrent buffer: {buffer.decode()}\nRunning PIDs: {pids}"
        raise TimeoutError(msg)

    decoded = buffer.decode("utf-8", errors="backslashreplace").replace("\r\n", "\n")
    return "\n".join(line for line in decoded.splitlines())


def get_container_name(image_name: str, provided_name: str | None = None) -> str:
    """
    Generate a unique container name if none is provided, otherwise use the provided name.

    Args:
        image_name: The Docker image name.
        provided_name: Optional container name provided by the user.

    Returns:
        A container name.
    """
    if provided_name:
        return provided_name

    process_id = str(os.getpid())
    current_time = str(datetime.datetime.now())
    unique_string = current_time + process_id
    hash_object = hashlib.sha256(unique_string.encode())
    image_name_sanitized = image_name.replace("/", "-").replace(":", "-")
    return f"{image_name_sanitized}-{hash_object.hexdigest()[:10]}"


def create_container(
    ctr_name: str, image_name: str, container_mounts: list[str]
) -> subprocess.Popen:
    startup_cmd = [
        "docker",
        "run",
        "-i",
        *[
            item
            for mount in container_mounts
            for item in ("-v", f"{Path(mount).absolute()}:/{Path(mount).name}")
        ],
        "--name",
        ctr_name,
        image_name,
        "/bin/bash",
        "-c",
        "while true; do sleep 1; done",
    ]
    container = subprocess.Popen(
        startup_cmd,
        stdin=PIPE,
        stdout=PIPE,
        stderr=STDOUT,
        text=True,
        bufsize=1,  # line buffered
    )
    time.sleep(DOCKER_START_UP_DELAY)
    # try to read output from container setup (usually an error), timeout if no output
    output = read_with_timeout(container, lambda: list(), timeout_duration=2)
    return container


# Usage example
if __name__ == "__main__":
    import argparse

    # CLI to accept a container name
    parser = argparse.ArgumentParser(
        description="Create a Docker container with an optional name."
    )
    parser.add_argument(
        "--container_name",
        type=str,
        default=None,
        help="Optional name for the container. If not provided, a unique name will be generated.",
    )
    parser.add_argument(
        "--image_name",
        type=str,
        default="sweagent/swe-agent:latest",
        help="Docker image name to use.",
    )
    args = parser.parse_args()

    try:
        container_name = get_container_name(args.image_name, args.container_name)
        container = create_container(
            ctr_name=container_name,
            image_name=args.image_name,
            container_mounts=[],
        )
        print(f"Container created with name: {container_name} and PID: {container.pid}")
    except Exception as e:
        print(f"Error: {e}")
