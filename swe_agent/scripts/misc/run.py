from fast_service import FastServiceConfig
from sweagent.request import get_args, ScriptWarrper, swe_agent_request
from sweagent.environment.env_manager import SWE_fsm

if __name__ == "__main__":
    fast_service_config = FastServiceConfig.load_from_file(
        "config/swe_agent-client.yml"
    )
    SWE_fsm.setup_client_mode(fast_service_config)

    swe_agent_request(ScriptWarrper(arguments=get_args()))
