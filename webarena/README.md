# WebArena

This is a modified and dockerized version of WebArena.

## Usage

0. Set environment variable `WEBSITE_HOST` to the IP address of the host
```bash
export WEBSITE_HOST=********** # Change this IP as needed
```

1. Build the docker image that will be used later:
```bash
bash scripts/1.docker_build.sh
```

2. Follow the instructions in [this page](https://github.com/web-arena-x/webarena/blob/e31c190c9b43f63e5724322b847e00249300df40/environment_docker/README.md) to setup website environments. For example, to setup the CMS (shopping_admin) website:
```bash
# Deploy at least one website as instructed in https://github.com/web-arena-x/webarena/blob/e31c190c9b43f63e5724322b847e00249300df40/environment_docker/README.md . Here we use shopping_admin for example:
wget http://metis.lti.cs.cmu.edu/webarena-images/shopping_admin_final_0719.tar
docker load --input shopping_admin_final_0719.tar
docker run --name shopping_admin -p 7780:80 -d shopping_admin_final_0719
# wait ~1 min to wait all services to start

docker exec shopping_admin /var/www/magento2/bin/magento setup:store-config:set --base-url="http://${WEBSITE_HOST}:7780"
docker exec shopping_admin mysql -u magentouser -pMyPassword magentodb -e "UPDATE core_config_data SET value=\"http://${WEBSITE_HOST}:7780/\" WHERE path = \"web/secure/base_url\";"
docker exec shopping_admin /var/www/magento2/bin/magento cache:flush
```

3. Prepare the dataset:
```bash
bash scripts/3.prepare_datasets.sh # This will download datasets and generate task files for testing
```

4. Login to all websites in advance:
```bash
bash scripts/4.auto_login.sh # This will perform auto-login on websites. Unavailable websites will be skipped.
```

5. Prepare the benchmark tasks:
```bash
# set to benchmark all tasks
bash scripts/5.prepare_benchmark.sh -f

# set to benchmark tasks only on shopping_admin website
bash scripts/5.prepare_benchmark.sh -f \
    --task_websites shopping_admin

# set to benchmark tasks only on shopping_admin website with task_id within [0, 3) (left-closed and right-open)
bash scripts/5.prepare_benchmark.sh -f \
    --task_websites shopping_admin \
    --task_start_idx 0 \
    --task_end_idx 3
```

6. Start the benchmark. Pass LLM related variables here:
```bash
# For OpenAI-compatible service
OPENAI_API_KEY="<your_api_key>" OPENAI_BASE_URL="<your_base_url>" LLM_MODEL="<model_name>" bash scripts/6.run_benchmark.sh

# For Azure OpenAI Endpoint
OPENAI_API_TYPE="azure" OPENAI_API_KEY="<your_api_key>" AZURE_OPENAI_ENDPOINT="<your_azure_endpoint>" LLM_MODEL="<model_name>" bash scripts/6.run_benchmark.sh
```
Use `docker logs -f <container_name>` to follow logs. The container `webarena-loadgen` will exit once the benchmark is finished. You can repeatedly run the above command more than one time to reproduce the results, and use the script in step 5 to regenerate the benchmark before each running.

7. Cleanup all docker containers and the docker network after benchmarking:
```bash
bash scripts/7.cleanup.sh
```
You may ignore the WARN about variables not set. They are in fact not needed during the shutting-down process.

## What's More

For further information, please refer to the [original WebArena repo](https://github.com/web-arena-x/webarena/tree/e31c190c9b43f63e5724322b847e00249300df40).
