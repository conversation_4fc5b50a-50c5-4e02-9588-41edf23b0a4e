service_impl: fastapi
ip: 0.0.0.0
port: 1703
service_list:
  - module_name: webarena.fast_service_api.execution_coordinator
    name: e2e_execution_with_evaluation
    ip: 0.0.0.0
    port: 1703
  - module_name: webarena.fast_service_api.execution_coordinator
    name: e2e_execution_no_evaluation
    ip: 0.0.0.0
    port: 1703
  - module_name: webarena.fast_service_api.env_manager
    name: env_init
    ip: webarena-env-manager
    port: 1701
  - module_name: webarena.fast_service_api.env_manager
    name: env_step
    ip: webarena-env-manager
    port: 1701
  - module_name: webarena.fast_service_api.env_manager
    name: env_reset
    ip: webarena-env-manager
    port: 1701
  - module_name: webarena.fast_service_api.env_manager
    name: env_evaluate
    ip: webarena-env-manager
    port: 1701
  - module_name: webarena.fast_service_api.env_manager
    name: env_save_trace
    ip: webarena-env-manager
    port: 1701
  - module_name: webarena.fast_service_api.env_manager
    name: env_destroy
    ip: webarena-env-manager
    port: 1701
  - module_name: webarena.fast_service_api.llm
    name: call_llm
    ip: webarena-llm
    port: 1702


monitor: True
monitor_storage:
  type: csv
  store_dir: ./.cache/webarena/execution_coordinator