service_impl: fastapi
ip: 0.0.0.0
port: 1701
service_list:
  - module_name: webarena.fast_service_api.env_manager
    name: env_init
    ip: 0.0.0.0
    port: 1701
  - module_name: webarena.fast_service_api.env_manager
    name: env_step
    ip: 0.0.0.0
    port: 1701
  - module_name: webarena.fast_service_api.env_manager
    name: env_reset
    ip: 0.0.0.0
    port: 1701
  - module_name: webarena.fast_service_api.env_manager
    name: env_evaluate
    ip: 0.0.0.0
    port: 1701
  - module_name: webarena.fast_service_api.env_manager
    name: env_save_trace
    ip: 0.0.0.0
    port: 1701
  - module_name: webarena.fast_service_api.env_manager
    name: env_destroy
    ip: 0.0.0.0
    port: 1701

monitor: True
monitor_storage:
  type: csv
  store_dir: ./.cache/webarena/env_manager