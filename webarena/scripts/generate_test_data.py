"""Replace the website placeholders with website domains from env_config
Generate the test data"""

import json
import itertools

from webarena.browser_env.env_config import *


def main() -> None:
    with open("datasets/test.raw.json", "r") as f:
        raw = f.read()
    raw = raw.replace("__GITLAB__", GITLAB)
    raw = raw.replace("__REDDIT__", REDDIT)
    raw = raw.replace("__SHOPPING__", SHOPPING)
    raw = raw.replace("__SHOPPING_ADMIN__", SHOPPING_ADMIN)
    raw = raw.replace("__WIKIPEDIA__", WIKIPEDIA)
    raw = raw.replace("__MAP__", MAP)
    with open("datasets/test.json", "w") as f:
        f.write(raw)
    # split to multiple files
    data = json.loads(raw)

    for idx, item in enumerate(data):
        with open(f"datasets/{idx}.json", "w") as f:
            json.dump(item, f, indent=2)

    for websites, tasks in itertools.groupby(
        sorted(data, key=lambda item: tuple(sorted(item["sites"]))),
        key=lambda item: tuple(sorted(frozenset(item["sites"]))),
    ):
        tasks = list(tasks)

        with open(f"datasets/{'_'.join(websites)}.json", "w") as f:
            json.dump(tasks, f, indent=2)

        for task in tasks:
            with open(
                f"datasets/{'_'.join(websites)}.{task['task_id']}.json", "w"
            ) as f:
                json.dump(task, f, indent=2)


if __name__ == "__main__":
    main()
