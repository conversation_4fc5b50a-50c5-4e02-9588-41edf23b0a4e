#!/bin/env sh

WEBSITE_HOST="${WEBSITE_HOST:-127.0.0.1}"
export SHOPPING="http://${WEBSITE_HOST}:7770"
export SHOPPING_ADMIN="http://${WEBSITE_HOST}:7780/admin"
export REDDIT="http://${WEBSITE_HOST}:9999"
export GITLAB="http://${WEBSITE_HOST}:8023"
export MAP="http://${WEBSITE_HOST}:3000"
export WIKIPEDIA="http://${WEBSITE_HOST}:8888/wikipedia_en_all_maxi_2022-05/A/User:The_other_Kiwix_guy/Landing"
export HOMEPAGE="http://${WEBSITE_HOST}:4399"

docker run --rm -it \
	-e SHOPPING -e SHOPPING_ADMIN -e REDDIT -e GITLAB -e MAP -e WIKIPEDIA -e HOMEPAGE \
	-v $(pwd)/datasets:/workspace/webarena/datasets \
	-v $(pwd)/.cache:/workspace/webarena/.cache \
	-v $(pwd)/results:/workspace/webarena/results \
	-v $(pwd)/benchmark:/workspace/webarena/benchmark \
	webarena:v1 python benchmark/prepare_data.py "$@"
