#!/bin/env bash

WEBSITE_HOST="${WEBSITE_HOST:-127.0.0.1}"
export SHOPPING="http://${WEBSITE_HOST}:7770"
export SHOPPING_ADMIN="http://${WEBSITE_HOST}:7780/admin"
export REDDIT="http://${WEBSITE_HOST}:9999"
export GITLAB="http://${WEBSITE_HOST}:8023"
export MAP="http://${WEBSITE_HOST}:3000"
export WIKIPEDIA="http://${WEBSITE_HOST}:8888/wikipedia_en_all_maxi_2022-05/A/User:The_other_Kiwix_guy/Landing"
export HOMEPAGE="http://${WEBSITE_HOST}:4399"

# Download the raw task file (datasets)
mkdir -p ./datasets
DATASETS_PATH="datasets/test.raw.json"
DATASETS_URL="https://raw.githubusercontent.com/web-arena-x/webarena/e31c190c9b43f63e5724322b847e00249300df40/config_files/test.raw.json"
if [ -e "$DATASETS_PATH" ]; then
	echo "Raw dataset already exists, skipping"
else
	wget -O "$DATASETS_PATH" "$DATASETS_URL"
fi

# Generate task files
docker run --rm -it \
	-e SHOPPING -e SHOPPING_ADMIN -e REDDIT -e GITLAB -e MAP -e WIKIPEDIA -e HOMEPAGE \
	-v $(pwd)/datasets:/workspace/webarena/datasets \
	-v $(pwd)/scripts/generate_test_data.py:/workspace/webarena/scripts/generate_test_data.py \
	webarena:v1 \
	python scripts/generate_test_data.py
