#!/bin/env bash

WEBSITE_HOST="${WEBSITE_HOST:-127.0.0.1}"
export SHOPPING="http://${WEBSITE_HOST}:7770"
export SHOPPING_ADMIN="http://${WEBSITE_HOST}:7780/admin"
export REDDIT="http://${WEBSITE_HOST}:9999"
export GITLAB="http://${WEBSITE_HOST}:8023"
export MAP="http://${WEBSITE_HOST}:3000"
export WIKIPEDIA="http://${WEBSITE_HOST}:8888/wikipedia_en_all_maxi_2022-05/A/User:The_other_Kiwix_guy/Landing"
export HOMEPAGE="http://${WEBSITE_HOST}:4399"

# Auto-login
mkdir -p ./.auth
docker run --rm -it \
	-e SHOPPING -e SHOPPING_ADMIN -e REDDIT -e GITLAB -e MAP -e WIKIPEDIA -e HOMEPAGE \
	-v $(pwd)/.auth:/workspace/webarena/.auth \
	webarena:v1 \
	python scripts/auto_login.py
