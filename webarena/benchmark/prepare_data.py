"""Script to run end-to-end evaluation on the benchmark"""

import argparse
import glob
import os
import re


def config() -> argparse.Namespace:
    parser = argparse.ArgumentParser(
        description="Run end-to-end evaluation on the benchmark"
    )

    # task selection
    parser.add_argument("--task_start_idx", type=int, default=0)
    parser.add_argument("--task_end_idx", type=int, default=1000)
    parser.add_argument("--task_websites", type=str, default="ALL")
    parser.add_argument(
        "-d", "--dir", default="./.cache", help="Output directory for prepared data."
    )
    parser.add_argument(
        "-f", "--force", action="store_true", help="Force overwrite the existing data."
    )

    # used to skip finished tasks
    parser.add_argument("--result_dir", type=str, default="results")

    args = parser.parse_args()

    return args


def get_unfinished(task_files: list[str], result_dir: str) -> list[str]:
    result_files = glob.glob(f"{result_dir}/*.html")
    task_ids = [os.path.basename(f).split(".")[0].split("_")[1] for f in result_files]
    unfinished_tasks = []
    for task_file in task_files:
        task_id = os.path.basename(task_file).split(".")[-2]
        if task_id not in task_ids:
            unfinished_tasks.append(task_file)
    return unfinished_tasks


if __name__ == "__main__":
    args = config()

    task_file_list = []

    # The task files used should follow the pattern: datasets/{task_websites}.{idx}.json if task_websites != "ALL"
    # otherwise, the task files should follow the pattern: datasets/{idx}.json.
    # Some idxs might be missing, so we need to use the pattern to get the task files, and then check if the idx falls in the range.
    # The start idx is inclusive, the end idx is exclusive, and the range applies to both specified websites and "ALL".
    if args.task_websites == "ALL":
        path_pattern = re.compile(r"datasets/(\d+)\.json")
    else:
        path_pattern = re.compile(rf"datasets/{args.task_websites}\.(\d+)\.json")
    task_file_list = list(
        filter(re.compile(path_pattern).match, glob.glob("datasets/*.json"))
    )

    # Use regex to extract the idx from the file name, and sort the task_file_list by the idx
    path_pattern = re.compile(r"datasets/((?:\w|_)+\.)?(\d+)\.json")
    task_file_list = sorted(
        filter(
            lambda x: args.task_start_idx
            <= int(re.match(path_pattern, x).group(2))
            < args.task_end_idx,
            task_file_list,
        ),
        key=lambda x: int(re.match(path_pattern, x).group(2)),
    )

    # Skip finished tasks
    if "debug" not in args.result_dir:
        task_file_list = get_unfinished(task_file_list, args.result_dir)

    os.makedirs(args.dir, exist_ok=True)
    file_path = os.path.join(args.dir, "webarena_data.txt")
    if args.force or not os.path.exists(file_path):
        if len(task_file_list) == 0:
            print("No task left to run")
        else:
            print(f"Total {len(task_file_list)} tasks left")
        with open(file_path, "w", encoding="utf-8") as file:
            for task_file in task_file_list:
                file.write(f"{task_file}\n")
    else:
        print(f"{file_path} already exists.")
