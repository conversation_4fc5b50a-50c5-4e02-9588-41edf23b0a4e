from dataclasses import dataclass
from io import BytesIO
from typing import Any, Dict, TypedDict, Union

import numpy as np
import numpy.typing as npt
from fast_service import FastServiceManager
from PIL import Image
from pydantic import BaseModel

WEBARENA_FSM = FastServiceManager()


class DetachedPage(BaseModel):
    url: str
    content: str  # html

    def __init__(self, *args, **kwargs):
        if len(args) == 2:
            kwargs["url"] = args[0]
            kwargs["content"] = args[1]
        super().__init__(**kwargs)


def png_bytes_to_numpy(png: bytes) -> npt.NDArray[np.uint8]:
    """Convert png bytes to numpy array

    Example:

    >>> fig = go.Figure(go.Scatter(x=[1], y=[1]))
    >>> plt.imshow(png_bytes_to_numpy(fig.to_image('png')))
    """
    return np.array(Image.open(BytesIO(png)))


class AccessibilityTreeNode(TypedDict):
    nodeId: str
    ignored: bool
    role: dict[str, Any]
    chromeRole: dict[str, Any]
    name: dict[str, Any]
    properties: list[dict[str, Any]]
    childIds: list[str]
    parentId: str
    backendDOMNodeId: str
    frameId: str
    bound: list[float] | None
    union_bound: list[float] | None
    offsetrect_bound: list[float] | None


class DOMNode(TypedDict):
    nodeId: str
    nodeType: str
    nodeName: str
    nodeValue: str
    attributes: str
    backendNodeId: str
    parentId: str
    childIds: list[str]
    cursor: int
    union_bound: list[float] | None


class BrowserConfig(TypedDict):
    win_top_bound: float
    win_left_bound: float
    win_width: float
    win_height: float
    win_right_bound: float
    win_lower_bound: float
    device_pixel_ratio: float


class BrowserInfo(TypedDict):
    DOMTree: dict[str, Any]
    config: BrowserConfig


AccessibilityTree = list[AccessibilityTreeNode]
DOMTree = list[DOMNode]


Observation = str | npt.NDArray[np.uint8]
ObservationModel = str | list


class ObservationMetadata(TypedDict):
    obs_nodes_info: dict[str, Any]


class EnvInfo(BaseModel):
    page: DetachedPage
    fail_error: str
    observation_metadata: dict[str, ObservationMetadata]


class StateInfo(BaseModel):
    observation: dict[str, Observation]
    info: EnvInfo

    class Config:
        arbitrary_types_allowed = True


class StateInfoModel(BaseModel):
    observation: dict[str, ObservationModel]
    info: EnvInfo


def observation_from_model(model: ObservationModel) -> Observation:
    if isinstance(model, str):
        return model
    return np.array(model, dtype=np.uint8)


def observation_to_model(observation: Observation) -> ObservationModel:
    if isinstance(observation, str):
        return observation
    return observation.tolist()


def state_info_from_model(model: StateInfoModel) -> StateInfo:
    return StateInfo(
        observation={
            k: observation_from_model(v) for k, v in model.observation.items()
        },
        info=model.info,
    )


def state_info_to_model(state_info: StateInfo) -> StateInfoModel:
    return StateInfoModel(
        observation={
            k: observation_to_model(v) for k, v in state_info.observation.items()
        },
        info=state_info.info,
    )
