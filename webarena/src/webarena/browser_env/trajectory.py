from typing import Union

from .actions import Action, ActionModel, action_from_model, action_to_model
from .utils import StateInfo, StateInfoModel, state_info_from_model, state_info_to_model

Trajectory = list[Union[StateInfo, Action]]
TrajectoryModel = list[Union[ActionModel, StateInfoModel]]


def trajectory_from_model(trajectory: TrajectoryModel) -> Trajectory:
    return [
        (
            action_from_model(item)
            if isinstance(item, ActionModel)
            else state_info_from_model(item)
        )
        for item in trajectory
    ]


def trajectory_to_model(trajectory: Trajectory) -> TrajectoryModel:
    return [
        (
            state_info_to_model(item)
            if isinstance(item, StateInfo)
            else action_to_model(item)
        )
        for item in trajectory
    ]
