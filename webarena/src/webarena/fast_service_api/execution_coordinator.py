import webarena.execution_coordinator as ec
from fast_service import RequestContext
from ..fast_service_manager import WEBARENA_FSM


@WEBARENA_FSM.fast_service
def e2e_execution_with_evaluation(
    args: ec.E2eExecutionArgs,
    fsm_context: RequestContext = None,
) -> ec.E2eExecutionRetVal:
    ret = ec.e2e_execution(args, with_evaluation=True, fsm_context=fsm_context)
    print(f"{args.config_file} executed and evaluated. score == {ret.score}")
    return ret


@WEBARENA_FSM.fast_service
def e2e_execution_no_evaluation(
    args: ec.E2eExecutionArgs,
    fsm_context: RequestContext = None,
) -> ec.E2eExecutionRetVal:
    ret = ec.e2e_execution(args, with_evaluation=False, fsm_context=fsm_context)
    print(f"{args.config_file} executed.")
    return ret
