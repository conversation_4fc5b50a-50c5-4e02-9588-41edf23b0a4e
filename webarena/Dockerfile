FROM python:3.12
LABEL authors="fty1777"

ARG WEBSITE_HOST=127.0.0.1
# set url for websites
ENV SHOPPING="http://${WEBSITE_HOST}:7770"
ENV SHOPPING_ADMIN="http://${WEBSITE_HOST}:7780/admin"
ENV REDDIT="http://${WEBSITE_HOST}:9999"
ENV GITLAB="http://${WEBSITE_HOST}:8023"
ENV MAP="http://${WEBSITE_HOST}:3000"
ENV WIKIPEDIA="http://${WEBSITE_HOST}:8888/wikipedia_en_all_maxi_2022-05/A/User:The_other_Kiwix_guy/Landing"
ENV HOMEPAGE="http://${WEBSITE_HOST}:4399"

# download and install fast-service
COPY .cache/fast-service /workspace/fast-service
RUN cd /workspace/fast-service && \
    pip install -r requirements.txt && \
    pip install -e .

# install dependencies for webarena
WORKDIR /workspace/webarena
COPY requirements.txt requirements.txt


RUN pip install -r requirements.txt
RUN playwright install-deps
RUN playwright install
RUN apt install -y curl

RUN curl https://codeload.github.com/web-arena-x/webarena/tar.gz/e31c190c9b43f63e5724322b847e00249300df40 | tar -xz --strip=1 webarena-e31c190c9b43f63e5724322b847e00249300df40/environment_docker && perl -pi -e "s|<your-server-hostname>|http://${WEBSITE_HOST}|g" environment_docker/webarena-homepage/templates/index.html

# copy current code to docker image
COPY src src
COPY scripts/auto_login.py scripts/auto_login.py
COPY setup.py setup.py
COPY setup.cfg setup.cfg

RUN pip install -e .

# set workdir and default command
WORKDIR /workspace/webarena
