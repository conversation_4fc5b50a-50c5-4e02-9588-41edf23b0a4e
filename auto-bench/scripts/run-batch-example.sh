#!/bin/env bash

SCRIPT_DIR=$(dirname "$(realpath "$0")")
PARENT_DIR=$(dirname "$SCRIPT_DIR")

EXAMPLE_DIR=${PARENT_DIR}/example
YML_PATH=${EXAMPLE_DIR}/batch-job.yml
ACTIVE_PATH=${EXAMPLE_DIR}/batch-job_active.yml

cp ${YML_PATH} ${ACTIVE_PATH}

export BATCH_CONFIG_DIR=${EXAMPLE_DIR}
sed -i "s|\${BATCH_CONFIG_DIR}|${BATCH_CONFIG_DIR}|g" ${ACTIVE_PATH}

AUTO_BENCH=${SCRIPT_DIR}/auto-bench.sh
bash ${AUTO_BENCH} run-batch ${ACTIVE_PATH}