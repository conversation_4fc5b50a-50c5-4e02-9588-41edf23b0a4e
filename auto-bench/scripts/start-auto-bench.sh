#!/bin/env bash

SCRIPT_DIR=$(dirname "$(realpath "$0")")
PARENT_DIR=$(dirname "$SCRIPT_DIR")

export AUTO_BENCH_DATA_DIR=${PARENT_DIR}/data
#export AUTO_BENCH_DATA_DIR=YOUR_DIR

mkdir -p ${AUTO_BENCH_DATA_DIR}

docker run \
  -d \
  --name auto-bench-tool \
  -e AUTO_BENCH_WORKING_DIR=${PARENT_DIR} \
  -v ${PARENT_DIR}:/workspace/auto-bench/ \
  -v ${AUTO_BENCH_DATA_DIR}:/workspace/auto-bench/data/ \
  auto-bench:v1
