client_mode: True
service_impl: fastapi
service_list:
  - module_name: auto_bench.protocol.service
    name: check_server
    ip: 127.0.0.1
    port: 8000
  - module_name: auto_bench.protocol.service
    name: run
    ip: 127.0.0.1
    port: 8000
  - module_name: auto_bench.protocol.service
    name: stop
    ip: 127.0.0.1
    port: 8000
  - module_name: auto_bench.protocol.service
    name: remove
    ip: 127.0.0.1
    port: 8000
  - module_name: auto_bench.protocol.service
    name: run_batch
    ip: 127.0.0.1
    port: 8000
  - module_name: auto_bench.protocol.service
    name: stop_batch
    ip: 127.0.0.1
    port: 8000
  - module_name: auto_bench.protocol.service
    name: remove_batch
    ip: 127.0.0.1
    port: 8000


