client_mode: False
service_impl: fastapi
ip: 0.0.0.0
port: 8000
service_list:
  - module_name: auto_bench.protocol.service
    name: check_server
    ip: 0.0.0.0
    port: 8000
  - module_name: auto_bench.protocol.service
    name: run
    ip: 0.0.0.0
    port: 8000
  - module_name: auto_bench.protocol.service
    name: stop
    ip: 0.0.0.0
    port: 8000
  - module_name: auto_bench.protocol.service
    name: remove
    ip: 0.0.0.0
    port: 8000
  - module_name: auto_bench.protocol.service
    name: run_batch
    ip: 0.0.0.0
    port: 8000
  - module_name: auto_bench.protocol.service
    name: stop_batch
    ip: 0.0.0.0
    port: 8000
  - module_name: auto_bench.protocol.service
    name: remove_batch
    ip: 0.0.0.0
    port: 8000
