import os
import time
import pytimeparse
import shutil

from threading import Event
from typing import Any

from ..utils import yaml_utils
from ..config import (
    DeploymentTemplate,
    ServiceTemplate,
    JobVariable,
    ServiceVariable,
    VariableLocation,
)
from ..command import ICommandExecutor
from ..deployment import (
    DeploymentFactory,
    IDeployment,
    DeploymentExecutor,
    ServiceStatus,
)
from ..file import MappedFile, MappedFileManager

from .job import AutoBenchJob


class JobExecutor:

    job: AutoBenchJob
    backend: str
    command_executor: ICommandExecutor
    mapped_file_manager: MappedFileManager

    deployment_executor: DeploymentExecutor

    _start_time: float

    stop_event: Event

    def __init__(
        self,
        job: AutoBenchJob,
        backend: str,
        command_executor: ICommandExecutor,
        mapped_file_manager: MappedFileManager,
    ):
        self.job = job
        self.backend = backend
        self.command_executor = command_executor
        self.mapped_file_manager = mapped_file_manager

        self._start_time = 0

        self.stop_event = Event()
        self.stop_event.set()

    def start(self) -> Event:
        job_dir = self.job.job_dir
        deployment_template = self.job.deployment_template
        job_variable = self.job.job_variable

        # create job working dir
        if not os.path.exists(job_dir):
            os.mkdir(job_dir)

        # update customized variables in config files before deployment
        self._rewrite_variables()

        # execute deployment
        deployment: IDeployment = DeploymentFactory.create_deployment(
            self.backend,
            job_dir,
            deployment_template,
            job_variable,
            self.command_executor,
            self.mapped_file_manager,
        )
        self.deployment_executor = DeploymentExecutor(deployment)

        self.deployment_executor.deploy()
        self._start_time = time.time()

        self.stop_event.clear()
        return self.stop_event

    def stop(self):
        self.deployment_executor.terminate()
        self.stop_event.set()

    def clear(self):
        self.deployment_executor.clear()
        shutil.rmtree(self.job.job_dir)
        self._start_time = 0

    def get_start_time(self):
        return self._start_time

    def is_running(self) -> bool:
        return not self.stop_event.is_set()

    def need_stop(self) -> bool:
        if not self.is_running():
            return False

        service_condition_dict: dict[str, str] = self.job.job_variable.ends_on

        if not service_condition_dict:
            # we don't stop those without ends_on conditions
            return False

        service_list = list(service_condition_dict.keys())
        status_list = self.deployment_executor.get_service_status(service_list)
        for i in range(len(service_list)):
            name: str = service_list[i]
            status: ServiceStatus = status_list[i]
            condition: str = service_condition_dict[name].lower()
            if condition == "exit":
                if status.running:
                    return False
            else:
                time_limit = pytimeparse.parse(condition)
                if status.running and time_limit > status.duration:
                    return False
        return True

    def _rewrite_variables(self):
        template: DeploymentTemplate = self.job.deployment_template
        variables: JobVariable = self.job.job_variable

        all_mapped_files = []

        try:
            # for each service, update the variable values to config files
            for service_name, variable_value_dict in variables.services.items():
                variable_value_dict: ServiceVariable = variable_value_dict

                # host_file_path -> variable_name -> flatten_key
                file_dict: dict[str, dict[str, list[str]]] = {}
                host_path_list = []
                file_idx_dict: dict[str, int] = {}

                file_variable_dict: dict[str, Any] = {}

                # extract the relation between files and variables
                for v_name in variable_value_dict.variables:
                    service_template: ServiceTemplate = template.services[service_name]
                    v_definition = service_template.variables[v_name]

                    if isinstance(v_definition, str):
                        # update environment variables
                        service_template.environment[v_definition] = (
                            variable_value_dict.variables[v_name]
                        )
                        continue

                    file_variable_dict[v_name] = variable_value_dict.variables[v_name]

                    location: VariableLocation = service_template.variables[v_name]
                    file_path = os.path.abspath(location.file_path)
                    if file_path not in file_dict:
                        file_dict[file_path] = {}
                        host_path_list.append(file_path)
                        file_idx_dict[file_path] = len(host_path_list) - 1

                    file_dict[file_path][v_name] = location.flatten_key

                # pull config files from host
                mapped_file_list: list[MappedFile] = (
                    self.mapped_file_manager.construct_file_map(host_path_list)
                )
                all_mapped_files.extend(mapped_file_list)

                # update the config files locally
                for file_path, location_dict in file_dict.items():
                    yaml_utils.rewrite_params(
                        mapped_file_list[file_idx_dict[file_path]].working_path,
                        location_dict,
                        file_variable_dict,
                    )

                # push the config files to host
                self.mapped_file_manager.commit_files(mapped_file_list)
        finally:
            self.mapped_file_manager.clear_file_map(all_mapped_files)
