import os
import time
import traceback
import shutil

from threading import Thread, Event, Lock

from ..config import DeploymentTemplate, JobVariable, BatchJobConfig, SingleJobConfig
from ..file import MappedFileManager, MappedFile
from ..utils import yaml_utils

from .manager import JobManager


class BatchJob:

    batch_config: BatchJobConfig

    index: int
    job_list: list[(DeploymentTemplate, JobVariable)]

    def __init__(
        self,
        batch_config: BatchJobConfig,
        job_list: list[(DeploymentTemplate, JobVariable)],
    ):
        self.batch_config: BatchJobConfig = batch_config
        self.index: int = self.batch_config.index
        self.job_list = job_list

    def inc_index(self):
        self.index += 1

    def is_finished(self):
        return self.index >= len(self.job_list)

    def get_next_job(self) -> (DeploymentTemplate, JobVariable):
        return self.job_list[self.index]


class BatchJobExecutor:

    batch: Batch<PERSON>ob

    working_dir: str

    worker: Thread
    # stop event is used for worker status indicating
    stop_event: Event
    start_time: float
    # stop signal is used for user stopping batch job
    stop_signal: Event

    job_manager: JobManager

    def __init__(
        self,
        batch: BatchJob,
        working_dir: str,
        job_manager: JobManager,
    ):
        self.batch: BatchJob = batch

        self.working_dir: str = working_dir

        self.stop_event: Event = Event()
        self.stop_event.set()
        self.stop_signal = Event()
        self.start_time: float = 0
        self.worker: Thread = Thread(target=self._execute)

        self.job_manager: JobManager = job_manager

    def start(self) -> Event:
        if self.is_running():
            raise RuntimeError(
                f"Batch job {self.batch.batch_config.name} is already running"
            )

        self.stop_event.clear()
        self.worker.start()
        self.start_time = time.time()
        return self.stop_event

    def _execute(self):
        log_path = os.path.join(self.working_dir, "progress.log")
        try:
            with open(log_path, "a") as log:
                while not self.stop_signal.is_set():
                    if self.batch.is_finished():
                        break

                    deployment_template, job_variable = self.batch.get_next_job()
                    job_stop_event: Event = self.job_manager.run_job(
                        deployment_template, job_variable
                    )
                    while not job_stop_event.wait(5):
                        if self.stop_signal.is_set():
                            self.job_manager.stop_job(job_variable.name())
                            return

                    log.write(f"{time.time()},{self.batch.index}\n")
                    log.flush()
                    os.fsync(log.fileno())

                    self.batch.inc_index()
        except Exception as e:
            traceback.print_exc()
            print(e, flush=True)
        finally:
            self.stop_event.set()
            log.close()

    def stop(self):
        self.stop_signal.set()
        self.worker.join()

    def is_running(self):
        return not self.stop_event.is_set()

    def clear(self):
        i = 0
        while i <= self.batch.index and i < len(self.batch.job_list):
            job_variable: JobVariable = self.batch.job_list[i][1]
            job_name: str = job_variable.name
            self.job_manager.remove_job(job_name)
            i += 1

        shutil.rmtree(self.working_dir)


class BatchJobManager:

    working_dir: str

    batch_dict: dict[str, BatchJobExecutor]
    lock: Lock

    job_manager: JobManager
    mapped_file_manager: MappedFileManager

    def __init__(
        self,
        auto_bench_dir: str,
        job_manager: JobManager,
        mapped_file_manager: MappedFileManager,
    ):
        self.batch_dict: dict[str, BatchJobExecutor] = {}
        self.lock = Lock()

        self.job_manager = job_manager
        self.mapped_file_manager = mapped_file_manager

        self.working_dir = os.path.join(auto_bench_dir, "batches")
        if not os.path.exists(self.working_dir):
            os.mkdir(self.working_dir)

    def run_batch(self, batch_config: BatchJobConfig):
        batch_name: str = batch_config.name
        if batch_name in self.batch_dict:
            raise RuntimeError(f"Batch job {batch_name} already exists")

        batch_dir: str = os.path.join(self.working_dir, batch_name)
        if not os.path.exists(batch_dir):
            os.mkdir(batch_dir)

        flattened_yml_list = []
        for job_config in batch_config.jobs:
            job_config: SingleJobConfig = job_config
            flattened_yml_list.append(job_config.template)
            flattened_yml_list.append(job_config.variable)

        mapped_files: list[MappedFile] = self.mapped_file_manager.construct_file_map(
            flattened_yml_list
        )
        try:
            job_list: list[(str, str)] = []
            file_index: int = 0
            while file_index < len(mapped_files):
                template: dict = yaml_utils.read_file(
                    mapped_files[file_index].working_path
                )
                variable: dict = yaml_utils.read_file(
                    mapped_files[file_index + 1].working_path
                )
                deployment_template = DeploymentTemplate.of(**template)
                job_variable = JobVariable.of(**variable)

                job_variable.name = (
                    f"{batch_name}-{int(file_index/2)}-{job_variable.name}"
                )

                job_list.append((deployment_template, job_variable))

                file_index += 2

            batch_job: BatchJob = BatchJob(batch_config, job_list)
            executor = BatchJobExecutor(batch_job, batch_dir, self.job_manager)

            with self.lock:
                self.batch_dict[batch_name] = executor

            executor.start()
        finally:
            self.mapped_file_manager.clear_file_map(mapped_files)

    def stop_batch(self, batch_name: str):
        if batch_name not in self.batch_dict:
            return
        self.batch_dict[batch_name].stop()

    def remove_batch(self, batch_name: str):
        with self.lock:
            if batch_name not in self.batch_dict:
                return
            executor: BatchJobExecutor = self.batch_dict[batch_name]
            if executor.is_running():
                raise RuntimeError(
                    f"Cannot remove running batch job {batch_name}. Please stop it first."
                )
            executor.clear()
            self.batch_dict.pop(batch_name)
