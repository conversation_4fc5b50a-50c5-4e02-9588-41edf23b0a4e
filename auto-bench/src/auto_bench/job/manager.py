import os
import queue
import traceback

from threading import Thread
from threading import Lock
from threading import Event
from time import sleep

from ..config import AutoBenchConfig, DeploymentTemplate, JobVariable
from ..command import ICommandExecutor
from ..file import MappedFileManager

from .job import <PERSON>BenchJob
from .executor import JobExecutor


class JobDict:
    jobs: dict[str:JobExecutor]
    lock: Lock

    def __init__(self):
        self.jobs = {}
        self.lock = Lock()

    def put(self, job_name: str, job: JobExecutor):
        with self.lock:
            self.jobs[job_name] = job

    def put_if_absent(self, job_name: str, job: JobExecutor) -> bool:
        with self.lock:
            if job_name in self.jobs:
                return False
            else:
                self.jobs[job_name] = job
                return True

    def pop(self, job_name: str):
        with self.lock:
            if job_name in self.jobs:
                return self.jobs.pop(job_name)
            else:
                return None

    def has(self, job_name: str):
        with self.lock:
            return job_name in self.jobs

    def get(self, job_name: str):
        with self.lock:
            return self.jobs.get(job_name)

    def keys(self):
        with self.lock:
            return list(self.jobs.keys())

    def size(self):
        with self.lock:
            return len(self.jobs)


class JobManager:

    config: AutoBenchConfig
    command_executor: ICommandExecutor
    mapped_file_manager: MappedFileManager

    working_dir: str

    job_dict: JobDict
    running_jobs: JobDict

    job_killer: Thread
    killing_queue: queue.Queue
    killer_event: Event

    def __init__(
        self,
        config: AutoBenchConfig,
        command_executor: ICommandExecutor,
        mapped_file_manager: MappedFileManager,
    ):
        self.config = config
        self.command_executor = command_executor
        self.mapped_file_manager = mapped_file_manager

        self.working_dir = os.path.join(self.config.data_path, "jobs")
        if not os.path.exists(self.working_dir):
            os.mkdir(self.working_dir)

        self.job_dict = JobDict()
        self.running_jobs = JobDict()

        self.killing_queue = queue.Queue()
        self.killer_event = Event()
        self.job_killer = Thread(target=self._check_and_kill_job)
        self.job_killer.start()

    def run_job(
        self, deployment_template: DeploymentTemplate, job_variable: JobVariable
    ) -> Event:
        job_name = job_variable.name
        job_dir = os.path.join(self.working_dir, job_name)
        job = AutoBenchJob(
            job_dir,
            deployment_template,
            job_variable,
        )
        executor = JobExecutor(
            job, job_variable.backend, self.command_executor, self.mapped_file_manager
        )

        if not self.job_dict.put_if_absent(job_name, executor):
            raise RuntimeError(f"Job {job_name} already exists.")

        self.running_jobs.put(job_name, executor)

        return executor.start()

    def stop_job(self, job_name: str):
        if not self.running_jobs.has(job_name):
            return
        stop_signal = Event()
        self.killing_queue.put((job_name, stop_signal))
        self.killer_event.set()
        stop_signal.wait()

    def _stop_job(self, job_name: str):
        executor: JobExecutor = self.running_jobs.get(job_name)
        if executor:
            executor.stop()
            self.running_jobs.pop(job_name)

    def _check_and_kill_job(self):
        while True:
            try:
                if self.killer_event.wait(10):
                    self.killer_event.clear()

                # process user stop instruction first
                while not self.killing_queue.empty():
                    job_name, stop_signal = self.killing_queue.get()
                    self._stop_job(job_name)
                    stop_signal: Event = stop_signal
                    stop_signal.set()

                if self.running_jobs.size() == 0:
                    continue

                for job_name in self.running_jobs.keys():

                    if not self.killing_queue.empty():
                        break

                    executor: JobExecutor = self.running_jobs.get(job_name)
                    if executor and executor.need_stop():
                        self._stop_job(job_name)

            except Exception as e:
                traceback.print_exc()
                print(e, flush=True)

    def remove_job(self, job_name: str):
        executor: JobExecutor = self.running_jobs.get(job_name)
        if executor:
            raise RuntimeError(
                f"Cannot remove running job {job_name}. Please stop it first."
            )

        executor: JobExecutor = self.job_dict.get(job_name)
        if executor:
            executor.clear()
            self.job_dict.pop(job_name)
