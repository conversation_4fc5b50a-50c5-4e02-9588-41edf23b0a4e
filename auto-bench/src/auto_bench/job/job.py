from ..config import DeploymentTemplate, JobVariable


class AutoBenchJob:

    job_dir: str
    deployment_template: DeploymentTemplate
    job_variable: JobVariable

    def __init__(
        self,
        job_dir: str,
        deployment_template: DeploymentTemplate,
        job_variable: JobVariable,
    ):
        self.job_dir = job_dir
        self.deployment_template = deployment_template
        self.job_variable = job_variable

    def name(self):
        return self.job_variable.name
