import yaml

from typing import Any


def read_file(path: str) -> dict:
    result = None
    with open(path, "r", encoding="utf-8") as f:
        result = yaml.load(f.read(), Loader=yaml.FullLoader)
    if result is None:
        raise IOError(f"Fail to read yaml file {path}")
    if not isinstance(result, dict):
        raise SyntaxError(f"Only support dict style yaml file.")
    return result


def to_str(yaml_dict: dict) -> str:
    return yaml.safe_dump(yaml_dict, default_flow_style=False)


def load_str(yaml_str: str) -> dict:
    return yaml.safe_load(yaml_str)


def _rewrite_param(subject: dict, depth: int, flatten_key: list[str], value: Any):
    next_key = flatten_key[depth]
    if isinstance(subject, list):
        next_key = int(next_key)

    if depth == len(flatten_key) - 1:
        subject[flatten_key[depth]] = value
    else:
        _rewrite_param(subject[next_key], depth + 1, flatten_key, value)


def rewrite_params(
    file_path: str, param_keys: dict[str, list[str]], param_values: dict[str, Any]
):
    with open(file_path, "r") as file:
        data = yaml.safe_load(file)

    for k, v in param_values.items():
        _rewrite_param(data, 0, param_keys[k], v)

    with open(file_path, "w") as file:
        yaml.safe_dump(data, file, default_flow_style=False)


def dump_to_file(file_path: str, data: dict) -> None:
    with open(file_path, "w") as file:
        yaml.safe_dump(data, file, default_flow_style=False)


def dump_all_to_file(file_path: str, data: list[dict]) -> None:
    with open(file_path, "w") as file:
        yaml.dump_all(data, file, default_flow_style=False)
