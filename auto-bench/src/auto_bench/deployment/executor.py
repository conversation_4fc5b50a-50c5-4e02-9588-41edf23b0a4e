from .interface import IDeployment, ServiceStatus


class DeploymentExecutor:

    deployment: IDeployment

    def __init__(self, deployment: IDeployment):
        self.deployment = deployment

    def deploy(self):
        # generate deployment config: docker args, k8s yml
        self.deployment.prepare_config()
        # execute deployment: docker run, k8s apply
        self.deployment.execute_deployment()

    def terminate(self):
        self.deployment.terminate_deployment()

    def clear(self):
        self.deployment.delete_config()

    def get_service_status(self, service_list: list[str]) -> list[ServiceStatus]:
        return self.deployment.inspect(service_list)
