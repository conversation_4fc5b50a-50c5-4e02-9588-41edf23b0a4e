from abc import ABCMeta, abstractmethod


class ServiceStatus:
    name: str
    running: bool
    duration: float

    def __init__(self, name: str, running: bool, duration: float):
        self.name = name
        self.running = running
        self.duration = duration


class IDeployment(metaclass=ABCMeta):

    @abstractmethod
    def get_deployment_template(self):
        pass

    @abstractmethod
    def get_job_variables(self):
        pass

    @abstractmethod
    def prepare_config(self):
        pass

    @abstractmethod
    def execute_deployment(self):
        pass

    @abstractmethod
    def terminate_deployment(self):
        pass

    @abstractmethod
    def delete_config(self):
        pass

    @abstractmethod
    def inspect(self, service_list: list[str]) -> list[ServiceStatus]:
        pass
