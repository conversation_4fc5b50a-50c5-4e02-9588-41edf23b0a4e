import os
import json
import time

from datetime import datetime

from ...config import DeploymentTemplate, ServiceTemplate, JobVariable, ServiceVariable
from ...utils import yaml_utils
from ...command import ICommandExecutor, CommandResult
from ...file import MappedFileManager

from ..interface import IDeployment, ServiceStatus


class DockerDeployment(IDeployment):

    working_dir: str
    template: DeploymentTemplate
    variables: JobVariable
    command_executor: ICommandExecutor
    mapped_file_manager: MappedFileManager

    def __init__(
        self,
        working_dir: str,
        template: DeploymentTemplate,
        variables: JobVariable,
        command_executor: ICommandExecutor,
        mapped_file_manager: MappedFileManager,
    ):
        self.working_dir = working_dir
        self.template = template
        self.variables = variables
        self.command_executor = command_executor
        self.mapped_file_manager = mapped_file_manager

    def get_deployment_template(self):
        return self.template

    def get_job_variables(self):
        return self.variables

    def _get_container_name(self, service_name: str) -> str:
        return f"auto_bench-{self.variables.name}-{service_name}"

    def _get_local_docker_compose_yaml_path(self) -> str:
        return os.path.join(self.working_dir, "docker-compose.yml")

    def _get_host_docker_compose_yaml_path(self) -> str:
        host_working_dir = os.environ.get("AUTO_BENCH_WORKING_DIR")
        return os.path.join(host_working_dir, self.working_dir, "docker-compose.yml")

    def _get_host_data_dir(self) -> str:
        return os.path.join(os.environ.get("AUTO_BENCH_WORKING_DIR"), self.working_dir)

    def _handle_depends_on(self, docker_compose_yaml: dict):
        depended_service_dict: dict[str:str] = {}

        # parse to depends_on in docker_compose_yaml
        for service_name, service_template in self.template.services.items():
            service_template: ServiceTemplate = service_template

            if service_template.depends_on is None:
                continue

            service_yaml: dict = docker_compose_yaml["services"][service_name]

            depends_on_yaml: dict = {}
            for depended_service_name, test_url in service_template.depends_on.items():
                depends_on_yaml[depended_service_name] = {
                    "condition": "service_healthy"
                }

                if depended_service_name not in depended_service_dict:
                    depended_service_dict[depended_service_name] = test_url

            service_yaml["depends_on"] = depends_on_yaml

        # add healthcheck
        for service_name, test_url in depended_service_dict.items():
            service_yaml: dict = docker_compose_yaml["services"][service_name]
            test_url: str = test_url
            healthcheck_yaml: dict = {
                "test": [
                    "CMD",
                    "curl",
                    "-f",
                    test_url.replace(service_name, "localhost", 1),
                ],
                "interval": "15s",
                "timeout": "10s",
                "retries": 20,
            }
            service_yaml["healthcheck"] = healthcheck_yaml

    def prepare_config(self):
        docker_compose_yaml = {"version": "3.8", "services": {}}

        job_name = self.variables.name

        for service_name, service_template in self.template.services.items():
            service_template: ServiceTemplate = service_template

            service_yaml = {
                "image": service_template.image,
                "container_name": self._get_container_name(service_name),
            }

            if service_template.ports:
                service_yaml["expose"] = [_ for _ in service_template.ports]

            if service_template.environment:
                service_yaml["environment"] = service_template.environment

            if service_template.volumes:
                service_yaml["volumes"] = service_template.volumes

            if service_template.command is not None:
                if isinstance(service_template.command, str):
                    service_yaml["entrypoint"] = f"{service_template.command}"
                elif isinstance(service_template.command, list):
                    service_yaml["entrypoint"] = f"{' '.join(service_template.command)}"

            if service_template.args:
                service_yaml["command"] = service_template.args

            if service_name in self.variables.services:
                service_variables: ServiceVariable = self.variables.services[
                    service_name
                ]
                if service_variables.resources:
                    service_resources = service_variables.resources
                else:
                    service_resources = None
            else:
                service_resources = None

            if service_resources:
                limit_dict = {}
                reservation_dict = {}

                if "cpu" in service_resources:
                    limit_dict["cpus"] = f'{service_resources["cpu"]}'
                    reservation_dict["cpus"] = f'{service_resources["cpu"]}'
                if "memory" in service_resources:
                    limit_dict["memory"] = service_resources["memory"]
                    reservation_dict["memory"] = service_resources["memory"]
                if "gpu" in service_resources:
                    reservation_dict["devices"] = [
                        {
                            "capabilities": ["gpu"],
                            "count": service_resources["gpu"],
                        }
                    ]
                    service_yaml["ipc"] = "host"  # for vllm
                    service_yaml["runtime"] = "nvidia"

                service_yaml["deploy"] = {
                    "resources": {
                        "limits": limit_dict,
                        "reservations": reservation_dict,
                    }
                }

            docker_compose_yaml["services"][service_name] = service_yaml

        self._handle_depends_on(docker_compose_yaml)

        yaml_utils.dump_to_file(
            self._get_local_docker_compose_yaml_path(), docker_compose_yaml
        )

    def execute_deployment(self):
        result: CommandResult = self.command_executor.execute_command(
            [
                "docker-compose",
                "-f",
                self._get_host_docker_compose_yaml_path(),
                "up",
                "-d",
            ]
        )
        if not result.success:
            raise RuntimeError(f"Deployment failed\n{result.err}")

    def terminate_deployment(self):
        result: CommandResult = self.command_executor.execute_command(
            ["docker-compose", "-f", self._get_host_docker_compose_yaml_path(), "down"]
        )
        if not result.success:
            raise RuntimeError(f"Termination failed\n{result.err}")

    def delete_config(self):
        docker_compose_yaml = self._get_local_docker_compose_yaml_path()
        if os.path.exists(docker_compose_yaml):
            os.remove(docker_compose_yaml)

    def inspect(self, service_list: list[str]) -> list[ServiceStatus]:
        result: CommandResult = self.command_executor.execute_command(
            "docker inspect "
            "--format="
            "'{"
            '"Name": "{{.Name}}", '
            '"Running": {{.State.Running}}, '
            '"StartedAt": "{{.State.StartedAt}}"'
            "}' "
            f"{' '.join(
                [self._get_container_name(_) for _ in service_list]
            )}"
        )

        if not result.success:
            raise RuntimeError(f"Inspect failed\n{result.err}")

        raw_results: list[str] = result.out.strip().split("\n")
        status_list: list[ServiceStatus] = []
        for i in range(len(service_list)):
            line: str = raw_results[i]
            parsed_line: dict[str, str] = json.loads(line)
            raw_name: str = parsed_line["Name"]
            name = raw_name[1:]
            start_time_str: str = parsed_line["StartedAt"]
            start_time: datetime = datetime.fromisoformat(
                start_time_str.replace("Z", "+00:00")
            )

            status_list.append(
                ServiceStatus(
                    name=service_list[i],
                    running=bool(parsed_line["Running"]),
                    duration=time.time() - start_time.timestamp().real,
                )
            )

        return status_list
