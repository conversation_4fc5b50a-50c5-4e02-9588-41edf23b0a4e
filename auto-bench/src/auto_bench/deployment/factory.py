from ..config import DeploymentTemplate, JobVariable
from ..command import ICommandExecutor
from ..file import MappedFileManager

from .interface import IDeployment
from .docker_impl import DockerDeployment
from .k8s_impl import K8sDeployment


class DeploymentFactory:

    @classmethod
    def create_deployment(
        cls,
        backend: str,
        working_dir: str,
        template: DeploymentTemplate,
        variables: JobVariable,
        command_executor: ICommandExecutor,
        mapped_file_manager: MappedFileManager,
    ) -> IDeployment:
        if backend == "docker":
            return DockerDeployment(
                working_dir, template, variables, command_executor, mapped_file_manager
            )
        elif backend == "k8s":
            return K8sDeployment(
                working_dir, template, variables, command_executor, mapped_file_manager
            )
        else:
            raise ValueError(f"Unsupported backend: {backend}")
