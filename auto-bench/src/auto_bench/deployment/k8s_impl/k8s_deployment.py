import os
import time
import json

from datetime import datetime

from ...config import DeploymentTemplate, ServiceTemplate, JobVariable, ServiceVariable
from ...command import ICommandExecutor, CommandResult
from ...file import MappedFileManager
from ...utils import yaml_utils

from ..interface import IDeployment, ServiceStatus

DEPLOYMENT_FILE = "deployment_config.yaml"
SERVICE_FILE = "service_config.yaml"


class K8sDeployment(IDeployment):

    working_dir: str
    template: DeploymentTemplate
    variables: JobVariable
    command_executor: ICommandExecutor
    mapped_file_manager: MappedFileManager

    namespace: str

    def __init__(
        self,
        working_dir: str,
        template: DeploymentTemplate,
        variables: JobVariable,
        command_executor: ICommandExecutor,
        mapped_file_manager: MappedFileManager,
    ):
        self.working_dir = working_dir
        self.template = template
        self.variables = variables
        self.command_executor = command_executor
        self.mapped_file_manager = mapped_file_manager

        self.namespace = f"auto-bench-{variables.name}"

    def get_deployment_template(self):
        return self.template

    def get_job_variables(self):
        return self.variables

    def _get_host_dir_path(self):
        return os.path.join(os.environ.get("AUTO_BENCH_WORKING_DIR"), self.working_dir)

    def _get_pod_name(self, service_name: str) -> str:
        return f"{service_name}-pod"

    def _prepare_as_deployment(
        self,
        service_name: str,
        service_template: ServiceTemplate,
    ) -> (dict, dict):
        deployment_yml = {
            "apiVersion": "apps/v1",
            "kind": "Deployment",
            "metadata": {
                "name": self._get_pod_name(service_name),
                "namespace": self.namespace,
            },
            "spec": {
                "selector": {"matchLabels": {"app": service_name}},
                "template": {
                    "metadata": {"labels": {"app": service_name}},
                    "spec": {
                        "restartPolicy": "Always",
                    },
                },
            },
        }

        deployment_spec_dict = deployment_yml["spec"]["template"]["spec"]
        container_dict = {
            "name": service_name,
            "image": service_template.image,
            "ports": [{"containerPort": _} for _ in service_template.ports],
        }

        if service_template.environment:
            container_dict["env"] = [
                {
                    "name": k,
                    "value": str(v),
                }
                for k, v in service_template.environment.items()
            ]

        if service_template.volumes:
            volume_list = []
            path_pair_list = []
            idx = 0
            for volume_str in service_template.volumes:
                pair = volume_str.split(":")
                path_pair_list.append(pair)
                volume_list.append(
                    {
                        "name": f"{service_name}-volume-{idx}",
                        "hostPath": {
                            "path": pair[0],
                            "type": (
                                "DirectoryOrCreate"
                                if self.mapped_file_manager.is_dir(pair[0])
                                else "FileOrCreate"
                            ),
                        },
                    }
                )
                idx += 1
            deployment_spec_dict["volumes"] = volume_list
            container_dict["volumeMounts"] = [
                {
                    "name": f"{service_name}-volume-{_}",
                    "mountPath": path_pair_list[_][1],
                }
                for _ in range(len(volume_list))
            ]

        if service_template.command is not None:
            if isinstance(service_template.command, str):
                container_dict["command"] = ["/bin/sh", "-c"]
                if service_template.args:
                    container_dict["args"] = [
                        f"{service_template.command} {' '.join(service_template.args)}"
                    ]
                else:
                    container_dict["args"] = [f"{service_template.command}"]
            elif isinstance(service_template.command, list):
                container_dict["command"] = service_template.command
                if service_template.args:
                    container_dict["args"] = service_template.args
        elif service_template.args:
            container_dict["args"] = service_template.args

        if service_name in self.variables.services:
            service_variables: ServiceVariable = self.variables.services[service_name]
            if service_variables.resources:
                service_resources = service_variables.resources
            else:
                service_resources = None
        else:
            service_resources = None

        if service_resources:
            limit_dict = {}
            request_dict = {}

            if "cpu" in service_resources:
                limit_dict["cpu"] = f'{service_resources["cpu"]}'
                request_dict["cpu"] = f'{service_resources["cpu"]}'
            if "memory" in service_resources:
                limit_dict["memory"] = service_resources["memory"]
                request_dict["memory"] = service_resources["memory"]
            if "gpu" in service_resources:
                limit_dict["nvidia.com/gpu"] = service_resources["gpu"]
                request_dict["nvidia.com/gpu"] = service_resources["gpu"]
                deployment_spec_dict["hostIPC"] = True  # for vllm

            container_dict["resources"] = {
                "limits": limit_dict,
                "requests": request_dict,
            }

        deployment_spec_dict["containers"] = [container_dict]

        # handle depends_on
        if service_template.depends_on:
            init_container_yml = {
                "name": f"{service_name}-init",
                "image": "curlimages/curl",
                "command": [
                    "sh",
                    "-c",
                    " ".join(
                        [
                            f"until curl -f {v}; do echo waiting for {k}; sleep 5; done;"
                            for k, v in service_template.depends_on.items()
                        ]
                    ),
                ],
            }
            deployment_spec_dict["initContainers"] = [init_container_yml]

        service_yml = {
            "apiVersion": "v1",
            "kind": "Service",
            "metadata": {
                "name": service_name,
                "namespace": self.namespace,
            },
            "spec": {
                "ports": [
                    {
                        "name": f"http-{service_name}",
                        "port": _,
                        "protocol": "TCP",
                        "targetPort": _,
                    }
                    for _ in service_template.ports
                ],
                "selector": {
                    "app": service_name,
                },
            },
        }

        return deployment_yml, service_yml

    def _prepare_as_job(
        self,
        service_name: str,
        service_template: ServiceTemplate,
    ) -> dict:
        deployment_yml = {
            "apiVersion": "batch/v1",
            "kind": "Job",
            "metadata": {
                "name": self._get_pod_name(service_name),
                "namespace": self.namespace,
            },
            "spec": {
                "template": {
                    "metadata": {"labels": {"app": service_name}},
                    "spec": {
                        "restartPolicy": "Never",
                    },
                },
            },
        }

        deployment_spec_dict = deployment_yml["spec"]["template"]["spec"]
        container_dict = {
            "name": service_name,
            "image": service_template.image,
        }

        if service_template.ports:
            container_dict["ports"] = [
                {"containerPort": _} for _ in service_template.ports
            ]

        if service_template.environment:
            container_dict["env"] = [
                {
                    "name": k,
                    "value": str(v),
                }
                for k, v in service_template.environment.items()
            ]

        if service_template.volumes:
            volume_list = []
            path_pair_list = []
            idx = 0
            for volume_str in service_template.volumes:
                pair = volume_str.split(":")
                path_pair_list.append(pair)
                volume_list.append(
                    {
                        "name": f"{service_name}-volume-{idx}",
                        "hostPath": {
                            "path": pair[0],
                            "type": (
                                "DirectoryOrCreate"
                                if self.mapped_file_manager.is_dir(pair[0])
                                else "FileOrCreate"
                            ),
                        },
                    }
                )
                idx += 1
            deployment_spec_dict["volumes"] = volume_list
            container_dict["volumeMounts"] = [
                {
                    "name": f"{service_name}-volume-{_}",
                    "mountPath": path_pair_list[_][1],
                }
                for _ in range(len(volume_list))
            ]

        if service_template.command is not None:
            if isinstance(service_template.command, str):
                container_dict["command"] = ["/bin/sh", "-c"]
                if service_template.args:
                    container_dict["args"] = [
                        f"{service_template.command} {' '.join(service_template.args)}"
                    ]
                else:
                    container_dict["args"] = [f"{service_template.command}"]
            elif isinstance(service_template.command, list):
                container_dict["command"] = service_template.command
                if service_template.args:
                    container_dict["args"] = service_template.args
        elif service_template.args:
            container_dict["args"] = service_template.args

        if service_name in self.variables.services:
            service_variables: ServiceVariable = self.variables.services[service_name]
            if service_variables.resources:
                service_resources = service_variables.resources
            else:
                service_resources = None
        else:
            service_resources = None

        if service_resources:
            limit_dict = {}
            request_dict = {}

            if "cpu" in service_resources:
                limit_dict["cpu"] = f'{service_resources["cpu"]}'
                request_dict["cpu"] = f'{service_resources["cpu"]}'
            if "memory" in service_resources:
                limit_dict["memory"] = service_resources["memory"]
                request_dict["memory"] = service_resources["memory"]
            if "gpu" in service_resources:
                limit_dict["nvidia.com/gpu"] = service_resources["gpu"]
                request_dict["nvidia.com/gpu"] = service_resources["gpu"]
                deployment_spec_dict["hostIPC"] = True  # for vllm

            container_dict["resources"] = {
                "limits": limit_dict,
                "requests": request_dict,
            }

        deployment_spec_dict["containers"] = [container_dict]

        # handle depends_on
        if service_template.depends_on:
            init_container_yml = {
                "name": f"{service_name}-init",
                "image": "curlimages/curl",
                "command": [
                    "sh",
                    "-c",
                    " ".join(
                        [
                            f"until curl -f {v}; do echo waiting for {k}; sleep 5; done;"
                            for k, v in service_template.depends_on.items()
                        ]
                    ),
                ],
            }
            deployment_spec_dict["initContainers"] = [init_container_yml]

        return deployment_yml

    def prepare_config(self):

        deployment_yml_list = []
        service_yml_list = []

        for service_name, service_template in self.template.services.items():
            if (not service_template.ports) or (
                service_name in self.variables.ends_on
                and self.variables.ends_on[service_name].lower() == "exit"
            ):
                deployment_yml_list.append(
                    self._prepare_as_job(service_name, service_template)
                )
            else:
                deployment_yml, service_yml = self._prepare_as_deployment(
                    service_name, service_template
                )
                deployment_yml_list.append(deployment_yml)
                service_yml_list.append(service_yml)

        yaml_utils.dump_all_to_file(
            os.path.join(self.working_dir, DEPLOYMENT_FILE),
            deployment_yml_list,
        )
        yaml_utils.dump_all_to_file(
            os.path.join(self.working_dir, SERVICE_FILE),
            service_yml_list,
        )

    def execute_deployment(self):
        result: CommandResult = self.command_executor.execute_command(
            f"kubectl create namespace {self.namespace} "
            f"&& "
            f"kubectl apply -f {os.path.join(self._get_host_dir_path(), DEPLOYMENT_FILE)} --namespace {self.namespace}"
            f"&& "
            f"kubectl apply -f {os.path.join(self._get_host_dir_path(), SERVICE_FILE)} --namespace {self.namespace}"
        )
        if not result.success:
            raise RuntimeError(f"Deployment failed\n{result.err}")

    def terminate_deployment(self):
        result: CommandResult = self.command_executor.execute_command(
            [
                "kubectl",
                "delete",
                "namespace",
                self.namespace,
            ]
        )
        if not result.success:
            raise RuntimeError(f"Termination failed\n{result.err}")

    def delete_config(self):
        service_file_path = os.path.join(self.working_dir, SERVICE_FILE)
        if os.path.exists(service_file_path):
            os.remove(service_file_path)

        deployment_file_path = os.path.join(self.working_dir, DEPLOYMENT_FILE)
        if os.path.exists(deployment_file_path):
            os.remove(deployment_file_path)

    def inspect(self, service_list: list[str]) -> list[ServiceStatus]:
        result: CommandResult = self.command_executor.execute_command(
            "kubectl get pods "
            "-l 'app in ("
            f"{', '.join(
                service_list
            )}"
            ")' "
            f"-n {self.namespace} "
            "-o jsonpath="
            "'{range .items[*]}"
            '{"{"}'
            '{"\\"name\\": \\""}'
            "{.metadata.name}"
            '{"\\", "}'
            '{"\\"app\\": \\""}'
            "{.metadata.labels.app}"
            '{"\\", "}'
            '{"\\"state\\": "}'
            "{.status.containerStatuses[0].state}"
            '{"}"}'
            "\n"
            "{end}'"
        )

        if not result.success:
            raise RuntimeError(f"Inspect failed\n{result.err}")

        raw_results: list[str] = result.out.strip().split("\n")

        status_dict: dict[str, ServiceStatus] = {}

        for i in range(len(service_list)):
            line: str = raw_results[i]
            parsed_line: dict[str] = json.loads(line)
            state: dict = parsed_line["state"]
            is_running = "running" in state
            is_waiting = "waiting" in state
            is_terminated = "terminated" in state

            duration = 0
            if is_running:
                start_time_str: str = state["running"]["startedAt"]
                start_time: datetime = datetime.fromisoformat(
                    start_time_str.replace("Z", "+00:00")
                )
                duration = time.time() - start_time.timestamp().real

            # handle multi replica, select the oldest one
            app_name: str = parsed_line["app"]
            if app_name not in status_dict:
                status_dict[app_name] = ServiceStatus(
                    name=app_name, running=not is_terminated, duration=duration
                )
            else:
                if duration > status_dict[app_name].duration:
                    status_dict[app_name] = ServiceStatus(
                        name=app_name, running=not is_terminated, duration=duration
                    )

        return [status_dict[_] for _ in service_list]
