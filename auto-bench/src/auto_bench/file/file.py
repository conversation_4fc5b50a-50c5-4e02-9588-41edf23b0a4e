import os
import threading
import shutil

from ..command import ICommandExecutor, CommandResult


class MappedFile:
    group: int
    host_path: str
    working_path: str

    def __init__(self, group: int, host_path: str, working_path: str):
        self.group = group
        self.host_path = host_path
        self.working_path = working_path


class MappedFileManager:
    working_dir: str

    command_executor: ICommandExecutor

    _group_idx: int
    _lock: threading.Lock

    def __init__(self, auto_bench_dir: str, command_executor: ICommandExecutor):
        self.command_executor = command_executor

        self._group_idx = 0
        self._lock = threading.Lock()

        self.working_dir = os.path.join(auto_bench_dir, "map")
        if os.path.exists(self.working_dir):
            shutil.rmtree(self.working_dir)
        os.mkdir(self.working_dir)

    def _get_group_idx(self) -> int:
        with self._lock:
            current_idx = self._group_idx
            self._group_idx += 1
            return current_idx

    def construct_file_map(self, request_files: list[str]) -> list[MappedFile]:
        group_idx = self._get_group_idx()
        group_dir = os.path.join(self.working_dir, str(group_idx))
        os.mkdir(group_dir)

        mapped_file_list = []
        file_dict = {}
        file_idx = 0
        for host_file in request_files:
            file_name = f"{file_idx}_{os.path.basename(host_file)}"
            working_path = os.path.join(group_dir, file_name)
            mapped_file_list.append(MappedFile(group_idx, host_file, working_path))
            if host_file not in file_dict:
                file_dict[host_file] = []
            file_dict[host_file].append(working_path)

            file_idx += 1

        command_result: CommandResult = self.command_executor.pull_file(file_dict)
        if command_result.success:
            return mapped_file_list
        else:
            raise RuntimeError(f"Pull failed due to {command_result.err}")

    def commit_files(self, mapped_file_list: list[MappedFile]) -> None:
        file_dict = {}
        for mapped_file in mapped_file_list:
            file_dict[mapped_file.working_path] = mapped_file.host_path

        self.command_executor.push_file(file_dict)

    def clear_file_map(self, mapped_file_list: list[MappedFile]) -> None:
        for mapped_file in mapped_file_list:
            os.remove(mapped_file.working_path)
            dir_path = os.path.dirname(mapped_file.working_path)
            if len(os.listdir(dir_path)) == 0:
                os.rmdir(dir_path)

    def is_dir(self, path: str) -> bool:
        result = self.command_executor.execute_command(["file", path])
        if result.success and result.out.strip().endswith("directory"):
            return True
        else:
            return False
