import subprocess

from typing import Optional

from ..interface import ICommandExecutor, CommandResult


class LocalExecutor(ICommandExecutor):

    def execute_command(self, command: Optional[str | list[str]]) -> CommandResult:
        result = subprocess.run(command, capture_output=True, text=True, shell=True)
        return CommandResult(
            success=result.returncode == 0, out=result.stdout, err=result.stderr
        )

    def pull_file(self, file_dict: dict[str, list[str]]) -> CommandResult:
        try:
            for host_file, working_file_list in file_dict.items():
                for working_file in working_file_list:
                    self.execute_command(["cp", host_file, working_file])
            return CommandResult(
                success=True,
                out=f"Successfully push files {file_dict}",
                err="",
            )
        except IOError as error:
            return CommandResult(
                success=False,
                out="",
                err=f"Push file failed due to {error}",
            )

    def push_file(self, file_dict: dict[str:str]) -> CommandResult:
        try:
            for working_file, host_file in file_dict.items():
                self.execute_command(["cp", working_file, host_file])
            return CommandResult(
                success=True,
                out=f"Successfully push files {file_dict}",
                err="",
            )
        except IOError as error:
            return CommandResult(
                success=False,
                out="",
                err=f"Push file failed due to {error}",
            )
