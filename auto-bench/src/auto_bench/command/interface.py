from abc import ABCMeta, abstractmethod
from typing import Optional


class CommandResult:
    success: bool
    out: str
    err: str

    def __init__(self, success: bool, out: str, err: str):
        self.success = success
        self.out = out
        self.err = err


class ICommandExecutor(metaclass=ABCMeta):

    @abstractmethod
    def execute_command(self, command: Optional[str | list[str]]) -> CommandResult:
        pass

    @abstractmethod
    def pull_file(self, file_dict: dict[str, list[str]]) -> CommandResult:
        """file_dict: dict {host_file: working_file}"""
        pass

    @abstractmethod
    def push_file(self, file_dict: dict[str, str]) -> CommandResult:
        """file_dict: dict {working_file: host_file}"""
        pass
