import paramiko

from typing import Optional

from ...config import SSHConfig

from ..interface import ICommandExecutor, CommandResult


class SSHExecutor(ICommandExecutor):

    ssh_config: SSHConfig

    def __init__(self, ssh_config: SSHConfig):
        self.ssh_config = ssh_config

    def _setup_ssh_session(self) -> paramiko.SSHClient:
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(
            self.ssh_config.host,
            username=self.ssh_config.username,
            password=self.ssh_config.password,
        )

        return ssh

    def execute_command(self, command: Optional[str | list[str]]) -> CommandResult:
        ssh = self._setup_ssh_session()

        if isinstance(command, list):
            command = " ".join(command)

        stdin, stdout, stderr = ssh.exec_command(str(command))
        exit_code = stdout.channel.recv_exit_status()
        stdout_output = stdout.read().decode()
        stderr_output = stderr.read().decode()

        ssh.close()

        if exit_code != 0:
            return CommandResult(
                success=False,
                out=stdout_output,
                err=stderr_output,
            )
        else:
            return CommandResult(
                success=True,
                out=stdout_output,
                err=stderr_output,
            )

    def pull_file(self, file_dict: dict[str, list[str]]) -> CommandResult:
        ssh = self._setup_ssh_session()
        sftp = ssh.open_sftp()
        try:
            for host_file, working_file_list in file_dict.items():
                for working_file in working_file_list:
                    sftp.get(host_file, working_file)
            return CommandResult(
                success=True,
                out=f"Successfully pull files {file_dict}.",
                err="",
            )
        except IOError as error:
            return CommandResult(
                success=False,
                out="",
                err=f"Pull file failed due to {error}",
            )
        finally:
            sftp.close()
            ssh.close()

    def push_file(self, file_dict: dict[str:str]) -> CommandResult:
        ssh = self._setup_ssh_session()
        sftp = ssh.open_sftp()
        try:
            for working_file, host_file in file_dict.items():
                sftp.put(working_file, host_file)
            return CommandResult(
                success=True,
                out=f"Successfully push files {file_dict}",
                err="",
            )
        except IOError as error:
            return CommandResult(
                success=False,
                out="",
                err=f"Push file failed due to {error}",
            )
        finally:
            sftp.close()
            ssh.close()
