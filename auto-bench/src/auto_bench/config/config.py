from ..utils import yaml_utils


class SSHConfig:
    host: str
    username: str

    port: int
    password: str
    ssh_key: str

    def __init__(
        self,
        host: str,
        username: str,
        port: int = 22,
        password: str = None,
        ssh_key: str = None,
    ):
        self.host = host
        self.port = port
        self.username = username
        self.password = password
        self.ssh_key = ssh_key

    @classmethod
    def of(
        cls,
        host: str,
        username: str,
        port: int = 22,
        password: str = None,
        ssh_key: str = None,
    ) -> "SSHConfig":
        return SSHConfig(host, username, port, password, ssh_key)


class AutoBenchConfig:

    data_path: str
    ssh: SSHConfig

    def __init__(self, data_path: str = "data", ssh: SSHConfig = None):
        self.data_path = data_path
        self.ssh = ssh

    def to_str(self):
        output_dict = {
            "ssh": self.ssh.__dict__,
        }
        return yaml_utils.to_str(output_dict)

    @classmethod
    def of(
        cls,
        data_path: str = "data",
        ssh: dict[str, str] = None,
    ):
        return AutoBenchConfig(data_path, SSHConfig.of(**ssh))

    @classmethod
    def load_from_file(cls, path: str):
        config_dict = yaml_utils.read_file(path)

        return AutoBenchConfig.of(**config_dict)

    @classmethod
    def load_from_dict(cls, config_dict: dict):
        return AutoBenchConfig.of(**config_dict)
