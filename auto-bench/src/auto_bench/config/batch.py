from ..utils import yaml_utils


class SingleJobConfig:
    template: str
    variable: str

    def __init__(
        self,
        template: str,
        variable: str,
    ):
        self.template: str = template
        self.variable: str = variable


class BatchJobConfig:

    version: str
    name: str
    index: int

    jobs: list[SingleJobConfig]

    def __init__(
        self, version: str, name: str, jobs: list[SingleJobConfig], index: int = 0
    ):
        self.version: str = version
        self.name: str = name
        self.index: int = index
        self.jobs: list[SingleJobConfig] = jobs

    @classmethod
    def of(cls, version: str, name: str, jobs: list[dict], index: int = 0):
        parsed_jobs = [SingleJobConfig(**_) for _ in jobs]
        return BatchJobConfig(version, name, parsed_jobs, index)

    @classmethod
    def load_from_file(cls, path: str):
        config_dict = yaml_utils.read_file(path)

        return BatchJobConfig.of(**config_dict)
