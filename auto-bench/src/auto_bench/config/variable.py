from typing import Any

from ..utils import yaml_utils


class ServiceVariable:
    resources: dict[str, Any]
    variables: dict[str, Any]

    def __init__(
        self, resources: dict[str, Any] = None, variables: dict[str, Any] = None
    ):
        self.resources = resources if resources is not None else {}
        self.variables = variables if variables is not None else {}


class JobVariable:
    version: str = "0.0.1"
    name: str
    backend: str
    services: dict[str, ServiceVariable]
    ends_on: dict[str, str]

    def __init__(
        self,
        version: str,
        name: str,
        backend: str = "docker",
        services: dict[str, ServiceVariable] = None,
        ends_on: dict[str, str] = None,
    ):
        self.version = version
        self.name = name
        self.backend = backend
        self.services = services if services else {}
        self.ends_on = ends_on if ends_on else {}

    @classmethod
    def of(
        cls,
        version: str,
        name: str,
        backend: str = "docker",
        services: dict[str, dict] = None,
        ends_on: dict[str, str] = None,
    ) -> "JobVariable":
        parsed_services = {}
        if services is not None:
            for k, v in services.items():
                parsed_services[k] = ServiceVariable(**v)
        return JobVariable(version, name, backend, parsed_services, ends_on)

    @classmethod
    def from_file(cls, filename: str) -> "JobVariable":
        return JobVariable.of(**(yaml_utils.read_file(filename)))
