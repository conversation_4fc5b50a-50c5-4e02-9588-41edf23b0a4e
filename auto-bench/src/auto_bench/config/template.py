from typing import Optional

from ..utils import yaml_utils


class VariableLocation:
    file_path: str
    flatten_key: list[str]

    def __init__(self, file_path: str, flatten_key: list[str]):
        self.file_path = file_path
        self.flatten_key = flatten_key

    @classmethod
    def of(cls, file_path: str, flatten_key: str) -> "VariableLocation":
        return VariableLocation(file_path, flatten_key.split("/"))


class ServiceTemplate:
    image: str

    ports: list[int | str]
    # key -> value
    environment: dict[str, str]
    # host path : path in container
    volumes: list[str]
    command: Optional[str | list[str]]
    args: list[str]

    # name -> file_path:flatten_key in container
    # for url:port of service communication, manually define it in variables
    variables: dict[str, Optional[VariableLocation | str]]

    # service_name -> test url
    depends_on: dict[str:str]

    def __init__(
        self,
        image: str,
        ports: list[int | str] = None,
        environment: dict[str:str] = None,
        volumes: list[str] = None,
        command: Optional[str | list[str]] = None,
        args: list[str] = None,
        variables: dict[str, Optional[VariableLocation | str]] = None,
        depends_on: dict[str, str] = None,
    ):
        self.image = image
        self.ports = ports
        self.environment = environment
        self.volumes = volumes
        self.command = command
        self.args = args
        self.variables = variables
        self.depends_on = depends_on

    @classmethod
    def of(
        cls,
        image: str,
        ports: list[int | str] = None,
        environment: dict[str, str] = None,
        volumes: list[str] = None,
        command: Optional[str | list[str]] = None,
        args: list[str] = None,
        variables: dict[str, dict] = None,
        depends_on: dict[str, str] = None,
    ) -> "ServiceTemplate":
        parsed_variables = {}
        if variables is not None:
            for k, v in variables.items():
                if isinstance(v, str):
                    parsed_variables[k] = v
                else:
                    parsed_variables[k] = VariableLocation.of(**v)
        return ServiceTemplate(
            image,
            ports,
            environment,
            volumes,
            command,
            args,
            parsed_variables,
            depends_on,
        )


class DeploymentTemplate:
    version: str = "0.0.1"
    services: dict[str, ServiceTemplate]

    def __init__(self, version: str, services: dict[str, ServiceTemplate]):
        self.version = version
        self.services = services

    @classmethod
    def of(cls, version: str, services: dict[str, dict]) -> "DeploymentTemplate":
        parsed_services = {}
        for k, v in services.items():
            parsed_services[k] = ServiceTemplate.of(**v)
        return DeploymentTemplate(version, parsed_services)

    @classmethod
    def from_file(cls, file_path: str) -> "DeploymentTemplate":
        return DeploymentTemplate.of(**(yaml_utils.read_file(file_path)))
