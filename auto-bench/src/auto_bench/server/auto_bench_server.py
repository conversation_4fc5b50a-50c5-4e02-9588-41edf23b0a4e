import os
import traceback

from fast_service import FastServiceConfig

from ..config import AutoBenchConfig, DeploymentTemplate, JobVariable, BatchJobConfig
from ..protocol import (
    AutoBenchProtocol,
    AutoBenchMessage,
    RunJobRequest,
    StopJobRequest,
    RemoveJobRequest,
    RunBatchJobRequest,
    StopBatchJobRequest,
    RemoveBatchJobRequest,
    setup_server,
)
from ..command import ICommandExecutor, CommandExecutorFactory, CommandResult
from ..job import JobManager, BatchJobManager
from ..utils import yaml_utils
from ..file import MappedFile, MappedFileManager


class AutoBenchServer(AutoBenchProtocol):

    config: AutoBenchConfig

    command_executor: ICommandExecutor
    file_manager: MappedFileManager
    job_manager: JobManager
    batch_job_manager: BatchJobManager

    def __init__(self, config: AutoBenchConfig):
        super().__init__()

        self.config = config

        if not os.path.exists(self.config.data_path):
            os.mkdir(self.config.data_path)

        self.command_executor = CommandExecutorFactory.create(self.config.ssh)
        self.file_manager = MappedFileManager(
            self.config.data_path, self.command_executor
        )
        self.job_manager = JobManager(
            self.config, self.command_executor, self.file_manager
        )
        self.batch_job_manager = BatchJobManager(
            self.config.data_path, self.job_manager, self.file_manager
        )

    def check_server(self) -> AutoBenchMessage:
        command_result = self.command_executor.execute_command(
            'echo "Hello auto-bench!"'
        )
        if command_result.success:
            return AutoBenchMessage(
                msg=f"{command_result.out}\nServer is alive with Config:\n\n{self.config.to_str()}"
            )
        else:
            return AutoBenchMessage(
                msg=f"Something wrong in server:\n{command_result.out}"
            )

    def run(self, req: RunJobRequest) -> AutoBenchMessage:
        try:
            mapped_files: list[MappedFile] = self.file_manager.construct_file_map(
                [req.template_path, req.variable_path]
            )
            try:
                template: dict = yaml_utils.read_file(mapped_files[0].working_path)
                variable: dict = yaml_utils.read_file(mapped_files[1].working_path)
                deployment_template = DeploymentTemplate.of(**template)
                job_variable = JobVariable.of(**variable)
                self.job_manager.run_job(deployment_template, job_variable)
                return AutoBenchMessage(
                    msg=f"Successfully start job {job_variable.name}"
                )
            finally:
                self.file_manager.clear_file_map(mapped_files)
        except Exception as e:
            traceback.print_exc()
            return AutoBenchMessage(msg=f"Internal Server Error: {e}")

    def stop(self, req: StopJobRequest) -> AutoBenchMessage:
        try:
            self.job_manager.stop_job(req.job_name)
            return AutoBenchMessage(msg=f"Successfully stop job {req.job_name}")
        except Exception as e:
            traceback.print_exc()
            return AutoBenchMessage(msg=f"Internal Server Error: {e}")

    def remove(self, req: RemoveJobRequest) -> AutoBenchMessage:
        try:
            self.job_manager.remove_job(req.job_name)
            return AutoBenchMessage(msg=f"Successfully remove job {req.job_name}")
        except Exception as e:
            traceback.print_exc()
            return AutoBenchMessage(msg=f"Internal Server Error: {e}")

    def run_batch(self, req: RunBatchJobRequest) -> AutoBenchMessage:
        try:
            mapped_files: list[MappedFile] = self.file_manager.construct_file_map(
                [req.yml_path]
            )
            try:
                batch_yml: dict = yaml_utils.read_file(mapped_files[0].working_path)
                batch_config: BatchJobConfig = BatchJobConfig.of(**batch_yml)
                self.batch_job_manager.run_batch(batch_config)
                return AutoBenchMessage(
                    msg=f"Successfully start batch job {batch_config.name}"
                )
            finally:
                self.file_manager.clear_file_map(mapped_files)
        except Exception as e:
            traceback.print_exc()
            return AutoBenchMessage(msg=f"Internal Server Error: {e}")

    def stop_batch(self, req: StopBatchJobRequest) -> AutoBenchMessage:
        try:
            self.batch_job_manager.stop_batch(req.batch_name)
            return AutoBenchMessage(msg=f"Successfully stop batch job {req.batch_name}")
        except Exception as e:
            traceback.print_exc()
            return AutoBenchMessage(msg=f"Internal Server Error: {e}")

    def remove_batch(self, req: RemoveBatchJobRequest) -> AutoBenchMessage:
        try:
            self.batch_job_manager.remove_batch(req.batch_name)
            return AutoBenchMessage(
                msg=f"Successfully remove batch job {req.batch_name}"
            )
        except Exception as e:
            traceback.print_exc()
            return AutoBenchMessage(msg=f"Internal Server Error: {e}")


def run_server(auto_bench_cfg: str, server_cfg: str):
    auto_bench_cfg = AutoBenchConfig.load_from_file(auto_bench_cfg)
    server_cfg = FastServiceConfig.load_from_file(server_cfg)
    server = AutoBenchServer(auto_bench_cfg)
    setup_server(server_cfg, server)
