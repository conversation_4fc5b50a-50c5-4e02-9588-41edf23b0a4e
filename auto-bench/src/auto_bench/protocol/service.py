import logging

from typing import Optional

from fast_service import FastServiceManager, FastServiceConfig
from .declaration import (
    AutoBenchProtocol,
    AutoBenchMessage,
    RunJobRequest,
    StopJobRequest,
    RemoveJobRequest,
    RunBatchJobRequest,
    StopBatchJobRequest,
    RemoveBatchJobRequest,
)

SERVICE_MANAGER = FastServiceManager()

PROTOCOL_IMPL: Optional[AutoBenchProtocol] = None


def setup_client(config: FastServiceConfig):
    global SERVICE_MANAGER
    SERVICE_MANAGER.setup_client_mode(config)
    loggers = [logging.getLogger(name) for name in logging.root.manager.loggerDict]
    for logger in loggers:
        for handler in logger.handlers:
            logger.removeHandler(handler)


def setup_server(config: FastServiceConfig, protocol_impl: AutoBenchProtocol):
    global SERVICE_MANAGER, PROTOCOL_IMPL
    PROTOCOL_IMPL = protocol_impl
    SERVICE_MANAGER.execute(config)


@SERVICE_MANAGER.fast_service
def check_server() -> AutoBenchMessage:
    return PROTOCOL_IMPL.check_server()


@SERVICE_MANAGER.fast_service
def run(req: RunJobRequest) -> AutoBenchMessage:
    return PROTOCOL_IMPL.run(req)


@SERVICE_MANAGER.fast_service
def stop(req: StopJobRequest) -> AutoBenchMessage:
    return PROTOCOL_IMPL.stop(req)


@SERVICE_MANAGER.fast_service
def remove(req: RemoveJobRequest) -> AutoBenchMessage:
    return PROTOCOL_IMPL.remove(req)


@SERVICE_MANAGER.fast_service
def run_batch(req: RunBatchJobRequest) -> AutoBenchMessage:
    return PROTOCOL_IMPL.run_batch(req)


@SERVICE_MANAGER.fast_service
def stop_batch(req: StopBatchJobRequest) -> AutoBenchMessage:
    return PROTOCOL_IMPL.stop_batch(req)


@SERVICE_MANAGER.fast_service
def remove_batch(req: RemoveBatchJobRequest) -> AutoBenchMessage:
    return PROTOCOL_IMPL.remove_batch(req)
