from abc import ABCMeta, abstractmethod
from pydantic import BaseModel


class AutoBenchMessage(BaseModel):
    msg: str


class RunJobRequest(BaseModel):
    template_path: str
    variable_path: str


class StopJobRequest(BaseModel):
    job_name: str


class RemoveJobRequest(BaseModel):
    job_name: str


class RunBatchJobRequest(BaseModel):
    yml_path: str


class StopBatchJobRequest(BaseModel):
    batch_name: str


class RemoveBatchJobRequest(BaseModel):
    batch_name: str


class AutoBenchProtocol(metaclass=ABCMeta):

    @abstractmethod
    def check_server(self) -> AutoBenchMessage:
        pass

    @abstractmethod
    def run(self, req: RunJobRequest) -> AutoBenchMessage:
        pass

    @abstractmethod
    def stop(self, req: StopJobRequest) -> AutoBenchMessage:
        pass

    @abstractmethod
    def remove(self, req: RemoveJobRequest) -> AutoBenchMessage:
        pass

    @abstractmethod
    def run_batch(self, req: RunBatchJobRequest) -> AutoBenchMessage:
        pass

    @abstractmethod
    def stop_batch(self, req: StopBatchJobRequest) -> AutoBenchMessage:
        pass

    @abstractmethod
    def remove_batch(self, req: RemoveBatchJobRequest) -> AutoBenchMessage:
        pass
