import click

from fast_service import FastServiceConfig

from ..config import AutoBenchConfig
from ..protocol import (
    AutoBenchMessage,
    RunJobRequest,
    StopJobRequest,
    RemoveJobRequest,
    RunBatchJobRequest,
    StopBatchJobRequest,
    RemoveBatchJobRequest,
    setup_client,
)

from .. import protocol as remote_server

AUTO_BENCH_CONFIG: AutoBenchConfig


@click.group()
def cli():
    """A simple CLI tool for file operations."""
    pass


@cli.command()
def check_server():
    """Check auto-bench server status."""
    rsp: AutoBenchMessage = remote_server.check_server()
    click.echo(rsp.msg)


@cli.command()
@click.argument("template_path", type=click.Path())
@click.argument("variable_path", type=click.Path())
def run(template_path: str, variable_path: str):
    """Run single job with given template and variable file."""
    req: RunJobRequest = RunJobRequest(
        template_path=template_path,
        variable_path=variable_path,
    )
    rsp: AutoBenchMessage = remote_server.run(req=req)
    click.echo(rsp.msg)


@cli.command()
@click.argument("job_name", type=click.STRING)
def stop(job_name: str):
    """Stop single job with given job name."""
    rsp: AutoBenchMessage = remote_server.stop(StopJobRequest(job_name=job_name))
    click.echo(rsp.msg)


@cli.command()
@click.argument("job_name", type=click.STRING)
def remove(job_name: str):
    """Remove single job with given job name."""
    rsp: AutoBenchMessage = remote_server.remove(RemoveJobRequest(job_name=job_name))
    click.echo(rsp.msg)


@cli.command()
@click.argument("yml_path", type=click.Path())
def run_batch(yml_path: str):
    """Run batch job defined by given yml file."""
    req: RunBatchJobRequest = RunBatchJobRequest(yml_path=yml_path)
    rsp: AutoBenchMessage = remote_server.run_batch(req=req)
    click.echo(rsp.msg)


@cli.command()
@click.argument("batch_name", type=click.STRING)
def stop_batch(batch_name: str):
    """Stop single job with given job name."""
    rsp: AutoBenchMessage = remote_server.stop_batch(
        StopBatchJobRequest(batch_name=batch_name)
    )
    click.echo(rsp.msg)


@cli.command()
@click.argument("batch_name", type=click.STRING)
def remove_batch(batch_name: str):
    """Remove single job with given job name."""
    rsp: AutoBenchMessage = remote_server.remove_batch(
        RemoveBatchJobRequest(batch_name=batch_name)
    )
    click.echo(rsp.msg)


def run_client(auto_bench_cfg: str, client_cfg: str):
    global AUTO_BENCH_CONFIG
    AUTO_BENCH_CONFIG = AutoBenchConfig.load_from_file(auto_bench_cfg)
    client_cfg = FastServiceConfig.load_from_file(client_cfg)
    setup_client(client_cfg)
    cli()
