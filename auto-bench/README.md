# Auto-Bench-Tool

This is a one-stop tool for simplified docker, k8s and OpenFaaS deployment and job management.

Features include:
1. **Containerd execution**: this tool runs in a docker container, thus is easily portable.
2. **Unified and simplified interface**: this tool uses one set of simplified configs to specify how to deploy and run jobs on multiple engines.
3. **Batch job management**: this tool can automatically run batch jobs in sequence.
4. **Centralized job variable management**: this tool can automatically update numerous original configs distributed in different yml files as far as user defines them in given job config file.

## 1. Concepts

**job**: the management unit of auto-bench-tool. A job will be defined by user via two config files, deployment template yml and job variable yml. Auto-bench-tool will update the original configs and run the job on specified engine.

**batch job**: a batch of jobs, specified by one individual yml file, that will be run in sequence.

**deployment template**: a yml file that specifies the basic information of containers/services to be deployed and declares the customized job variables.

**job variable** a yml file that specifies the deployment engine, the resource and customized variables of a job, and its ending condition.

**original config** the original configs are those defined and used by the system or project to be deployed, and they are distributed in the original files of the project rather than any yml files required by auto-bench-tool.

## 2. Setup

### 2.1. Prepare fast-service locally

Download the source code of [fast-service](https://github.com/hkust-adsl/fast-service), and move them to ```.cache``` dir under the project dir of auto-bench. They will be used to build the image.

### 2.2. Build the image

In the project dir, run the scripts ```scripts/build-image.sh``` to build the image ```auto-bench:v1```.

The instruction could be:

```shell
bash scripts/build-image.sh
```

### 2.3. Run Auto-Bench-Tool

After built the image, run auto-bench-tool via:

```shell
bash scripts/start-auto-bench.sh
```

The tool will use the dir ```data``` under the project dir of auto-bench to store job info, so please keep the data dir clean before run auto-bench-tool for the first time.

## 3. Example

In this section, we will walk through several typical examples to see how to use auto-bench-tool.

### 3.1. Single job

**(1) Prepare the deployment template yml**

The deployment template yml is a docker-compose-like yml. You can define the services to be deployed with basic information, including image, ports, volumes, command, and depends_on.

Besides, the customized job variable should be declared in this file with variable name and the location of original config, either environment variable or configs in some yml file.

An example is [example/template.yml](example/template.yml). This file defines a system with one nginx server and one client to call the nginx server.

**(2) Prepare the job variable yml**

In a job variable yml, users should give the job a name and define the deployment engine or the backend, optionally docker, k8s or openfaas. Meanwhile, the resource of each service container and customized variables shall be defined as well. Besides, if you want a job ends automatically, the ends_on field shall be specified.

Typical examples are [example/docker-job.yml](example/docker-job.yml) and [example/k8s-job.yml](example/k8s-job.yml).

The difference between two jobs are job name, backend to be used, some customized variables and the ending condition.

**(3) Run job**

Run a job via:

```shell
bash scripts/auto-bench.sh <template yml> <variable yml>
```

For example, you can run a docker example via:

```shell
bash scripts/run-docker-example.sh
```

You can run a k8s example via:

```shell
bash scripts/run-k8s-example.sh
```

**(4) Check the job status**

For a docker job, you can check the job status via ```docker ps```.

For a k8s job, you can check the job status via ```kubectl get namespaces```. Each job will be run under a namespace. You can check the pods inside the namespace.

**(5) Stop job**

Stop a job via:

```shell
bash scripts/auto-bench.sh stop <job name>
```

The job name is what you defined in the job variable yml.

**(6) Remove job**

Remove a job via:

```shell
bash scripts/auto-bench.sh remove <job name>
```

All the information of the target job will be removed from the ```data``` dir.

### 3.2. Batch job

**(1) Prepare batch job yml**

To run a batch of jobs, you should list them in a batch job yml in order and specify a batch job name, the start index and the template yml and variable yml of each single job.

An typical example is [example/batch-job.yml](example/batch-job.yml).

**(2) Run batch job**

Run a batch job via:

```shell
bash scripts/auto-bench.sh run-batch <batch job yml>
```

For example, you can run the docker example job and k8s example job in a batch via:

```shell
bash scripts/run-batch-example.sh
```

**(3) Stop batch job**

Stop a batch job via:

```shell
bash scripts/auto-bench.sh stop-batch <batch job name>
```

**(4) Remove batch job**

Remove a batch job via:

```shell
bash scripts/auto-bench.sh remove-batch <batch job name>
```

All the information of the target batch job, including the generated information of each single job, will be removed from the ```data``` dir.

## 4. Stop and cleanup

To stop the tool, please run:

```shell
bash scripts/stop-auto-bench.sh
```

To clean up all the data generated by the tool, you can remove the dir ```data``` under the project dir of auto-bench. This operation may require root permission since this is a dir generated by docker container. You can execute the operation via ```sudo``` or ```docker exec``` in a container.