FROM python:3.12

# download and install fast-service
COPY .cache/fast-service /workspace/fast-service
RUN cd /workspace/fast-service && \
    pip install -r requirements.txt && \
    pip install -e .

# install dependencies for hugginggpt
WORKDIR /workspace/auto-bench
COPY requirements.txt requirements.txt
RUN pip install -r requirements.txt

# copy necessary code to docker image
COPY src src
COPY scripts scripts
COPY config config

# set workdir and default command
WORKDIR /workspace/auto-bench
CMD ["python", "src/run_server.py"]