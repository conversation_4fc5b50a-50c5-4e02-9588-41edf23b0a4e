version: "0.0.1"
name: k8s-example
backend: k8s
services:
  example-client:
    variables:
      nginx-port: 2080
      test-env: "This is an updated message from k8s example."

#  example-vllm:
#    resources:
#      cpu: 4
#      memory: 10G # 10M, 10G
#      gpu: 1

ends_on:
  # the job will be terminated iff all the conditions are met
  example-client: exit
  example-nginx: 1min # exit, 10s, 10min, 10h
