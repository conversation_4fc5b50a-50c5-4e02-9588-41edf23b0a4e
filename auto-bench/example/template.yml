version: '0.0.1'
services:
  example-nginx:
    image: nginx
    ports:
    - 80

  example-client:
    image: python:3.12
    environment:
      TEST_MESSAGE: "Start auto-bench example."
      UPDATED_MESSAGE: "This is the raw message."
    volumes:
      - "/home/<USER>/ai-agent-benchmark/auto-bench/data/:/workspace/"
      - "/home/<USER>/ai-agent-benchmark/auto-bench/example/example-config.yml:/workspace/example/example-config.yml"
#    command: >
#      sh -c
#      "curl http://example-nginx:80 >> /workspace/out.txt;
#      curl http://example-vllm:8000/v1/models >> /workspace/out.txt"
    command: >
      sh -c
      "cat /workspace/example/example-config.yml >> /workspace/out.txt;
      echo $$UPDATED_MESSAGE >> /workspace/out.txt;
      curl http://example-nginx:80 >> /workspace/out.txt"
    variables:
      nginx-port:
        file_path: /home/<USER>/ai-agent-benchmark/auto-bench/example/example-config.yml
        flatten_key: target/1/nginx/port
      test-env: UPDATED_MESSAGE
    depends_on:
      example-nginx: http://example-nginx:80
#      example-vllm: http://example-vllm:8000/v1/models

#  example-vllm:
#    image: vllm/vllm-openai:v0.6.4
#    ports:
#    - 8000
#    environment:
#      HUGGING_FACE_HUB_TOKEN: *************************************
#      NVIDIA_VISIBLE_DEVICES: 7
#    args: ["--model", "Qwen/Qwen2.5-1.5B-Instruct"]
#    volumes:
#      - "/mnt/data/huggingface:/root/.cache/huggingface"
