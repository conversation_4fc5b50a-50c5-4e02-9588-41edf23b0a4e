# Monitoring Setup


## Launch the Monitoring Stack
First, launch the monitoring stack in this directory:
``` bash
docker-compose up -d
```

You can update the configuration of prometheus and others in the `config` directory.

## Access Grafana
Open your web browser and go to http://<your-server-ip>:3000.

Log in with the default credentials:

Username: admin

Password: admin

Do not Change the password when prompted.

## Configure Grafana Data Sources
Go to Configuration > Data Sources.

Click Add data source.

Select Prometheus.

Set the URL to http://prometheus:9090.

Click Save & Test.

## Import Grafana Dashboards
Go to Dashboards > Manage.

Click Import.

Import the following dashboards:

cAdvisor Dashboard: Use ID 14282.

NVIDIA DCGM Dashboard: Use ID 12239.

## Monitor Your Server
You should now have a comprehensive monitoring setup with:

cAdvisor: Monitoring container and host resource usage.

DCGM-Exporter: Monitoring GPU metrics.

Prometheus: Collecting metrics from cAdvisor and DCGM-Exporter.

Grafana: Visualizing the metrics.