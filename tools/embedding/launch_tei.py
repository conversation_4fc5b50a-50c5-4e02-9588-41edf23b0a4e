import os
import argparse


def get_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("-p", "--port", default=18080)
    parser.add_argument("-m", "--model", default="Alibaba-NLP/gte-Qwen2-1.5B-instruct")
    parser.add_argument("-hf", "--hf_home", default="/mnt/data/huggingface")
    parser.add_argument("-t", "--hf_token", default="EMPTY")
    parser.add_argument("-n", "--name", required=True)
    parser.add_argument("-d", "--device", default=0)
    parser.add_argument("-v", "--version", default="1.5")
    parser.add_argument("-mb", "--max_batch_tokens", default=16384)
    parser.add_argument("-mcb", "--max_client_batch_size", default=2560)
    parser.add_argument("-l", "--log", default=None)

    args = parser.parse_args()
    return args


if __name__ == "__main__":
    args = get_args()
    cmd = f"""docker ps | grep ghcr.io/huggingface/text-embeddings-inference | grep {args.name} | wc -l"""
    res = int(os.popen(cmd).read())
    if res == 1:
        print(f"{args.name} is running")
    else:
        cmd = f"""
            docker run -d \
            --name {args.name} \
            --runtime nvidia --gpus device={args.device} \
            -v {args.hf_home}/hub:/data \
            -p {args.port}:80 \
            --pull always ghcr.io/huggingface/text-embeddings-inference:{args.version} \
            --model-id {args.model} \
            --hf-api-token {args.hf_token} \
            --max-batch-tokens {args.max_batch_tokens} \
            --max-client-batch-size {args.max_client_batch_size}
            """
        os.system(cmd)

        if args.log is not None:
            cmd = f"docker logs -f {args.name} > {args.log} 2>&1 &"
            os.system(cmd)
