import os
import argparse


def get_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("-p", "--port", default=17070)
    parser.add_argument("-m", "--model", default="nvidia/NV-Embed-v2")
    parser.add_argument("-hf", "--hf_home", default="/mnt/data/huggingface")
    parser.add_argument("-n", "--name", required=True)
    parser.add_argument("-d", "--device", default=0)
    parser.add_argument("-v", "--version", default="latest")
    parser.add_argument("-l", "--log", default=None)

    args = parser.parse_args()
    return args


if __name__ == "__main__":
    args = get_args()
    cmd = f"""docker ps | grep michaelf34/infinity | grep {args.name} | wc -l"""
    res = int(os.popen(cmd).read())
    if res == 1:
        print(f"{args.name} is running")
    else:
        cmd = f"""
            docker run -d \
            --name {args.name} \
            --runtime nvidia --gpus device={args.device} \
            -v {args.hf_home}:/app/.cache/huggingface \
            -p {args.port}:7070 \
            michaelf34/infinity:{args.version} \
            v2 \
            --model-id {args.model} \
            --port 7070
            """
        os.system(cmd)

        if args.log is not None:
            cmd = f"docker logs -f {args.name} > {args.log} 2>&1 &"
            os.system(cmd)
