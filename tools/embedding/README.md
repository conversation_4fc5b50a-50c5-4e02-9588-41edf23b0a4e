Launch Emebdding service with text-embeddings-inference or infinity.

## Launch text-embeddings-inference

You can launch and embedding model (e.g. gte-Qwen2-1.5B-instruct) with text-embeddings-inference by running the following command:
```bash
python launch_tei.py \
    -n [container name] \
    -m [model you want to use] \
    -p [port of the host to be used] \
    -d [the gpu device id selected for running text-embeddings-inference, default is 0] \
    -v [the version of text-embeddings-inference to be used, default is 1.5] \
    -t [the huggingface access token] \
    -hf [the local huggingface_home, default will be "/mnt/data/huggingface"] \
    -mb [the max batch tokens, default is 16384] \
    -mcb [the max client batch size, default is 2560]
```

An example will be like:
```bash
python launch_tei.py -n test-tei -m Alibaba-NLP/gte-Qwen2-1.5B-instruct -t [xxxxx]
```

A docker container named  ```test-tei``` will be started on default port 18080 with model ```Alibaba-NLP/gte-Qwen2-1.5B-instruct```. The default text-embeddings-inference version is ```1.5```.


## Launch infinity

You can launch and embedding model (e.g. NV-Retriever-v1) with infinity by running the following command:
```bash
python launch_infinity.py \
    -n [container name] \
    -m [model you want to use] \
    -p [port of the host to be used] \
    -d [the gpu device id selected for running infinity, default is 0] \
    -v [the version of infinity to be used, default is latest] \
    -hf [the local huggingface_home, default will be "/mnt/data/huggingface"]
```

An example will be like:
```bash
python launch_infinity.py -n test-infinity -m nvidia/NV-Embed-v2 -t [xxxxx]
```

A docker container named  ```test-infinity``` will be started on default port 17070 with model ```nvidia/NV-Embed-v2```. The default infinity version is ```latest```.

