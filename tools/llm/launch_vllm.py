import os
import argparse


def get_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("-p", "--port", default=8002)
    parser.add_argument("-m", "--model", default="Qwen/Qwen2.5-7B-Instruct")
    parser.add_argument("-hf", "--hf_home", default="/mnt/data/huggingface")
    parser.add_argument("-s", "--hf_secret", required=True)
    parser.add_argument("-n", "--name", required=True)
    parser.add_argument("-d", "--device", default=0)
    parser.add_argument("-v", "--version", default="v0.6.4")

    args = parser.parse_args()
    return args


if __name__ == "__main__":
    args = get_args()
    cmd = f"""docker ps|grep vllm/vllm-openai|grep {args.name}|wc -l"""
    res = int(os.popen(cmd).read())
    if res == 1:
        print(f"{args.name} is running")
    else:
        cmd = f"""
            docker run -d \
            --name {args.name} \
            --runtime nvidia --gpus device={args.device} \
            -v {args.hf_home}:/root/.cache/huggingface \
            --env "HUGGING_FACE_HUB_TOKEN={args.hf_secret}" \
            -p {args.port}:8000 \
            --ipc=host \
            vllm/vllm-openai:{args.version} \
            --model {args.model}
            """
        os.system(cmd)
