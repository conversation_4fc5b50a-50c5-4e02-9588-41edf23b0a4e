# LLM Deployment

## Deployment

Currently, we use docker and vllm to deploy LLM models.

Here's the command template to run [launch_vllm.py](launch_vllm.py).

```shell
python launch_vllm.py \
    -n [container name] \
    -s [your huggingface access token] \
    -hf [your local huggingface_home, default will be "~/.cache/huggingface"] \
    -m [model you want to use, if not exits in hf_home, will be download from hf] \
    -p [port of the host to be used] \
    -d [the gpu device id selected for running vllm, default is 0] \
    -v [the version of vllm to be used, default is 0.6.4]
```

An example will be like:

```shell
python launch_vllm.py -n test-vllm -s [xxxxx]
```

A docker container named  ```test-vllm``` will be started on default port 8000 with default model ```google/gemma-2-2b-it```. The default vllm version is ```v0.6.3.post1```.

If there wasn't ```google/gemma-2-2b-it``` in your local huggingface home dir (by default ```~/.cache/huggingface```), vllm will download it from huggingface. If you are using the 4090D server in our lab, please change the huggingface home dir with `-hf /mnt/data/huggingface` in order to save space of `/home` directory. 

It will take some time to download the model. You can download the model manually via huggingface-cli in advance.

To test vllm and invoke LLM, please refer to [vllm-quickstart](https://docs.vllm.ai/en/latest/getting_started/quickstart.html#openai-compatible-server). We suggest the OpenAI style usage.


## Monitor

If you follow [monitor doc](https://github.com/hkust-adsl/fast-service/blob/master/docs/Monitor.md), you could monitor the overall status of vllm docker container.

For detailed internal status of vllm, you have to import vllm's [grafana.json](https://github.com/vllm-project/vllm/blob/main/examples/production_monitoring/grafana.json) to your grafana and select the model you are using, e.g. Qwen/Qwen2.5-14B-Instruct, which you can retrieve via the following instruction:

```shell
curl http://localhost:8000/v1/models
```