#!/usr/bin/env python3
"""
Test script to verify that async chunk situating uses the correct template.
"""

import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_chunk_situating_template_usage():
    """Test that async chunk situating uses the contextual_retrieval template."""
    print("🔍 Testing Async Chunk Situating Template Usage")
    print("=" * 50)
    
    try:
        # Check that the template exists
        from rag.prompt_templates import ALL_DEFAULT_TEMPLATES
        
        if "contextual_retrieval" not in ALL_DEFAULT_TEMPLATES:
            print("❌ contextual_retrieval template not found in ALL_DEFAULT_TEMPLATES")
            return False
        
        template = ALL_DEFAULT_TEMPLATES["contextual_retrieval"]
        print("✅ contextual_retrieval template found")
        
        # Check that template has correct placeholders
        if "{doc_content}" not in template:
            print("❌ contextual_retrieval template missing {doc_content} placeholder")
            return False
        
        if "{chunk_content}" not in template:
            print("❌ contextual_retrieval template missing {chunk_content} placeholder")
            return False
        
        print("✅ contextual_retrieval template has correct placeholders")
        
        # Check that async chunk situating can be imported
        from rag.async_chunk_situating import AdvancedAsyncChunkSituatingEngine
        print("✅ AdvancedAsyncChunkSituatingEngine imported successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def test_sync_vs_async_template_consistency():
    """Test that sync and async versions use the same template."""
    print("\n🔍 Testing Sync vs Async Template Consistency")
    print("=" * 50)
    
    try:
        # Check sync version
        with open('src/rag/chunk_situating.py', 'r') as f:
            sync_content = f.read()
        
        if 'ALL_DEFAULT_TEMPLATES["contextual_retrieval"]' not in sync_content:
            print("❌ Sync version doesn't use contextual_retrieval template")
            return False
        
        print("✅ Sync version uses contextual_retrieval template")
        
        # Check async version
        with open('src/rag/async_chunk_situating.py', 'r') as f:
            async_content = f.read()
        
        if 'template_name="contextual_retrieval"' not in async_content:
            print("❌ Async version doesn't use contextual_retrieval template")
            return False
        
        print("✅ Async version uses contextual_retrieval template")
        
        # Check that async version doesn't use hardcoded prompts
        if 'template_name="direct_generation"' in async_content:
            print("⚠️  Async version still has some direct_generation usage")
            # This might be okay if it's in other functions, let's check the specific function
            lines = async_content.split('\n')
            in_llm_based_situating = False
            for line in lines:
                if 'def llm_based_situating(' in line:
                    in_llm_based_situating = True
                elif 'def ' in line and in_llm_based_situating:
                    in_llm_based_situating = False
                elif in_llm_based_situating and 'template_name="direct_generation"' in line:
                    print("❌ llm_based_situating still uses direct_generation template")
                    return False
        
        print("✅ Async version doesn't use hardcoded prompts in llm_based_situating")
        
        # Check template variables
        if '"doc_content": source_doc.page_content' not in async_content:
            print("❌ Async version missing correct doc_content variable")
            return False
        
        if '"chunk_content": chunk.page_content' not in async_content:
            print("❌ Async version missing correct chunk_content variable")
            return False
        
        print("✅ Async version uses correct template variables")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def test_enhanced_content_format():
    """Test that async version creates enhanced content in same format as sync."""
    print("\n🔍 Testing Enhanced Content Format")
    print("=" * 50)
    
    try:
        # Check sync version format
        with open('src/rag/chunk_situating.py', 'r') as f:
            sync_content = f.read()
        
        # Sync version uses: f"{chunk.page_content}\n\n{llm_output.content}"
        if 'f"{chunk.page_content}\\n\\n{llm_output.content}"' not in sync_content:
            print("⚠️  Sync version format might have changed")
        
        print("✅ Sync version format identified")
        
        # Check async version format
        with open('src/rag/async_chunk_situating.py', 'r') as f:
            async_content = f.read()
        
        # Async version should use: f"{chunk.page_content}\n\n{contextual_info}"
        if 'f"{chunk.page_content}\\n\\n{contextual_info}"' not in async_content:
            print("❌ Async version doesn't use correct enhanced content format")
            return False
        
        print("✅ Async version uses correct enhanced content format")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🧪 Chunk Situating Template Fix Verification")
    print("=" * 50)
    
    results = []
    
    # Test template usage
    results.append(test_chunk_situating_template_usage())
    
    # Test sync vs async consistency
    results.append(test_sync_vs_async_template_consistency())
    
    # Test enhanced content format
    results.append(test_enhanced_content_format())
    
    # Summary
    print("\n" + "=" * 50)
    print("🧪 Chunk Situating Fix Summary")
    print("=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"✅ All {total} tests passed!")
        print("🎉 Async chunk situating now uses correct template!")
        print("\n📋 Summary of fixes:")
        print("  ✅ Uses contextual_retrieval template (not direct_generation)")
        print("  ✅ Uses correct template variables (doc_content, chunk_content)")
        print("  ✅ Creates enhanced content in same format as sync version")
        print("  ✅ Complete prompt parity with sync version achieved")
    else:
        print(f"❌ {total - passed} out of {total} tests failed!")
        print("⚠️  Additional fixes may be needed!")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
