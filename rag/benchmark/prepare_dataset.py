import os, sys
import argparse
import pandas as pd
from datasets import load_dataset


def get_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("-d", "--dataset", default="wiki_qa")
    parser.add_argument("-o", "--out_dir", default="./.cache")
    parser.add_argument("-n", "--nrows", type=int, default=0)
    parser.add_argument("--seed", type=int, default=None)

    args = parser.parse_args()
    return args


def prepare_wiki_qa(args: argparse.Namespace):
    ds = load_dataset("microsoft/wiki_qa")
    print(ds["train"].column_names)

    selected_cols = ["question_id", "question", "document_title"]
    train_df = ds["train"].select_columns(selected_cols).to_pandas().drop_duplicates()
    valid_df = (
        ds["validation"].select_columns(selected_cols).to_pandas().drop_duplicates()
    )
    test_df = ds["test"].select_columns(selected_cols).to_pandas().drop_duplicates()

    print("size of three parts: ", len(train_df), len(valid_df), len(test_df))
    df = pd.concat([train_df, valid_df, test_df]).drop_duplicates()
    df.rename(
        columns={
            "question_id": "qid",
            "question": "query",
            "document_title": "collection_name",
        },
        inplace=True,
    )

    os.makedirs(args.out_dir, exist_ok=True)
    if args.nrows > 0:
        df = df.sample(n=args.nrows, random_state=args.seed)
    save_path = os.path.join(args.out_dir, args.dataset + ".txt")
    df.to_csv(save_path, index=False, header=False, sep="\t")


def prepare_ms_marco(args):
    ds = load_dataset("microsoft/ms_marco", "v2.1")
    print(ds["train"].column_names)
    selected_cols = ["query_id", "query", "query_type", "passages"]

    train_df: pd.DataFrame = ds["train"].select_columns(selected_cols).to_pandas()
    valid_df: pd.DataFrame = ds["validation"].select_columns(selected_cols).to_pandas()
    test_df: pd.DataFrame = ds["test"].select_columns(selected_cols).to_pandas()

    print("size of three parts: ", len(train_df), len(valid_df), len(test_df))
    df = pd.concat([train_df, valid_df, test_df])
    df["urls"] = df["passages"].apply(lambda x: x["url"].tolist())
    df.drop(columns=["passages"], inplace=True)
    df = df.drop_duplicates(subset=["query_id", "query", "query_type"])

    os.makedirs(args.out_dir, exist_ok=True)
    if args.nrows > 0:
        df = df.sample(n=args.nrows, random_state=args.seed)
    save_path = os.path.join(args.out_dir, args.dataset + ".txt")
    df.to_csv(save_path, index=False, header=False, sep="\t")


def prepare_natural_questions(args):
    ds = load_dataset("natural_questions", "default")
    print(ds["train"].column_names)
    selected_cols = ["id", "question", "document"]
    train_df: pd.DataFrame = ds["train"].select_columns(selected_cols).to_pandas()
    valid_df: pd.DataFrame = ds["validation"].select_columns(selected_cols).to_pandas()

    print("size of three parts: ", len(train_df), len(valid_df))
    df = pd.concat([train_df, valid_df])
    df["question"] = df["question"].apply(lambda x: x["text"])
    df["url"] = df["document"].apply(lambda x: x["url"])
    df["title"] = df["document"].apply(lambda x: x["title"])
    html_dir = os.path.join(args.out_dir, "NQ_htmls")
    os.makedirs(html_dir, exist_ok=True)
    paths = []
    for row in df.itertuples():
        qid = row.id
        html = row.document["html"]
        # save html to file
        file_path = os.path.join(args.out_dir, "NQ_htmls", f"{qid}.html")
        with open(file_path, "w") as f:
            f.write(html)
        paths.append(file_path)
    df["html_path"] = paths
    df.drop(columns=["document"], inplace=True)

    os.makedirs(args.out_dir, exist_ok=True)
    if args.nrows > 0:
        df = df.sample(n=args.nrows, random_state=args.seed)
    save_path = os.path.join(args.out_dir, args.dataset + ".txt")
    df.to_csv(save_path, index=False, header=False, sep="\t")


def prepare_dataset(args):
    if args.dataset == "wiki_qa":
        prepare_wiki_qa(args)
    elif args.dataset == "ms_marco":
        prepare_ms_marco(args)
    elif args.dataset == "natural_questions":
        prepare_natural_questions(args)
    else:
        raise ValueError(f"Dataset {args.dataset} is not supported.")


if __name__ == "__main__":
    args = get_args()
    prepare_dataset(args)

# %%
