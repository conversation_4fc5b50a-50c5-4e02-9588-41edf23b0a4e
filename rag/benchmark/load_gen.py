import os, time
import sys
import argparse
import logging

from fast_service import (
    FastServiceConfig,
    FastServiceBenchmark,
    FastServiceBenchmarkConfig,
    FastServiceModuleBenchmarkConfig,
    FastServiceModuleBenchmark,
)
from fast_service.config import _read_yaml


abs_path = os.path.abspath("../src/rag")

module_dir = os.path.dirname(abs_path)
if module_dir not in sys.path:
    sys.path.append(module_dir)

from rag import rag_fsm, init_settings
from rag import RAGE2EInput, RAGE2EOutput
from rag import naive_rag
from rag import advanced_rag
from rag import dynamic_rag
from rag import contextual_rag
from rag.settings import (
    SplitterSettings,
    VDBSettings,
    EmbeddingSettings,
    RetrieverSettings,
    GenerationSettings,
)
from rag.settings import get_settings
import multiprocessing
from rag.async_rag_e2e import naive_rag_async, multiquery_rag_async, dynamic_rag_async, contextual_rag_async


def str2dict(arg):
    try:
        pairs = arg.split(",")
        result = {}
        for pair in pairs:
            key, value = pair.split(":")
            result[key.strip()] = value.strip()
        return result
    except ValueError:
        raise argparse.ArgumentTypeError(
            f"Invalid dict format: {arg}. Expected 'key:value,key2:value2'"
        )


def get_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("-r", "--rag", default="naive")
    parser.add_argument("-d", "--dataset", default="natural_questions")
    parser.add_argument("--data_dir", default="./.cache")
    parser.add_argument("-f", "--file_path", default=None)
    parser.add_argument("-c", "--config", default="../config/rag-client.yml")
    parser.add_argument("-s", "--settings", default="../config/settings.yml")
    parser.add_argument("-m", "--mode", default="one-by-one")
    parser.add_argument("-n", "--request_num", default=20, type=int)
    parser.add_argument("-w", "--warm_up", default=0, type=int)
    parser.add_argument("-wt", "--warm_up_time", default=0.0, type=float)
    parser.add_argument("-b", "--batch_size", default=1, type=int)
    parser.add_argument("-l", "--lambda_rate", default=1.0, type=float)
    parser.add_argument("-o", "--output_file", type=str, default=None)

    parser.add_argument("--module", type=str, default=None)
    parser.add_argument("--function", type=str, default=None)
    parser.add_argument("--intermediate_storage", type=str, default=None)
    parser.add_argument("--request_version", type=str, default=None)
    parser.add_argument("--bench_concurrency", type=str, default="thread")
    parser.add_argument(
        "--sampling_params",
        type=str2dict,
        help='A dictionary in the format "key:value,key2:value2"',
        default=None,
    )

    parser.add_argument("--log", type=str, default=None)
    args = parser.parse_args()

    if args.file_path is None:
        args.file_path = os.path.join(args.data_dir, f"{args.dataset}.txt")

    if args.log is not None:
        logging.basicConfig(
            filename=args.log,
            level=logging.INFO,
            format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        )
    else:
        logging.basicConfig(
            level=logging.INFO,
            format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        )

    return args


def get_all_settings(args):
    splitter_settings = SplitterSettings.from_default_settings()
    vdb_settings = VDBSettings.from_default_settings()
    embedding_settings = EmbeddingSettings.from_default_settings()
    retriever_settings = RetrieverSettings.from_default_settings()
    generation_settings = GenerationSettings.from_default_settings()

    return (
        splitter_settings,
        vdb_settings,
        embedding_settings,
        retriever_settings,
        generation_settings,
    )


def wiki_qa_parser(line: str):
    (
        splitter_settings,
        vdb_settings,
        embedding_settings,
        retriever_settings,
        generation_settings,
    ) = get_all_settings(args)
    qid, question, wiki_query = line.strip().split("\t")
    vdb_settings.collection_name = f"query_{qid}"
    return RAGE2EInput(
        query=question,
        uri=wiki_query,
        type="wiki",
        splitter_settings=splitter_settings,
        vdb_settings=vdb_settings,
        embedding_settings=embedding_settings,
        retriever_settings=retriever_settings,
        generation_settings=generation_settings,
    )


def ms_marco_parser(line: str):
    (
        splitter_settings,
        vdb_settings,
        embedding_settings,
        retriever_settings,
        generation_settings,
    ) = get_all_settings(args)
    query_id, query, query_type, urls_str = line.strip().split("\t")
    urls = eval(urls_str)
    vdb_settings.collection_name = f"query_{query_id}"
    return RAGE2EInput(
        query=query,
        uri=urls,
        type="web",
        splitter_settings=splitter_settings,
        vdb_settings=vdb_settings,
        embedding_settings=embedding_settings,
        retriever_settings=retriever_settings,
        generation_settings=generation_settings,
    )


def natural_question_parser(line: str):
    (
        splitter_settings,
        vdb_settings,
        embedding_settings,
        retriever_settings,
        generation_settings,
    ) = get_all_settings(args)
    query_id, query, url, title, path = line.strip().split("\t")
    # query_id is int, we want it to be non-negative
    vdb_settings.collection_name = f"query_{query_id}".replace("-", "_")
    return RAGE2EInput(
        query=query,
        uri=path,
        type="local_html",
        splitter_settings=splitter_settings,
        vdb_settings=vdb_settings,
        embedding_settings=embedding_settings,
        retriever_settings=retriever_settings,
        generation_settings=generation_settings,
    )


def get_benchmark(args):
    if args.dataset == "wiki_qa":
        line_to_req = wiki_qa_parser
    elif args.dataset == "ms_marco":
        line_to_req = ms_marco_parser
    elif args.dataset == "natural_questions":
        line_to_req = natural_question_parser
    else:
        raise ValueError(f"unknown dataset: {args.dataset}")

    def invoke_naive_rag(request):
        return naive_rag(request)

    def invoke_advanced_rag(request):
        return advanced_rag(request)

    def invoke_dynamic_rag(request):
        return dynamic_rag(request)

    def invoke_contextual_rag(request):
        return contextual_rag(request)

    async def invoke_naive_rag_async(request):
        return await naive_rag_async(request)

    async def invoke_multiquery_rag_async(request):
        return await multiquery_rag_async(request)

    async def invoke_dynamic_rag_async(request):
        return await dynamic_rag_async(request)

    async def invoke_contextual_rag_async(request):
        return await contextual_rag_async(request)

    if args.rag == "naive":
        invoke_service = invoke_naive_rag
    elif args.rag == "advanced":
        invoke_service = invoke_advanced_rag
    elif args.rag == "dynamic":
        invoke_service = invoke_dynamic_rag
    elif args.rag == "contextual":
        invoke_service = invoke_contextual_rag
    elif args.rag == "naive_async":
        invoke_service = invoke_naive_rag_async
    elif args.rag == "multiquery_async" or args.rag == "advanced_async":
        invoke_service = invoke_multiquery_rag_async
    elif args.rag == "dynamic_async":
        invoke_service = invoke_dynamic_rag_async
    elif args.rag == "contextual_async":
        invoke_service = invoke_contextual_rag_async
    else:
        raise ValueError(f"unknown rag: {args.rag}")

    bm_config = FastServiceBenchmarkConfig(
        file_path=args.file_path,
        mode=args.mode,
        request_num=args.request_num,
        buffer_size=1000,
        lambda_rate=args.lambda_rate,
        output_file=args.output_file,
        warm_up=args.warm_up,
        warm_up_time=args.warm_up_time,
        batch_size=args.batch_size,
        concurrency=args.bench_concurrency,
        sampling_params=args.sampling_params,
    )
    return FastServiceBenchmark(
        config=bm_config, line_to_request=line_to_req, invoke_service=invoke_service
    )


def get_module_benchmark(args):
    bm_config = FastServiceBenchmarkConfig(
        file_path=args.file_path,
        mode=args.mode,
        request_num=args.request_num,
        buffer_size=1000,
        lambda_rate=args.lambda_rate,
        output_file=args.output_file,
        warm_up=args.warm_up,
        warm_up_time=args.warm_up_time,
        batch_size=args.batch_size,
        concurrency=args.bench_concurrency,
        sampling_params=args.sampling_params,
    )
    dir_path = os.path.join(
        args.intermediate_storage, "requests", f"{args.module}.{args.function}"
    )
    module_config = FastServiceModuleBenchmarkConfig(
        module_name=args.module,
        function_name=args.function,
        dir_path=dir_path,
        base_config=bm_config,
        request_version=args.request_version,
    )
    module_benchmark = FastServiceModuleBenchmark(
        config=module_config, fast_service_manager=rag_fsm
    )
    return module_benchmark


def launch_loadgen(config_path: str, pid: int, nworkers: int = 1):
    args = get_args()
    if args.mode not in ["one-by-one", "batch-by-batch"]:
        if nworkers > 1:
            if args.request_num > 0:
                while args.request_num % nworkers != 0:
                    args.request_num += 1
            args.request_num = args.request_num // nworkers
            args.lambda_rate = args.lambda_rate / nworkers
            args.sampling_params = {
                "method": "uniform",
                "step": nworkers,
                "offset": pid,
            }
            original_warm_up = args.warm_up
            args.warm_up = len(range(pid, original_warm_up, nworkers))
            print(f"Adjusted warm up requests to {args.warm_up} for worker-{pid}.")

    args.output_file = f"{args.output_file}_{pid}"
    config_dict = _read_yaml(config_path)
    if pid is not None:
        get_settings().log_file = f"{get_settings().log_file}_{pid}"
        config_dict["monitor_storage"][
            "store_dir"
        ] = f"{config_dict['monitor_storage']['store_dir']}_{pid}"
        for service in config_dict["service_list"]:
            service["port"] += pid + 1
    config = FastServiceConfig.load_from_dict(config_dict)
    rag_fsm.setup_client_mode(config)

    if args.module is not None:
        assert args.function is not None
        assert args.intermediate_storage is not None
        benchmark = get_module_benchmark(args)
    else:
        benchmark = get_benchmark(args)

    logging.info("start benchmark")
    benchmark.execute()
    logging.info("end benchmark")

    # write a file SUCCESS to indicate the benchmark is done
    file_path = os.path.join(os.path.dirname(get_settings().log_file), "SUCCESS")
    with open(file_path, "a+") as f:
        f.write(f"{pid}/{nworkers} SUCCESS\n")


if __name__ == "__main__":
    args = get_args()

    init_settings(os.path.abspath(args.settings))

    uvi_nworkers = get_settings().uvicorn_nworkers
    if uvi_nworkers is None or uvi_nworkers <= 1:
        config = FastServiceConfig.load_from_file(os.path.abspath(args.config))
        rag_fsm.setup_client_mode(config)

        if args.module is not None:
            assert args.function is not None
            assert args.intermediate_storage is not None
            benchmark = get_module_benchmark(args)
        else:
            benchmark = get_benchmark(args)

        logging.info("start benchmark")
        benchmark.execute()
        logging.info("end benchmark")
        # write a file SUCCESS to indicate the benchmark is done
        file_path = os.path.join(os.path.dirname(get_settings().log_file), "SUCCESS")
        with open(file_path, "a+") as f:
            f.write("SUCCESS\n")
    else:
        processes = []
        for i in range(uvi_nworkers):
            p = multiprocessing.Process(
                target=launch_loadgen,
                args=(os.path.abspath(args.config), i, uvi_nworkers),
            )
            processes.append(p)
            p.start()

        for p in processes:
            p.join()
