"""
This file is used to benchmark the tokenizer. It is not used in the main code.
"""

import time, os
import numpy as np
import pandas as pd
import seaborn as sns
import matplotlib.pyplot as plt

# from vllm import VLLM
from transformers import AutoTokenizer
import argparse
import nltk
from nltk.corpus import words

nltk.download("words")


def benchmark_encode(model: str, input_texts: list) -> dict:
    # Initialize the tokenizer
    start_time = time.time()
    tokenizer = AutoTokenizer.from_pretrained(model)
    loading_time = time.time() - start_time

    # Benchmarking: Measure the time taken for tokenization
    token_counts = []
    start_time = time.time()
    for text in input_texts:
        # Tokenize the text and count tokens
        tokens = tokenizer.encode(text)
        token_counts.append(len(tokens))
    elapsed_time = time.time() - start_time

    start_time = time.time()
    all_tokens = tokenizer(input_texts)
    batch_elapsed_time = time.time() - start_time

    # Compute average token count and throughput
    average_token_count = sum(token_counts) / len(token_counts)
    throughput = len(input_texts) / elapsed_time  # Requests per second
    batch_throughput = len(input_texts) / batch_elapsed_time  # Requests per second

    print(f"Tokenizer Loading Time: {loading_time:.2f} seconds")
    print(f"Average Token Count: {average_token_count}")
    print(f"Total Tokenization Time: {elapsed_time:.2f} seconds")
    print(f"Throughput (tokenization per second): {throughput:.2f}")
    print(f"Batch Tokenization Time: {batch_elapsed_time:.2f} seconds")
    print(f"Batch Throughput (tokenization per second): {batch_throughput:.2f}")
    print(
        f"Token Throughput (tokenization per second): {throughput * average_token_count:.2f}"
    )
    print(
        f"Batch Token Throughput (tokenization per second): {batch_throughput * average_token_count}"
    )

    return {
        "loading_time": loading_time,
        "average_token_count": average_token_count,
        "elapsed_time": elapsed_time,
        "throughput": throughput,
        "batch_elapsed_time": batch_elapsed_time,
        "batch_throughput": batch_throughput,
        "token_throughput": throughput * average_token_count,
        "batch_token_throughput": batch_throughput * average_token_count,
    }


def benchmark_decode(model: str, input_tokens: list) -> dict:
    # Initialize the tokenizer
    start_time = time.time()
    tokenizer = AutoTokenizer.from_pretrained(model)
    loading_time = time.time() - start_time

    # Benchmarking: Measure the time taken for tokenization
    token_counts = []
    start_time = time.time()
    for token_ids in input_tokens:
        text = tokenizer.decode(token_ids)
        token_counts.append(len(token_ids))
    elapsed_time = time.time() - start_time

    flattened_tokens = [token for tokens in input_tokens for token in tokens]
    start_time = time.time()
    texts = tokenizer.batch_decode(flattened_tokens)
    batch_elapsed_time = time.time() - start_time

    # Compute average token count and throughput
    average_token_count = sum(token_counts) / len(token_counts)
    throughput = len(input_tokens) / elapsed_time  # Requests per second
    batch_throughput = len(input_tokens) / batch_elapsed_time  # Requests per second

    print(f"Tokenizer Loading Time: {loading_time:.2f} seconds")
    print(f"Average Token Count: {average_token_count}")
    print(f"Total Decoding Time: {elapsed_time:.2f} seconds")
    print(f"Throughput (Decoding per second): {throughput:.2f}")
    print(f"Batch Decoding Time: {batch_elapsed_time:.2f} seconds")
    print(f"Batch Throughput (Decoding per second): {batch_throughput:.2f}")
    print(
        f"Token Throughput (Decoding per second): {throughput * average_token_count:.2f}"
    )
    print(
        f"Batch Token Throughput (Decoding per second): {batch_throughput * average_token_count
        }"
    )

    return {
        "loading_time": loading_time,
        "average_token_count": average_token_count,
        "elapsed_time": elapsed_time,
        "throughput": throughput,
        "batch_elapsed_time": batch_elapsed_time,
        "batch_throughput": batch_throughput,
        "token_throughput": throughput * average_token_count,
        "batch_token_throughput": batch_throughput * average_token_count,
    }


def generate_encode_inputs(length: int = 256, count: int = 100) -> list:
    """generate a set of sample inputs for benchmarking
    each word is sampled from words in the nltk corpus
    """
    # Load the words from the nltk corpus
    word_list = words.words()

    # Generate random input texts
    # tokens = [["a" for j in range(length)]  for i in range(count)]
    # input_texts = [" ".join(tokens[i]) for i in range(count)]
    tokens = np.random.choice(word_list, (count, length))
    input_texts = [" ".join(tokens[i]) for i in range(count)]

    return input_texts


def generate_decode_inputs(length: int = 256, count: int = 100) -> list:
    """generate a set of sample inputs for benchmarking"""
    # Generate random input tokens
    input_tokens = []
    for _ in range(count):
        tokens = np.random.randint(0, 4096, length)
        input_tokens.append(tokens)
    return input_tokens


def get_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("--method", type=str, default="encode")
    parser.add_argument("--model", type=str, default="deepseek-ai/DeepSeek-R1")
    parser.add_argument("--length", type=int, default=1024)
    parser.add_argument("--count", type=int, default=1024)
    parser.add_argument("--save_dir", type=str, default="./.cache/benchmark-tokenizer")
    parser.add_argument("--tag", type=str, default="")
    args = parser.parse_args()
    if not os.path.exists(args.save_dir):
        os.makedirs(args.save_dir)
    return args


def bench_encode_single(args):
    # Example Usage
    model = args.model  # Replace with your model
    input_texts = generate_encode_inputs(length=args.length, count=args.count)
    benchmark_encode(model, input_texts)


def bench_encode(
    args,
    length_list: list = [32, 64, 256, 512, 1024, 2048],
    count_list: list = [256, 512, 1024, 2048],
):
    model = args.model  # Replace with your model
    data = []
    for length in length_list:
        for count in count_list:
            input_texts = generate_encode_inputs(length=length, count=count)
            result = benchmark_encode(model, input_texts)
            data.append({"length": length, "count": count, **result})

    df = pd.DataFrame(data)
    tag = f"{args.model}_{args.tag}cpus".replace("/", "_")
    df.to_csv(os.path.join(args.save_dir, f"{tag}_encode.csv"), index=False)

    # plot the count vs throughput for each length
    sns.lineplot(data=df, x="count", y="throughput", hue="length")
    sns.lineplot(data=df, x="count", y="batch_throughput", hue="length", style="length")
    plt.xscale("log")
    plt.yscale("log")
    plt.xlabel("Number of Inputs")
    plt.ylabel("Throughput (Requests per Second)")
    plt.title("Throughput vs Number of Inputs")
    plt.savefig(os.path.join(args.save_dir, "encode", f"{tag}_encode.png"))
    plt.show()


def bench_decode_single(args):
    # Example Usage
    model = args.model  # Replace with your model
    input_tokens = generate_decode_inputs(length=args.length, count=args.count)
    benchmark_decode(model, input_tokens)


def bench_decode(
    args,
    length_list: list = [32, 64, 256, 512, 1024, 2048],
    count_list: list = [256, 512, 1024, 2048],
):
    model = args.model  # Replace with your model
    data = []
    for length in length_list:
        for count in count_list:
            input_tokens = generate_decode_inputs(length=length, count=count)
            result = benchmark_decode(model, input_tokens)
            data.append({"length": length, "count": count, **result})

    df = pd.DataFrame(data)
    tag = f"{args.model}_{args.tag}cpus".replace("/", "_")
    df.to_csv(os.path.join(args.save_dir, f"{tag}_decode.csv"), index=False)

    # plot the count vs throughput for each length
    sns.lineplot(data=df, x="count", y="throughput", hue="length")
    sns.lineplot(data=df, x="count", y="batch_throughput", hue="length", style="length")
    plt.xscale("log")
    plt.yscale("log")
    plt.xlabel("Number of Inputs")
    plt.ylabel("Throughput (Requests per Second)")
    plt.title("Throughput vs Number of Inputs")
    plt.savefig(os.path.join(args.save_dir, "decode", f"{tag}_decode.png"))
    plt.show()


if __name__ == "__main__":
    args = get_args()

    if args.method == "encode":
        bench_encode(args)
    elif args.method == "decode":
        bench_decode(args)
    elif args.method == "encode_single":
        bench_encode_single(args)
    elif args.method == "decode_single":
        bench_decode_single(args)
    else:
        print("Invalid method")
