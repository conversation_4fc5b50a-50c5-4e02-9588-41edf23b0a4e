import os, sys
import numpy as np
import json, time
import pandas as pd
import argparse
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from langchain_core.documents import Document
from langchain.text_splitter import TextSplitter
from langchain.text_splitter import RecursiveCharacterTextSplitter, Language
from langchain_text_splitters import SentenceTransformersTokenTextSplitter
from transformers import AutoTokenizer


def get_splitter(
    splitter_model: str, chunk_size: int, chunk_overlap: int, **kwargs
) -> TextSplitter:
    st = time.time()
    if splitter_model == "recursive-character":
        splitter = RecursiveCharacterTextSplitter(
            chunk_size=chunk_size, chunk_overlap=chunk_overlap
        )
    elif splitter_model == "recursive-character-embedding":
        embedding_model = kwargs.get("embedding_model")
        tokenizer = AutoTokenizer.from_pretrained(embedding_model)
        splitter = RecursiveCharacterTextSplitter.from_huggingface_tokenizer(
            tokenizer=tokenizer, chunk_size=chunk_size, chunk_overlap=chunk_overlap
        )
    elif splitter_model == "sentence-transformers":
        embedding_model = kwargs.get("embedding_model")
        splitter = SentenceTransformersTokenTextSplitter(
            model_name=embedding_model,
            tokens_per_chunk=chunk_size,
            chunk_overlap=chunk_overlap,
        )
    elif splitter_model == "recursive-character-tiktoken":
        splitter = RecursiveCharacterTextSplitter.from_tiktoken_encoder(
            chunk_size=chunk_size, chunk_overlap=chunk_overlap
        )
    elif splitter_model == "recursive-character-html":
        splitter = RecursiveCharacterTextSplitter.from_language(
            language=Language.HTML, chunk_size=chunk_size, chunk_overlap=chunk_overlap
        )
    elif splitter_model == "recursive-character-html-tiktoken":
        separators = RecursiveCharacterTextSplitter.get_separators_for_language(
            Language.HTML
        )
        splitter = RecursiveCharacterTextSplitter.from_tiktoken_encoder(
            chunk_size=chunk_size, chunk_overlap=chunk_overlap
        )
        splitter._separators = separators
        splitter._is_separator_regex = True
    else:
        raise NotImplementedError(f"Unsupported splitter model: {splitter_model}")
    et = time.time()
    print(f"initialization cost of splitter: {et - st}")
    return splitter


def load_docs(request_num: int) -> list[Document]:
    loading_persistency = "/mnt/data/rag-benchmark/cache/loading"
    dataset_path = "/mnt/data/rag-benchmark/data/ms_marco.txt"
    web_paths = []
    with open(dataset_path, "r", encoding="utf-8") as f:
        # lines = f.readline()
        # for line in lines[:request_num]:
        for i in range(request_num):
            line = f.readline()
            query_id, query, query_type, urls_str = line.strip().split("\t")
            urls = eval(urls_str)
            web_paths.extend(urls)

    docs = []
    for url in web_paths:
        dirs = url.replace("://", "/").split("/")
        loading_dir = os.path.join(loading_persistency, *dirs)
        if os.path.exists(loading_dir):
            # each file is a document
            _docs = []
            loading_files = sorted(
                [f for f in os.listdir(loading_dir) if f.endswith(".json")],
                key=lambda x: int(x.split(".")[0]),
            )
            try:
                for file in loading_files:
                    with open(
                        os.path.join(loading_dir, file), "r", encoding="utf-8"
                    ) as f:
                        doc = json.load(f)
                        _docs.append(Document.model_construct(**doc))
            except Exception as e:
                print(f"Error on {loading_dir}: {e}")
            docs.extend(_docs)
        else:
            print(f"Directory {loading_dir} does not exist")
    print(f"Loaded {len(docs)} documents")
    return docs


def get_args():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "-sm", "--splitter_model", type=str, default="recursive-character-embedding"
    )
    parser.add_argument("-cs", "--chunk_size", type=int, default=256)
    parser.add_argument("-co", "--chunk_overlap", type=int, default=30)
    parser.add_argument(
        "-em", "--embedding_model", type=str, default="BAAI/bge-large-en-v1.5"
    )
    parser.add_argument("-n", "--request_num", type=int, default=1)
    return parser.parse_args()


if __name__ == "__main__":
    args = get_args()

    st = time.time()
    splitter = get_splitter(
        splitter_model=args.splitter_model,
        chunk_size=args.chunk_size,
        chunk_overlap=args.chunk_overlap,
        embedding_model=args.embedding_model,
    )
    et = time.time()
    load_splitter_time = et - st
    print(f"load splitter cost: {load_splitter_time}")

    st = time.time()
    docs = load_docs(request_num=args.request_num)
    et = time.time()
    print(f"load docs cost: {et - st}")

    st = time.time()
    chunks = splitter.split_documents(docs)
    et = time.time()
    print(f"split documents cost: {et - st}")

    tokenizer = AutoTokenizer.from_pretrained(args.embedding_model)
    print(f"number of documents: {len(docs)}")
    print(
        f"number of characters in documents: {sum([len(doc.page_content) for doc in docs])}"
    )
    print(
        f"number of tokens in documents: {sum([len(tokenizer.tokenize(doc.page_content)) for doc in docs])}"
    )

    print(f"number of chunks: {len(chunks)}")
    print(
        f"number of characters in chunks: {sum([len(chunk.page_content) for chunk in chunks])}"
    )
    print(
        f"number of tokens in chunks: {sum([len(tokenizer.tokenize(chunk.page_content)) for chunk in chunks])}"
    )
