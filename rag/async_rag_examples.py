"""
Async RAG Examples

This file demonstrates how to use the async RAG functionality.
It shows the performance improvements and usage patterns for the async version.
"""

import asyncio
import time
from typing import List

# Import async RAG functions
from src.rag.async_rag import (
    # Core async functions
    indexing_async,
    naive_rag_chatting_async,
    multiquery_rag_chatting_async,
    dynamic_rag_chatting_async,
    naive_rag_async,
    multiquery_rag_async,
    dynamic_rag_async,
    contextual_rag_async,
    
    # Advanced engines
    get_advanced_async_rag_e2e_engine,
    get_advanced_async_rag_chatting_engine,
    get_async_vstore_manager,
    
    # Data models
    RAGE2EInput,
    RAGChattingInput,
    IndexingInput,
    VDBSettings,
    EmbeddingSettings,
    RetrieverSettings,
    GenerationSettings,
    SplitterSettings,
    
    # Utilities
    warm_up_async,
    init_settings,
)

# Import sync versions for comparison
from src.rag import (
    naive_rag,
    multiquery_rag,
    dynamic_rag,
    warm_up,
)


async def basic_async_rag_example():
    """Basic example of async RAG usage."""
    print("=== Basic Async RAG Example ===")
    
    # Initialize settings
    init_settings("config/settings.yml")
    
    # Prepare input
    rag_input = RAGE2EInput(
        query="What is machine learning?",
        uri="https://en.wikipedia.org/wiki/Machine_learning",
        type="web",
        clear_collection=True,
    )
    
    # Run async RAG
    start_time = time.time()
    result = await naive_rag_async(rag_input)
    end_time = time.time()
    
    print(f"Async RAG completed in {end_time - start_time:.2f} seconds")
    print(f"Answer: {result.answer[:200]}...")


async def parallel_rag_strategies_example():
    """Example of running multiple RAG strategies in parallel."""
    print("\n=== Parallel RAG Strategies Example ===")
    
    # Prepare input
    rag_input = RAGE2EInput(
        query="Explain the benefits of renewable energy",
        uri="https://en.wikipedia.org/wiki/Renewable_energy",
        type="web",
        clear_collection=True,
    )
    
    # Run multiple strategies in parallel
    start_time = time.time()
    
    tasks = [
        naive_rag_async(rag_input),
        multiquery_rag_async(rag_input),
        dynamic_rag_async(rag_input),
    ]
    
    results = await asyncio.gather(*tasks)
    end_time = time.time()
    
    print(f"Parallel execution of 3 strategies completed in {end_time - start_time:.2f} seconds")
    
    for i, result in enumerate(results):
        strategy_names = ["Naive", "MultiQuery", "Dynamic"]
        print(f"\n{strategy_names[i]} RAG Result:")
        print(f"Answer: {result.answer[:150]}...")


async def advanced_async_rag_example():
    """Example using advanced async RAG features."""
    print("\n=== Advanced Async RAG Example ===")
    
    # Get advanced engine
    engine = await get_advanced_async_rag_e2e_engine()
    
    # Prepare input
    rag_input = RAGE2EInput(
        query="Compare different types of neural networks",
        uri="https://en.wikipedia.org/wiki/Neural_network",
        type="web",
        clear_collection=True,
    )
    
    # Use adaptive RAG (automatically selects best strategy)
    start_time = time.time()
    result = await engine.adaptive_rag_e2e(rag_input)
    end_time = time.time()
    
    print(f"Adaptive RAG completed in {end_time - start_time:.2f} seconds")
    print(f"Answer: {result.answer[:200]}...")


async def batch_processing_example():
    """Example of batch processing multiple queries."""
    print("\n=== Batch Processing Example ===")
    
    # Prepare multiple queries
    queries = [
        "What is artificial intelligence?",
        "How does machine learning work?",
        "What are neural networks?",
        "Explain deep learning concepts",
    ]
    
    # Prepare RAG inputs
    rag_inputs = [
        RAGE2EInput(
            query=query,
            uri="https://en.wikipedia.org/wiki/Artificial_intelligence",
            type="web",
            clear_collection=False,  # Don't clear for batch processing
        )
        for query in queries
    ]
    
    # Process all queries concurrently
    start_time = time.time()
    
    # Use semaphore to limit concurrency
    semaphore = asyncio.Semaphore(2)  # Process 2 at a time
    
    async def process_with_semaphore(rag_input):
        async with semaphore:
            return await naive_rag_async(rag_input)
    
    tasks = [process_with_semaphore(rag_input) for rag_input in rag_inputs]
    results = await asyncio.gather(*tasks)
    
    end_time = time.time()
    
    print(f"Batch processing of {len(queries)} queries completed in {end_time - start_time:.2f} seconds")
    
    for i, (query, result) in enumerate(zip(queries, results)):
        print(f"\nQuery {i+1}: {query}")
        print(f"Answer: {result.answer[:100]}...")


async def performance_comparison_example():
    """Compare async vs sync performance."""
    print("\n=== Performance Comparison Example ===")
    
    # Prepare input
    rag_input = RAGE2EInput(
        query="What is quantum computing?",
        uri="https://en.wikipedia.org/wiki/Quantum_computing",
        type="web",
        clear_collection=True,
    )
    
    # Test async version
    print("Testing async version...")
    start_time = time.time()
    async_result = await naive_rag_async(rag_input)
    async_time = time.time() - start_time
    
    # Test sync version (would need to be run separately)
    print(f"Async version completed in {async_time:.2f} seconds")
    print(f"Answer length: {len(async_result.answer)} characters")


async def error_handling_example():
    """Example of error handling in async RAG."""
    print("\n=== Error Handling Example ===")
    
    # Prepare input with potentially problematic URI
    rag_input = RAGE2EInput(
        query="What is this about?",
        uri="https://nonexistent-website-12345.com",
        type="web",
        clear_collection=True,
    )
    
    try:
        result = await naive_rag_async(rag_input)
        print(f"Unexpected success: {result.answer[:100]}...")
    except Exception as e:
        print(f"Expected error occurred: {type(e).__name__}: {e}")
        
        # Fallback to a working URI
        print("Falling back to working URI...")
        rag_input.uri = "https://en.wikipedia.org/wiki/Python_(programming_language)"
        
        try:
            result = await naive_rag_async(rag_input)
            print(f"Fallback successful: {result.answer[:100]}...")
        except Exception as fallback_error:
            print(f"Fallback also failed: {fallback_error}")


async def vector_store_management_example():
    """Example of advanced vector store management."""
    print("\n=== Vector Store Management Example ===")
    
    # Get vector store manager
    vstore_manager = await get_async_vstore_manager()
    
    # Health check
    health_status = await vstore_manager.health_check(
        vdb_type="chromadb",
        vdb_uri="http://localhost:8800",
        collection_name="test_collection"
    )
    
    print(f"Vector store health: {health_status}")


async def conversational_rag_example():
    """Example of conversational RAG with history."""
    print("\n=== Conversational RAG Example ===")
    
    # Get advanced chatting engine
    engine = await get_advanced_async_rag_chatting_engine()
    
    # Prepare conversation history
    conversation_history = [
        {"user": "What is machine learning?", "assistant": "Machine learning is a subset of AI..."},
        {"user": "How does it differ from traditional programming?", "assistant": "Traditional programming..."},
    ]
    
    # Prepare current query
    chatting_input = RAGChattingInput(
        query="Can you give me some practical examples?",
        vdb_settings=VDBSettings.from_default_settings(),
        embedding_settings=EmbeddingSettings.from_default_settings(),
        retriever_settings=RetrieverSettings.from_default_settings(),
        generation_settings=GenerationSettings.from_default_settings(),
    )
    
    # Use conversational RAG
    result = await engine.conversational_rag_chatting(
        item=chatting_input,
        conversation_history=conversation_history,
    )
    
    print(f"Conversational RAG result: {result.answer[:200]}...")


async def main():
    """Run all examples."""
    print("Starting Async RAG Examples...")
    
    # Warm up the system
    print("Warming up async RAG system...")
    await warm_up_async(
        SplitterSettings.from_default_settings(),
        EmbeddingSettings.from_default_settings(),
        VDBSettings.from_default_settings(),
    )
    print("Warm-up completed!")
    
    # Run examples
    await basic_async_rag_example()
    await parallel_rag_strategies_example()
    await advanced_async_rag_example()
    await batch_processing_example()
    await performance_comparison_example()
    await error_handling_example()
    await vector_store_management_example()
    await conversational_rag_example()
    
    print("\n=== All examples completed! ===")


if __name__ == "__main__":
    # Run the examples
    asyncio.run(main())
