# RAG

This project include naive RAG, advanced RAG with Query Translation (Single Query to Multiple Query) and Reranking, and Dynamic RAG that will first assess the query and then decide whether to use the advanced RAG or not (if not, just answer with a pure LLM inference call).


## How to launch and test

Assume you have already launched the LLM server at `http://{llm_host}:{llm_port}`, and has update the llm settings in `config/settings.yml` accordingly.


``` bash
# install dependencies
pip install -r requirements.txt

# launch vector database
cd scripts
python launch_vdb.py
# By default, the chroma vector database will be launched at http://localhost:8800

# launch services
python launch_services.py

# test services
cd ../test
python test_services.py

```


## How to deploy (as a container)

Build docker image and start a container.

``` bash
# build an image
docker build -t rag:v1 .

# start a container
docker run -it --rm --gpus all --name rag-1 -p 20100:20100 -v ./.cache/config/settings.yml:/workspace/rag/config/settings.yml rag:v1
```

where the `./.cache/config/settings.yml` should be prepared by the user. The content of the file should be like:

``` yaml
...
openai_base_url: "http://*************:8000/v1"
...
vdb_type: "chromadb"
vdb_uri: "http://*************:8800"
...
```


## Benchmark

We support the following benchmarks dataset
- [wiki_qa](https://huggingface.co/datasets/microsoft/wiki_qa)
- [ms_marco](https://huggingface.co/datasets/microsoft/ms_marco)
- [natural_questions](https://huggingface.co/datasets/google-research-datasets/natural_questions)


First, we need to download the dataset and preprocess it.

``` bash
# prepare wiki_qa dataset
python prepare_dataset.py --dataset wiki_qa --out_dir /mnt/data/rag/datasets/wiki_qa
# request file will be at /mnt/data/rag/datasets/wiki_qa/wiki_qa.txt

# prepare ms_marco dataset
python prepare_dataset.py --dataset ms_marco --out_dir /mnt/data/rag/datasets/ms_marco
# request file will be at /mnt/data/rag/datasets/ms_marco/ms_marco.txt

# prepare natural_questions dataset
python prepare_dataset.py --dataset natural_questions --out_dir /mnt/data/rag/datasets/natural_questions
# request file will be at /mnt/data/rag/datasets/natural_questions/natural_questions.txt
# besides, there will be a directory /mnt/data/rag/datasets/natural_questions/NQ_htmls that contains the htmls for each question
```

Then, we can run the benchmark.

``` bash
# run benchmark
python benchmark.py --dataset wiki_qa --request_file /mnt/data/rag/datasets/wiki_qa/wiki_qa.txt
python benchmark.py --dataset ms_marco --request_file /mnt/data/rag/datasets/ms_marco/ms_marco.txt
python benchmark.py --dataset natural_questions --request_file /mnt/data/rag/datasets/natural_questions/natural_questions.txt
```

By default, benchmark will run the naive RAG. If you want to run the advanced RAG, you can specify the `--rag` argument.

``` bash
# run benchmark with advanced RAG
python benchmark.py --dataset wiki_qa --request_file /mnt/data/rag/datasets/wiki_qa/wiki_qa.txt --rag advanced
python benchmark.py --dataset ms_marco --request_file /mnt/data/rag/datasets/ms_marco/ms_marco.txt --rag advanced
python benchmark.py --dataset natural_questions --request_file /mnt/data/rag/datasets/natural_questions/natural_questions.txt --rag advanced

# run benchmark with dynamic RAG
python benchmark.py --dataset wiki_qa --request_file /mnt/data/rag/datasets/wiki_qa/wiki_qa.txt --rag dynamic
python benchmark.py --dataset ms_marco --request_file /mnt/data/rag/datasets/ms_marco/ms_marco.txt --rag dynamic
python benchmark.py --dataset natural_questions --request_file /mnt/data/rag/datasets/natural_questions/natural_questions.txt --rag dynamic

```

By default, we only run 100 requests for each dataset. If you want to run more requests, you can specify the `--request_num` argument. For example, to run 1000 requests for wiki_qa dataset, you can run the following command.

``` bash
# run benchmark with advanced RAG
python benchmark.py --dataset wiki_qa --request_file /mnt/data/rag/datasets/wiki_qa/wiki_qa.txt --rag advanced --request_num 1000
```
