FROM python:3.12

# download and install dependencies for fast-service
WORKDIR /workspace/fast-service
COPY .cache/fast-service/requirements.txt requirements.txt
RUN pip install -r requirements.txt

# install dependencies for rag
WORKDIR /workspace/rag
COPY requirements.txt requirements.txt
RUN pip install -r requirements.txt
RUN pip install grpcio grpcio-tools

# install fast-service
WORKDIR /workspace/fast-service
COPY .cache/fast-service/src src
COPY .cache/fast-service/pyproject.toml pyproject.toml
COPY .cache/fast-service/setup.py setup.py
RUN pip install -e .

# copy necessary code to docker image
WORKDIR /workspace/rag
COPY src src
COPY test test
COPY benchmark benchmark
COPY scripts scripts
COPY config config

# add naive_rag to PYTHONPATH
ENV PYTHONPATH=/workspace/rag/src

# set workdir and default command
WORKDIR /workspace/rag/scripts
<PERSON><PERSON> ["python", "launch_services.py"]