"""
Test script for async RAG implementation.

This script provides basic tests to verify that the async RAG implementation
works correctly and can be imported without errors.
"""

import asyncio
import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_imports():
    """Test that all async RAG modules can be imported."""
    print("Testing imports...")

    try:
        # Test basic imports
        from rag.async_utils import (
            async_run_in_executor,
            AsyncFileHandler,
            AsyncHTTPClient,
            AsyncBatchProcessor,
        )
        print("✓ async_utils imported successfully")

        # Test vector database imports
        from vectordb.async_base import AsyncBaseVectorDBClient
        from vectordb.async_chromadb import AsyncChromaVectorDBClient
        from vectordb.async_milvusdb import AsyncMilvusVectorDBClient
        print("✓ async vector database clients imported successfully")

        # Test core async modules
        from rag.async_loading import load_docs_async
        from rag.async_chunking import split_docs_async
        from rag.async_embedding import embed_documents_async, embed_query_async
        from rag.async_vstore import vdb_store_async, vdb_search_async
        from rag.async_retrieval import retrieval_async
        from rag.async_generation import generation_async
        print("✓ async core modules imported successfully")

        # Test high-level async modules
        from rag.async_rag_indexing import indexing_async
        from rag.async_rag_chatting import naive_rag_chatting_async
        from rag.async_rag_e2e import naive_rag_async
        print("✓ async high-level modules imported successfully")

        # Test main async RAG module
        from rag.async_rag import (
            naive_rag_async,
            multiquery_rag_async,
            dynamic_rag_async,
            contextual_rag_async,
        )
        print("✓ main async_rag module imported successfully")

        # Test data models
        from rag.shared import (
            RAGE2EInput,
            RAGE2EOutput,
            RAGChattingInput,
            IndexingInput,
        )
        print("✓ shared data models imported successfully")

        print("✅ All imports successful!")
        return True

    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error during import: {e}")
        return False


async def test_basic_functionality():
    """Test basic async functionality without external dependencies."""
    print("\nTesting basic async functionality...")

    try:
        # Test async utilities
        from rag.async_utils import AsyncFileHandler, AsyncBatchProcessor

        # Test file handler (without actual file operations)
        file_handler = AsyncFileHandler()
        print("✓ AsyncFileHandler created successfully")

        # Test batch processor
        batch_processor = AsyncBatchProcessor(batch_size=2, max_concurrency=2)

        # Simple test function
        async def test_process_func(items):
            await asyncio.sleep(0.1)  # Simulate processing
            return [f"processed_{item}" for item in items]

        # Test batch processing
        test_items = ["item1", "item2", "item3", "item4", "item5"]
        results = await batch_processor.process_batches(test_items, test_process_func)

        expected = ["processed_item1", "processed_item2", "processed_item3", "processed_item4", "processed_item5"]
        assert results == expected, f"Expected {expected}, got {results}"
        print("✓ AsyncBatchProcessor works correctly")

        # Test async_run_in_executor
        from rag.async_utils import async_run_in_executor

        def sync_function(x, y):
            return x + y

        result = await async_run_in_executor(sync_function, 5, 3)
        assert result == 8, f"Expected 8, got {result}"
        print("✓ async_run_in_executor works correctly")

        print("✅ Basic async functionality tests passed!")
        return True

    except Exception as e:
        print(f"❌ Basic functionality test failed: {e}")
        return False


async def test_data_models():
    """Test that data models work correctly."""
    print("\nTesting data models...")

    try:
        from rag.shared import (
            RAGE2EInput,
            RAGChattingInput,
            IndexingInput,
            VDBSettings,
            EmbeddingSettings,
        )

        # Test RAGE2EInput
        rag_input = RAGE2EInput(
            query="Test query",
            uri="test://example.com",
            type="test",
            clear_collection=True,
        )
        assert rag_input.query == "Test query"
        print("✓ RAGE2EInput created successfully")

        # Test settings
        vdb_settings = VDBSettings.from_default_settings()
        embedding_settings = EmbeddingSettings.from_default_settings()
        print("✓ Settings created successfully")

        # Test RAGChattingInput
        chatting_input = RAGChattingInput(
            query="Test chatting query",
            vdb_settings=vdb_settings,
            embedding_settings=embedding_settings,
        )
        assert chatting_input.query == "Test chatting query"
        print("✓ RAGChattingInput created successfully")

        print("✅ Data model tests passed!")
        return True

    except Exception as e:
        print(f"❌ Data model test failed: {e}")
        return False


async def test_engine_initialization():
    """Test that async engines can be initialized."""
    print("\nTesting engine initialization...")

    try:
        # Test getting async engines (without actually using them)
        from rag.async_rag import (
            get_async_embedding_engine,
            get_async_vstore_manager,
            get_advanced_async_rag_e2e_engine,
        )

        # These should not fail to import and call
        embedding_engine = await get_async_embedding_engine()
        print("✓ AsyncEmbeddingEngine initialized")

        vstore_manager = await get_async_vstore_manager()
        print("✓ AsyncVectorStoreManager initialized")

        rag_engine = await get_advanced_async_rag_e2e_engine()
        print("✓ AdvancedAsyncRAGE2EEngine initialized")

        print("✅ Engine initialization tests passed!")
        return True

    except Exception as e:
        print(f"❌ Engine initialization test failed: {e}")
        return False


async def test_native_async_optimizations():
    """Test that native async optimizations are working."""
    print("\nTesting native async optimizations...")

    try:
        # Test LLM async detection
        from rag.async_utils import get_llm
        llm = get_llm()

        has_ainvoke = hasattr(llm, 'ainvoke')
        print(f"✓ LLM async support detected: {has_ainvoke}")

        # Test embedding async detection
        from rag.async_utils import get_embedding_fn
        from rag.settings import get_settings

        settings = get_settings()
        embedding_fn = get_embedding_fn(
            embedding_model=settings.embedding_model,
            embedding_device=settings.embedding_device,
        )

        has_aembed = hasattr(embedding_fn, 'aembed_query') or hasattr(embedding_fn, 'aembed_documents')
        print(f"✓ Embedding async support detected: {has_aembed}")

        # Test vector DB async detection
        try:
            import chromadb
            has_async_chroma = hasattr(chromadb, 'AsyncHttpClient')
            print(f"✓ ChromaDB async support detected: {has_async_chroma}")
        except ImportError:
            print("✓ ChromaDB not available (optional)")

        try:
            from pymilvus import AsyncMilvusClient
            has_async_milvus = True
            print(f"✓ Milvus async support detected: {has_async_milvus}")
        except ImportError:
            print("✓ Milvus async client not available (using fallback)")

        print("✅ Native async optimization tests passed!")
        return True

    except Exception as e:
        print(f"❌ Native async optimization test failed: {e}")
        return False


async def test_async_patterns():
    """Test async patterns and concurrency."""
    print("\nTesting async patterns...")

    try:
        from rag.async_utils import async_gather_with_concurrency

        # Test concurrent execution with limits
        async def test_task(delay, value):
            await asyncio.sleep(delay)
            return value * 2

        tasks = [
            test_task(0.1, 1),
            test_task(0.1, 2),
            test_task(0.1, 3),
            test_task(0.1, 4),
        ]

        results = await async_gather_with_concurrency(tasks, max_concurrency=2)
        expected = [2, 4, 6, 8]
        assert results == expected, f"Expected {expected}, got {results}"
        print("✓ async_gather_with_concurrency works correctly")

        print("✅ Async pattern tests passed!")
        return True

    except Exception as e:
        print(f"❌ Async pattern test failed: {e}")
        return False


def test_sync_compatibility():
    """Test that sync versions still work alongside async versions."""
    print("\nTesting sync compatibility...")

    try:
        # Import sync versions
        from rag.shared import RAGE2EInput
        from rag.settings import get_settings, init_settings

        # Test that sync imports still work
        print("✓ Sync modules still importable")

        # Test settings
        settings = get_settings()
        print("✓ Settings accessible")

        # Test data models work the same
        rag_input = RAGE2EInput(
            query="Test query",
            uri="test://example.com",
            type="test",
        )
        print("✓ Data models compatible")

        print("✅ Sync compatibility tests passed!")
        return True

    except Exception as e:
        print(f"❌ Sync compatibility test failed: {e}")
        return False


async def run_all_tests():
    """Run all tests."""
    print("🧪 Starting Async RAG Tests")
    print("=" * 50)

    test_results = []

    # Test imports
    test_results.append(test_imports())

    # Test sync compatibility
    test_results.append(test_sync_compatibility())

    # Test basic async functionality
    test_results.append(await test_basic_functionality())

    # Test data models
    test_results.append(await test_data_models())

    # Test engine initialization
    test_results.append(await test_engine_initialization())

    # Test native async optimizations
    test_results.append(await test_native_async_optimizations())

    # Test async patterns
    test_results.append(await test_async_patterns())

    # Summary
    print("\n" + "=" * 50)
    print("🧪 Test Summary")
    print("=" * 50)

    passed = sum(test_results)
    total = len(test_results)

    if passed == total:
        print(f"✅ All {total} tests passed!")
        print("\n🎉 Async RAG implementation is working correctly!")
        return True
    else:
        print(f"❌ {total - passed} out of {total} tests failed!")
        print("\n⚠️  Some issues need to be resolved.")
        return False


if __name__ == "__main__":
    # Run the tests
    success = asyncio.run(run_all_tests())

    if success:
        print("\n✨ You can now use the async RAG implementation!")
        print("📖 See async_rag_examples.py for usage examples.")
        print("📚 See ASYNC_RAG_README.md for detailed documentation.")
        print("🚀 Run test_async_loading_optimizations.py for loading-specific tests.")
        print("🔄 Run test_async_http_reranking.py for HTTP reranking tests.")
    else:
        print("\n🔧 Please check the error messages above and fix any issues.")

    sys.exit(0 if success else 1)
