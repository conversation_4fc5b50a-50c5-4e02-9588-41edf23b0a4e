import requests
import os
import json
from typing import BinaryIO
import argparse
from fast_service import FastServiceConfig, FastServiceFile, RequestContext
from rag import rag_fsm
from rag import IndexingInput, IndexingOutput
from rag import RAGChattingInput, RAGChattingOutput
from rag import CollectionClearingInput
from rag import RAGE2EInput, RAGE2EOutput
from rag import indexing
from rag import naive_rag_chatting
from rag import multiquery_rag_chatting
from rag import dynamic_rag_chatting
from rag import clear_collection
from rag import naive_rag
from rag import advanced_rag
from rag import dynamic_rag
from rag.settings import (
    SplitterSettings,
    VDBSettings,
    EmbeddingSettings,
    RetrieverSettings,
    GenerationSettings,
)


def test_e2e():
    splitter_settings = SplitterSettings.from_default_settings()
    vdb_settings = VDBSettings.from_default_settings()
    embedding_settings = EmbeddingSettings.from_default_settings()
    retriever_settings = RetrieverSettings.from_default_settings()
    generation_settings = GenerationSettings.from_default_settings()

    test_item = RAGE2EInput(
        query="What is Task Decomposition?",
        uri="https://lilianweng.github.io/posts/2023-06-23-agent/",
        type="web",
        splitter_settings=splitter_settings,
        vdb_settings=vdb_settings,
        embedding_settings=embedding_settings,
        retriever_settings=retriever_settings,
        generation_settings=generation_settings,
    )
    print(f"Test item: {test_item}")

    print(f"---------- Test naive rag ----------")
    test_answer = naive_rag(item=test_item).answer
    print(f"Test answer: {test_answer}")

    print(f"---------- Test advanced rag ----------")
    test_answer = advanced_rag(item=test_item).answer
    print(f"Test answer: {test_answer}")

    print(f"---------- Test dynamic rag ----------")
    test_answer = dynamic_rag(item=test_item).answer
    print(f"Test answer: {test_answer}")


def get_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("-n", "--collection_name", default="test")
    parser.add_argument("-c", "--config", default="../config/rag-client.yml")

    args = parser.parse_args()
    return args


if __name__ == "__main__":
    args = get_args()
    config = FastServiceConfig.load_from_file(args.config)
    rag_fsm.setup_client_mode(config)

    test_e2e()
