# Async HTTP Reranking Support

## Overview

The async post-retrieval module now supports HTTP-based reranking engines, similar to the sync version. This allows you to use custom reranking services or local reranking servers with the async RAG implementation.

## 🚀 **Features**

### **1. HTTP Engine Support**
- **Custom reranking servers** via HTTP endpoints
- **Automatic URL handling** with `/v1` suffix removal
- **Graceful fallback** to simple reranking if HTTP fails
- **Compatible with Cohere API format**

### **2. Engine Types Supported**
- **`cohere`**: Official Cohere reranking service
- **`http://...`**: Custom HTTP-based reranking servers
- **`https://...`**: Secure HTTP-based reranking servers

### **3. URL Format Handling**
- **Automatic cleanup** of `/v1` suffixes
- **Flexible URL formats** (with or without trailing slashes)
- **Support for both HTTP and HTTPS**

## 🔧 **Implementation Details**

### **Engine Detection Pattern**
```python
async def rerank_documents(self, query, documents, model, engine, top_k):
    """Rerank documents with engine-specific handling."""
    if engine == "cohere":
        return await self._cohere_rerank(query, documents, model, top_k)
    elif engine and engine.startswith("http"):
        return await self._http_rerank(query, documents, model, engine, top_k)
    else:
        return await self._simple_rerank(query, documents, top_k)
```

### **HTTP Reranking Implementation**
```python
async def _http_rerank(self, query, documents, model, engine, top_k):
    """Rerank using HTTP-based reranking service."""
    def _sync_http_rerank():
        from .async_utils import get_async_reranker
        
        reranker = get_async_reranker(model=model, engine=engine)
        reranked_docs = reranker.compress_documents(
            documents=documents,
            query=query
        )
        
        if top_k:
            return reranked_docs[:top_k]
        return reranked_docs
    
    return await async_run_in_executor(_sync_http_rerank)
```

### **Utility Function**
```python
@lru_cache
def get_async_reranker(model: str, engine: str = "cohere"):
    """Get async reranker instance (cached)."""
    if engine == "cohere":
        return CohereRerank(model=model)
    elif engine.startswith("http"):
        # Clean up engine URL (remove /v1 suffix if present)
        base_url = engine
        if base_url.endswith("/v1"):
            base_url = base_url[:-3]
        
        # Create Cohere client with custom base URL
        cohere_client = cohere.Client(api_key="EMPTY", base_url=base_url)
        return CohereRerank(client=cohere_client, model=model, top_n=None)
    else:
        raise NotImplementedError(f"Unsupported reranking engine: {engine}")
```

## 🚀 **Usage Examples**

### **1. Basic HTTP Reranking**
```python
from rag.async_post_retrieval import AsyncReranker
from langchain_core.documents import Document

# Create reranker
reranker = AsyncReranker()

# Test documents
docs = [
    Document(page_content="Python programming tutorial"),
    Document(page_content="Machine learning with Python"),
    Document(page_content="Data analysis techniques"),
]

# Rerank with HTTP server
reranked_docs = await reranker.rerank_documents(
    query="Python programming",
    documents=docs,
    model="rerank-english-v2.0",
    engine="http://localhost:8080",
    top_k=2
)
```

### **2. Post-Retrieval Integration**
```python
from rag.async_post_retrieval import post_retrieval_async
from rag.shared import PostRetrievalInput, RetrievalOutput

# Create post-retrieval input with HTTP reranking
post_retrieval_input = PostRetrievalInput(
    retrieved=RetrievalOutput(docs=docs),
    reranking_args={
        "query": "Python programming",
        "model": "rerank-english-v2.0",
        "engine": "http://localhost:8080",
        "top_k": 3
    }
)

# Execute post-retrieval with HTTP reranking
result = await post_retrieval_async(post_retrieval_input, context)
```

### **3. Different Engine Types**
```python
# Cohere reranking
await reranker.rerank_documents(
    query="query",
    documents=docs,
    model="rerank-english-v2.0",
    engine="cohere"
)

# Local HTTP server
await reranker.rerank_documents(
    query="query",
    documents=docs,
    model="rerank-english-v2.0",
    engine="http://localhost:8080"
)

# Remote HTTPS API
await reranker.rerank_documents(
    query="query",
    documents=docs,
    model="rerank-english-v2.0",
    engine="https://api.example.com"
)

# With /v1 suffix (automatically cleaned)
await reranker.rerank_documents(
    query="query",
    documents=docs,
    model="rerank-english-v2.0",
    engine="http://localhost:8080/v1"
)
```

## 🔧 **Server Setup**

### **Local Reranking Server**
To use HTTP reranking, you need a server that implements the Cohere reranking API format:

```bash
# Example: Start a local reranking server
python -m reranking_server --port 8080 --model rerank-english-v2.0
```

### **API Endpoint Format**
Your HTTP reranking server should accept POST requests to `/rerank` with:

```json
{
    "model": "rerank-english-v2.0",
    "query": "search query",
    "documents": [
        {"text": "document 1 content"},
        {"text": "document 2 content"}
    ],
    "top_n": 5
}
```

And return:
```json
{
    "results": [
        {
            "index": 0,
            "relevance_score": 0.95,
            "document": {"text": "document 1 content"}
        },
        {
            "index": 1,
            "relevance_score": 0.87,
            "document": {"text": "document 2 content"}
        }
    ]
}
```

## 🧪 **Testing**

### **Run HTTP Reranking Tests**
```bash
cd rag
python test_async_http_reranking.py
```

### **Test Coverage**
- ✅ **HTTP engine detection**
- ✅ **URL format handling**
- ✅ **Graceful fallback mechanisms**
- ✅ **Post-retrieval integration**
- ✅ **Error handling**

### **Expected Test Output**
```
✓ HTTP reranking completed with 2 documents
✓ Cohere reranking code path tested successfully
✓ Simple reranking completed with 2 documents
✓ HTTP reranker created successfully
✓ URL format handled correctly: http://localhost:8080
✓ Post-retrieval with HTTP reranking completed: 2 docs
✅ All tests passed!
```

## 🔍 **Error Handling**

### **Graceful Fallback**
The system provides multiple fallback layers:

1. **HTTP reranking fails** → Falls back to simple reranking
2. **Cohere reranking fails** → Falls back to simple reranking
3. **No model specified** → Uses simple reranking

### **Error Logging**
```python
# Detailed error logging for debugging
get_logger().warning(f"HTTP reranking failed: {e}. Using simple rerank.")
get_logger().debug(f"Using HTTP reranking with engine: {engine}")
```

## 📋 **Configuration**

### **Engine Configuration**
```python
# In your settings or configuration
RERANKING_CONFIG = {
    "model": "rerank-english-v2.0",
    "engine": "http://localhost:8080",  # or "cohere"
    "top_k": 5
}
```

### **Environment Variables**
```bash
# For Cohere API
export COHERE_API_KEY="your-api-key"

# For custom HTTP server
export RERANKING_SERVER_URL="http://localhost:8080"
```

## 🎯 **Benefits**

### **Flexibility**
- **Use any reranking model** via HTTP API
- **Local or remote** reranking servers
- **Custom reranking logic** implementation

### **Performance**
- **Cached reranker instances** for efficiency
- **Async execution** with executor fallback
- **Graceful error handling** without blocking

### **Compatibility**
- **Same interface** as sync version
- **Drop-in replacement** for existing code
- **Backward compatible** with Cohere API

## 🔮 **Future Enhancements**

### **Planned Features**
1. **Native async HTTP client** for reranking requests
2. **Connection pooling** for HTTP reranking servers
3. **Batch reranking** for multiple queries
4. **Custom authentication** methods

### **Server Integrations**
- **Sentence Transformers** reranking server
- **BGE reranker** HTTP wrapper
- **Custom transformer** model servers

## ✅ **Conclusion**

The async HTTP reranking support provides:

- **Flexible reranking options** with HTTP-based servers
- **Seamless integration** with existing async post-retrieval
- **Robust error handling** with graceful fallbacks
- **Production-ready** implementation with comprehensive testing

**The async RAG implementation now supports HTTP-based reranking engines just like the sync version!**

## 📚 **Related Documentation**

- **Async Post-Retrieval**: See `async_post_retrieval.py` for full implementation
- **Sync Reranking**: See `utils.py` for sync version reference
- **Testing**: See `test_async_http_reranking.py` for usage examples
- **Configuration**: See settings documentation for reranking configuration
