#!/usr/bin/env python3
"""
Test script to verify the bug fix for the AttributeError in async_loading.py.

The bug was: AttributeError: 'list' object has no attribute 'replace'
This occurred when url was a list but the code tried to call url.replace().
"""

import asyncio
from src.rag.async_loading import AsyncLoadingEngine
from src.rag.shared import DocLoadingInput


async def test_bug_fix():
    """Test that the bug fix works for both single URLs and lists of URLs."""
    engine = AsyncLoadingEngine()
    
    print("Testing the bug fix for async_loading.py...")
    print("=" * 50)
    
    # Test 1: Single URL (string) - this should work
    print("Test 1: Single URL (string)")
    try:
        # This simulates the original working case
        docs = await engine.execute(
            type="web", 
            uri="https://httpbin.org/html"
        )
        print(f"✓ Single URL test passed: loaded {len(docs)} docs")
    except Exception as e:
        print(f"✗ Single URL test failed: {e}")
    
    # Test 2: Multiple URLs (list) - this was causing the bug
    print("\nTest 2: Multiple URLs (list) - this was the bug case")
    try:
        # This simulates the case that was causing the AttributeError
        docs = await engine.execute(
            type="web", 
            uri=["https://httpbin.org/html", "https://httpbin.org/json"]
        )
        print(f"✓ Multiple URLs test passed: loaded {len(docs)} docs")
        print("✓ Bug fix successful! No more AttributeError on list.replace()")
    except AttributeError as e:
        if "'list' object has no attribute 'replace'" in str(e):
            print(f"✗ Bug still exists: {e}")
            return False
        else:
            print(f"✗ Different AttributeError: {e}")
            return False
    except Exception as e:
        print(f"✗ Multiple URLs test failed with different error: {e}")
    
    # Test 3: Test via DocLoadingInput (the actual usage pattern)
    print("\nTest 3: Via DocLoadingInput (actual usage pattern)")
    try:
        from src.rag.async_loading import load_docs_async
        from fast_service import RequestContext
        
        # Test with list of URLs via DocLoadingInput
        item = DocLoadingInput(
            type="web", 
            uri=["https://httpbin.org/html", "https://httpbin.org/json"]
        )
        context = RequestContext(request_id="test-bug-fix")
        
        result = await load_docs_async(item, context)
        print(f"✓ DocLoadingInput test passed: loaded {len(result.docs)} docs")
        print("✓ Complete integration test successful!")
        
    except AttributeError as e:
        if "'list' object has no attribute 'replace'" in str(e):
            print(f"✗ Bug still exists in integration: {e}")
            return False
        else:
            print(f"✗ Different AttributeError in integration: {e}")
            return False
    except Exception as e:
        print(f"✗ Integration test failed with different error: {e}")
    
    print("\n" + "=" * 50)
    print("All tests completed successfully! Bug fix verified.")
    return True


if __name__ == "__main__":
    success = asyncio.run(test_bug_fix())
    if success:
        print("\n🎉 Bug fix verification: PASSED")
    else:
        print("\n❌ Bug fix verification: FAILED")
        exit(1)
